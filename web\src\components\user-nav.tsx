"use client";

import Link from "next/link";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/hooks/use-auth";
import type { User } from "@/lib/types";
import { User as UserIcon, Settings, LogOut, Shield } from "lucide-react";

export function UserNav({ user }: { user: User }) {
  const { logout } = useAuth();
  const initials = user.name.split(' ').map(n => n[0]).join('');

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full hover:bg-muted/50 transition-all duration-200 group">
          <div className="relative">
            <Avatar className="h-10 w-10 ring-2 ring-border/50 group-hover:ring-primary/30 transition-all duration-200">
              <AvatarImage src={user.avatarUrl} alt={user.name} />
              <AvatarFallback className="bg-gradient-to-br from-primary to-primary/70 text-white font-semibold">
                {initials}
              </AvatarFallback>
            </Avatar>
            {/* Online indicator */}
            <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-success rounded-full border-2 border-background" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="mobile-dropdown bg-card/95 backdrop-blur-xl border-border/50" align="end" forceMount>
        <DropdownMenuLabel className="font-normal p-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 lg:h-12 lg:w-12 shrink-0">
              <AvatarImage src={user.avatarUrl} alt={user.name} />
              <AvatarFallback className="bg-gradient-to-br from-primary to-primary/70 text-white font-semibold">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col space-y-1 min-w-0 flex-1">
              <p className="text-sm font-semibold leading-none text-foreground mobile-truncate">{user.name}</p>
              <p className="text-xs leading-none text-muted-foreground mobile-truncate">
                {user.email}
              </p>
              <div className="flex items-center gap-1 mt-1">
                <Shield className="h-3 w-3 text-primary shrink-0" />
                <span className="text-xs font-medium text-primary capitalize mobile-truncate">{user.role}</span>
              </div>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-border/50" />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href="/settings" className="flex items-center gap-3">
              <UserIcon className="h-4 w-4 shrink-0" />
              <span className="mobile-text-sm">Profile</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href="/settings" className="flex items-center gap-3">
              <Settings className="h-4 w-4 shrink-0" />
              <span className="mobile-text-sm">Settings</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator className="bg-border/50" />
        <DropdownMenuItem 
          onClick={() => logout()} 
          className="cursor-pointer text-destructive focus:text-destructive"
        >
          <LogOut className="mr-3 h-4 w-4 shrink-0" />
          <span className="mobile-text-sm">Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
