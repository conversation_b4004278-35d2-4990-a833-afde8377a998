{"version": 3, "file": "Time.js", "sourceRoot": "", "sources": ["../../../src/common/Time.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Defines High-Resolution Time.\n *\n * The first number, HrTime[0], is UNIX Epoch time in seconds since 00:00:00 UTC on 1 January 1970.\n * The second number, HrTime[1], represents the partial second elapsed since Unix Epoch time represented by first number in nanoseconds.\n * For example, 2021-01-01T12:30:10.150Z in UNIX Epoch time in milliseconds is represented as **********150.\n * The first number is calculated by converting and truncating the Epoch time in milliseconds to seconds:\n * HrTime[0] = Math.trunc(**********150 / 1000) = **********.\n * The second number is calculated by converting the digits after the decimal point of the subtraction, (**********150 / 1000) - HrTime[0], to nanoseconds:\n * HrTime[1] = Number((**********.150 - HrTime[0]).toFixed(9)) * 1e9 = 150000000.\n * This is represented in HrTime format as [**********, 150000000].\n */\nexport type HrTime = [number, number];\n\n/**\n * Defines TimeInput.\n *\n * hrtime, epoch milliseconds, performance.now() or Date\n */\nexport type TimeInput = HrTime | number | Date;\n"]}