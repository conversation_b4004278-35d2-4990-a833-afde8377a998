import { useState, useCallback } from 'react'

export interface DynamicIslandProps {
  title: string
  description?: string
  variant?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  onClose?: () => void
  id: string
}

interface DynamicIslandState extends DynamicIslandProps {
  id: string
}

export function useDynamicIsland() {
  const [toasts, setToasts] = useState<DynamicIslandState[]>([])

  const toast = useCallback((props: Omit<DynamicIslandProps, 'onClose' | 'id'>) => {
    const id = Math.random().toString(36).substring(2, 11)

    // Dismiss any existing toast first (only one at a time for Dynamic Island)
    setToasts([])

    const newToast: DynamicIslandState = {
      ...props,
      id,
      onClose: () => {
        setToasts(prev => prev.filter(t => t.id !== id))
      }
    }

    // Small delay to ensure clean transition
    setTimeout(() => {
      setToasts([newToast])
    }, 100)

  }, [])

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }, [])

  const dismissAll = useCallback(() => {
    setToasts([])
  }, [])

  return {
    toast,
    toasts,
    dismissToast,
    dismissAll
  }
}

// Also export as default for easier importing
export default useDynamicIsland
