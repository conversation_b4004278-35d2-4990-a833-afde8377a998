import { useState, useEffect } from 'react'
import { getLogoPath } from '../assets'
import { useAuth } from '../hooks/use-auth'

interface SplashScreenProps {
  onComplete?: () => void
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [logoError, setLogoError] = useState(false)
  const { isLoading: authLoading } = useAuth()

  // Debug auth state
  useEffect(() => {
    console.log('🔄 SplashScreen - Auth loading state:', authLoading)
  }, [authLoading])

  const loadingSteps = [
    'Initializing system...',
    'Loading school data...',
    'Checking authentication...',
    'Preparing interface...',
    'Almost ready...'
  ]

  useEffect(() => {
    let progressInterval: NodeJS.Timeout
    let stepInterval: NodeJS.Timeout
    let completionTimeout: NodeJS.Timeout
    let hasCompleted = false

    const startLoading = () => {
      // Step animation - moves through steps based on auth state
      stepInterval = setInterval(() => {
        setCurrentStep(prev => {
          if (authLoading && prev < 2) {
            // Move to auth step
            return prev + 1
          } else if (!authLoading && prev >= 2 && prev < loadingSteps.length - 1) {
            // Auth complete, move through final steps
            return prev + 1
          }
          return prev
        })
      }, 800)

      // Progress animation - tied to actual auth state
      progressInterval = setInterval(() => {
        setProgress(prev => {
          if (hasCompleted) return 100 // Always return 100 if completed

          if (authLoading) {
            // While auth is loading, progress slowly to 85%
            if (prev < 85) {
              return Math.min(prev + 3, 85) // Ensure we don't exceed 85%
            } else {
              return 85 // Stay at 85% while waiting for auth
            }
          } else {
            // Auth complete, finish loading quickly
            const newProgress = Math.min(prev + 10, 100) // Ensure we don't exceed 100%

            if (newProgress >= 100 && !hasCompleted) {
              hasCompleted = true
              clearInterval(progressInterval)
              clearInterval(stepInterval)
              completionTimeout = setTimeout(() => {
                if (onComplete) {
                  onComplete()
                }
              }, 800)
              return 100
            }
            return newProgress
          }
        })
      }, 120)
    }

    startLoading()

    return () => {
      clearInterval(progressInterval)
      clearInterval(stepInterval)
      clearTimeout(completionTimeout)
    }
  }, [onComplete, authLoading])

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center z-50">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-ping"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center space-y-8 max-w-md mx-auto px-6">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="w-24 h-24 rounded-3xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-300 bg-white/10 flex items-center justify-center">
              {!logoError ? (
                <img
                  src={getLogoPath()}
                  alt="Maggie Preparatory School Logo"
                  className="w-full h-full object-cover"
                  onError={() => {
                    console.warn('SplashScreen logo failed to load:', getLogoPath())
                    setLogoError(true)
                  }}
                  onLoad={() => {
                    console.log('SplashScreen logo loaded successfully:', getLogoPath())
                  }}
                />
              ) : (
                // Fallback when logo fails to load
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center text-white text-xl font-bold">
                  MP
                </div>
              )}
            </div>
            {/* Animated Ring */}
            <div className="absolute inset-0 w-24 h-24 border-4 border-white/30 rounded-3xl animate-spin"></div>
          </div>
        </div>

        {/* School Name */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-white mb-2 animate-fade-in">
            Maggie Preparatory School
          </h1>
          <p className="text-xl text-blue-200 animate-fade-in delay-300">
            Management System
          </p>
        </div>

        {/* Loading Progress */}
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>

          {/* Loading Text */}
          <div className="h-6">
            <p className="text-blue-100 text-sm animate-pulse">
              {loadingSteps[currentStep]}
            </p>
          </div>

          {/* Progress Percentage */}
          <p className="text-white/70 text-xs font-mono">
            {Math.round(progress)}%
          </p>
        </div>

        {/* Version Info */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <p className="text-white/50 text-xs">
            Version 1.0.0
          </p>
        </div>
      </div>


    </div>
  )
}
