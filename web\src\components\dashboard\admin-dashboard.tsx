
import Link from "next/link";
import { ArrowUpRight, Users, BookOpen, UserPlus, Send, Trash2, TrendingUp, GraduationCap, <PERSON>r<PERSON><PERSON>ck, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";

import { useEffect, useState } from "react";
import { collection, getDocs, limit, orderBy, query, addDoc, serverTimestamp, deleteDoc, doc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Student, Notification } from "@/lib/types";
import { useAuth } from "@/hooks/use-auth";
import { Textarea } from "../ui/textarea";
import { resetAllPaymentRecords } from "@/utils/student-cleanup";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import { Badge } from "../ui/badge";


export default function AdminDashboard() {
    const { user } = useAuth();
    const { toast } = useFloatingToast();
    const [stats, setStats] = useState({ totalStudents: 0, totalTeachers: 0, studentsWithResults: 0 });
    const [recentStudents, setRecentStudents] = useState<Student[]>([]);
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [isSending, setIsSending] = useState(false);

    const fetchNotifications = async () => {
        const notificationsQuery = query(collection(db, 'notifications'), orderBy('createdAt', 'desc'));
        const querySnapshot = await getDocs(notificationsQuery);
        setNotifications(querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Notification)));
    };

    useEffect(() => {
        const fetchData = async () => {
            // Fetch students
            const studentsCollection = collection(db, 'students');
            const studentsSnapshot = await getDocs(studentsCollection);
            const studentsData = studentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Student));
            const totalStudents = studentsSnapshot.size;

            // Fetch recent students
            const recentStudentsQuery = query(studentsCollection, orderBy('dateAdded', 'desc'), limit(5));
            const recentStudentsSnapshot = await getDocs(recentStudentsQuery);
            const recentStudentsData = recentStudentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Student));
            setRecentStudents(recentStudentsData);

            // Fetch users for teacher count
            const usersCollection = collection(db, 'users');
            const usersSnapshot = await getDocs(usersCollection);
            const totalTeachers = usersSnapshot.docs.filter(doc => doc.data().role === 'teacher').length;

            // Count students with results
            let studentsWithResults = 0;
            for (const student of studentsData) {
                const resultsCollection = collection(db, `students/${student.id}/results`);
                const resultsSnapshot = await getDocs(resultsCollection);
                if (!resultsSnapshot.empty) {
                    // Check if student has at least one unique term combination
                    const uniqueTerms = new Set();
                    resultsSnapshot.docs.forEach((doc) => {
                        const result = doc.data();
                        uniqueTerms.add(`${result.term}-${result.year}`);
                    });
                    if (uniqueTerms.size > 0) {
                        studentsWithResults++;
                    }
                }
            }

            setStats({ totalStudents, totalTeachers, studentsWithResults });
        };
        fetchData();
        fetchNotifications();
    }, []);

    const handleSendNotification = async () => {
        if (!notificationMessage.trim() || !user) return;
        setIsSending(true);
        try {
            await addDoc(collection(db, 'notifications'), {
                message: notificationMessage,
                createdAt: new Date().toISOString(),
                createdBy: user.name,
            });
            setNotificationMessage("");
            toast({ title: "Notification Sent!", description: "Your message has been sent to all teachers." });
            await fetchNotifications(); // Refresh list
        } catch (error) {
            console.error("Error sending notification: ", error);
            toast({ title: "Error", description: "Could not send notification.", variant: "error" });
        } finally {
            setIsSending(false);
        }
    }

    const handleDeleteNotification = async (id: string) => {
        try {
            await deleteDoc(doc(db, 'notifications', id));
            toast({ title: "Notification Deleted" });
            await fetchNotifications(); // Refresh list
        } catch (error) {
            console.error("Error deleting notification: ", error);
            toast({ title: "Error", description: "Could not delete notification.", variant: "error" });
        }
    }

    const dashboardStats = [
        {
            title: 'Total Students',
            value: stats.totalStudents.toString(),
            change: `${stats.totalStudents} enrolled`,
            trend: 'up',
            icon: (
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
            ),
            color: 'from-blue-500 to-cyan-500',
            bgColor: 'from-blue-50 to-cyan-50'
        },
        {
            title: 'Active Teachers',
            value: stats.totalTeachers.toString(),
            change: 'All positions filled',
            trend: 'stable',
            icon: (
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
            ),
            color: 'from-green-500 to-emerald-500',
            bgColor: 'from-green-50 to-emerald-50'
        },
        {
            title: 'Total Parents',
            value: '0',
            change: 'Registered parents',
            trend: 'stable',
            icon: (
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
            ),
            color: 'from-purple-500 to-pink-500',
            bgColor: 'from-purple-50 to-pink-50'
        },
        {
            title: 'With Results',
            value: stats.studentsWithResults.toString(),
            change: 'Students with terms',
            trend: 'up',
            icon: (
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
            ),
            color: 'from-orange-500 to-red-500',
            bgColor: 'from-orange-50 to-red-50'
        }
    ];

  return (
    <div className="flex flex-1 flex-col mobile-gap-base lg:gap-6 animate-fade-in max-w-full overflow-hidden">
      {/* Welcome Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mobile-gap-base">
        <div className="min-w-0 flex-1">
          <h1 className="mobile-text-2xl lg:text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mobile-truncate">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-muted-foreground mt-1 mobile-text-sm lg:text-base">
            Here's what's happening with your school today.
          </p>
        </div>
        <div className="flex items-center gap-2 shrink-0">
          <Badge variant="secondary" className="bg-success/10 text-success border-success/20 mobile-text-sm">
            <TrendingUp className="w-3 h-3 mr-1" />
            System Active
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {dashboardStats.map((stat, index) => (
          <div key={index} className="group relative overflow-hidden">
            {/* Glass morphism card */}
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
              {/* Background gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.bgColor} opacity-50`}></div>

              {/* Content */}
              <div className="relative z-10 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-2">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </p>
                  <p className="text-xs text-gray-500 flex items-center space-x-1">
                    {stat.trend === 'up' && (
                      <svg className="w-3 h-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                      </svg>
                    )}
                    <span>{stat.change}</span>
                  </p>
                </div>
                <div className={`w-14 h-14 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  {stat.icon}
                </div>
              </div>

              {/* Hover glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Recent Students */}
        <div className="xl:col-span-2 bg-gray-50 rounded-xl p-6">
          <div className="flex justify-between items-start mb-4">
            <h4 className="font-semibold text-gray-900">Recent Students</h4>
            <button
              type="button"
              onClick={() => window.location.href = '/students'}
              className="text-blue-600 hover:text-blue-800"
              title="View all students"
            >
              <ArrowUpRight className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-2 text-sm">
            {recentStudents.length > 0 ? recentStudents.map((student) => (
              <div key={student.id} className="flex justify-between">
                <span className="text-gray-600">{student.name} ({student.class}):</span>
                <span className="font-medium">{student.dateAdded}</span>
              </div>
            )) : (
              <div className="text-center text-gray-500 py-4">
                No recent students found.
              </div>
            )}
          </div>
        </div>

        {/* Notifications */}
        <div className="bg-gray-50 rounded-xl p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Send Notification</h4>
          <div className="space-y-3">
            <Textarea
              placeholder="Type your message here..."
              value={notificationMessage}
              onChange={(e) => setNotificationMessage(e.target.value)}
              disabled={isSending}
              className="min-h-[100px] resize-none"
            />
            <Button
              onClick={handleSendNotification}
              disabled={isSending || !notificationMessage.trim()}
              className="w-full"
            >
              <Send className="mr-2 h-4 w-4" />
              {isSending ? "Sending..." : "Send to Teachers"}
            </Button>

            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between font-semibold mb-2">
                <span>Active Notifications:</span>
                <span className="text-blue-600">{notifications.length}</span>
              </div>
              <div className="space-y-2 text-sm max-h-32 overflow-y-auto">
                {notifications.length > 0 ? notifications.map(n => (
                  <div key={n.id} className="flex justify-between">
                    <span className="text-gray-600 truncate flex-1">{n.message}</span>
                    <button
                      type="button"
                      onClick={() => handleDeleteNotification(n.id)}
                      className="text-red-600 hover:text-red-800 ml-2"
                      title="Delete notification"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                )) : (
                  <div className="text-center text-gray-500 py-2">
                    No notifications yet
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
