import { app, BrowserWindow, ipcMain } from 'electron'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import { initializeDatabaseService } from './database-service'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    width: 1280,
    height: 800,
    minWidth: 1024,
    minHeight: 600,
    center: true, // Center the window on screen
    frame: false, // Remove default frame for custom title bar
    titleBarStyle: 'hidden', // Hide title bar but keep traffic lights on macOS
    title: 'Maggie Preparatory School - Management System', // Set window title
    icon: VITE_DEV_SERVER_URL
      ? path.join(process.env.VITE_PUBLIC, 'logo.ico')
      : path.join(__dirname, '../public/logo.ico'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
    show: false, // Don't show until ready
    backgroundColor: '#f8fafc', // Light background to match app
  })

  // Show window when ready to prevent visual flash
  win.once('ready-to-show', () => {
    win?.show()
    // Only open dev tools if explicitly requested
    // if (process.env.NODE_ENV === 'development') {
    //   win?.webContents.openDevTools()
    // }
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    console.log('🔄 Loading development server:', VITE_DEV_SERVER_URL)
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    const indexPath = path.join(RENDERER_DIST, 'index.html')
    console.log('🔄 Loading production build from:', indexPath)
    console.log('📁 RENDERER_DIST:', RENDERER_DIST)
    console.log('📁 VITE_PUBLIC:', process.env.VITE_PUBLIC)

    // Check if logo files exist
    const logoPath = path.join(RENDERER_DIST, 'logo.jpg')
    const logoIconPath = path.join(RENDERER_DIST, 'logo.ico')

    try {
      const fs = require('fs')
      if (fs.existsSync(logoPath)) {
        console.log('✅ Logo file found:', logoPath)
      } else {
        console.warn('❌ Logo file NOT found:', logoPath)
      }

      if (fs.existsSync(logoIconPath)) {
        console.log('✅ Logo icon found:', logoIconPath)
      } else {
        console.warn('❌ Logo icon NOT found:', logoIconPath)
      }
    } catch (error) {
      console.error('Error checking logo files:', error)
    }

    win.loadFile(indexPath)
  }

  // Setup window state event listeners
  setupWindowStateEvents()
}

// Window control IPC handlers
ipcMain.handle('window-minimize', () => {
  win?.minimize()
})

ipcMain.handle('window-maximize', () => {
  if (win?.isMaximized()) {
    win?.unmaximize()
  } else {
    win?.maximize()
  }
})

ipcMain.handle('window-unmaximize', () => {
  win?.unmaximize()
})

ipcMain.handle('window-close', () => {
  win?.close()
})

ipcMain.handle('window-is-maximized', () => {
  return win?.isMaximized()
})

// Window state change events
function setupWindowStateEvents() {
  win?.on('maximize', () => {
    win?.webContents.send('window-maximized')
  })

  win?.on('unmaximize', () => {
    win?.webContents.send('window-unmaximized')
  })
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(async () => {
  // Initialize database service
  console.log('🚀 Initializing main process database service...');
  const dbService = initializeDatabaseService({
    mongoUrl: 'mongodb://localhost:27017',
    mongoDatabase: 'maggie_school_offline',
    syncEnabled: true
  });
  console.log('✅ Main process database service initialized');

  // Test MongoDB connection
  try {
    const result = await dbService.connect();
    if (result.success) {
      console.log('🎉 MongoDB connection successful!');
    } else {
      console.log('⚠️ MongoDB connection failed:', result.error);
    }
  } catch (error) {
    console.log('❌ MongoDB connection error:', error);
  }

  // Create the main window
  createWindow();
});
