@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar utilities */
@layer utilities {
  .scrollbar-hide {
    /* IE and Edge */
    -ms-overflow-style: none;
    /* Firefox */
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    /* Safari and Chrome */
    display: none;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 144.9 80.4% 10%;
    --warning: 48 96% 89%;
    --warning-foreground: 38 92% 50%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    overflow-x: hidden;
    max-width: 100vw;
    width: 100%;
  }
  
  /* Prevent horizontal scroll on all elements */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }
  
  /* Ensure all containers respect viewport width */
  .container, .container-fluid {
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  /* Prevent horizontal scroll on main content */
  main {
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  /* Ensure responsive images don't overflow */
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* Prevent body scroll on desktop login pages */
  body.desktop-login {
    overflow: hidden;
  }
  
  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/50;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  /* Glassmorphism Effect */
  .glass {
    @apply bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/20 dark:border-white/10 shadow-2xl;
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
  
  /* Modern Card Styles */
  .card-modern {
    @apply bg-card border border-border/50 shadow-sm hover:shadow-md transition-all duration-200 rounded-xl;
  }
  
  /* Gradient Backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-br from-primary/10 via-primary/5 to-transparent;
  }
  
  .gradient-success {
    @apply bg-gradient-to-br from-success/10 via-success/5 to-transparent;
  }
  
  .gradient-warning {
    @apply bg-gradient-to-br from-warning/10 via-warning/5 to-transparent;
  }
  
  .gradient-info {
    @apply bg-gradient-to-br from-info/10 via-info/5 to-transparent;
  }
  
  /* Modern Button Styles */
  .btn-modern {
    @apply relative overflow-hidden transition-all duration-200 hover:scale-105 active:scale-95;
  }
  
  .btn-modern::before {
    content: '';
    @apply absolute inset-0 bg-white/20 opacity-0 transition-opacity duration-200;
  }
  
  .btn-modern:hover::before {
    @apply opacity-100;
  }
  
  /* Loading Animation */
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }
  
  /* Loading dot animation delays */
  .loading-dot-0 {
    animation-delay: 0s;
  }
  
  .loading-dot-1 {
    animation-delay: 0.2s;
  }
  
  .loading-dot-2 {
    animation-delay: 0.4s;
  }
  
  /* Modern Table Styles */
  .table-modern {
    @apply w-full border-collapse;
  }
  
  .table-modern th {
    @apply text-left font-semibold text-sm text-muted-foreground p-4 border-b border-border/50;
  }
  
  .table-modern td {
    @apply p-4 border-b border-border/30;
  }
  
  .table-modern tr:hover {
    @apply bg-muted/30;
  }
  
  /* Modern Form Styles */
  .form-modern {
    @apply space-y-6;
  }
  
  .form-modern .form-group {
    @apply space-y-2;
  }
  
  .form-modern label {
    @apply text-sm font-medium text-foreground;
  }
  
  .form-modern input,
  .form-modern textarea,
  .form-modern select {
    @apply w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200;
  }
  
  /* Modern Navigation */
  .nav-modern {
    @apply space-y-1;
  }
  
  .nav-modern a {
    @apply flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-muted/50 hover:text-foreground;
  }
  
  .nav-modern a.active {
    @apply bg-primary/10 text-primary border-r-2 border-primary;
  }
  
  /* Modern Stats Cards */
  .stats-card {
    @apply relative overflow-hidden rounded-xl border border-border/50 bg-card p-6 shadow-sm hover:shadow-md transition-all duration-200;
  }
  
  .stats-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-200;
  }
  
  .stats-card:hover::before {
    @apply opacity-100;
  }
  
  /* Modern Sidebar */
  .sidebar-modern {
    @apply bg-card/50 backdrop-blur-xl border-r border-border/50;
  }
  
  /* Modern Header */
  .header-modern {
    @apply bg-card/50 backdrop-blur-xl border-b border-border/50 sticky top-0 z-50;
  }
  
  /* Chart Component Styles */
  .chart-indicator {
    @apply shrink-0 rounded-[2px];
  }
  
  .chart-indicator-dot {
    @apply h-2.5 w-2.5;
  }
  
  .chart-indicator-line {
    @apply w-1;
  }
  
  .chart-indicator-dashed {
    @apply w-0 border-[1.5px] border-dashed bg-transparent;
  }
  
  .chart-legend-item {
    @apply h-2 w-2 shrink-0 rounded-[2px];
  }
  
  /* Chart Color Handling */
  .chart-indicator[data-indicator-color] {
    background-color: var(--indicator-color);
    border-color: var(--indicator-color);
  }
  
  .chart-legend-item[data-legend-color] {
    background-color: var(--legend-color);
  }
  
  /* Sidebar Component Styles */
  .sidebar-wrapper {
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;
  }
  
  .sidebar-mobile {
    --sidebar-width: 18rem;
  }
  
  /* Mobile Responsive Styles */
  .mobile-container {
    @apply w-full max-w-full overflow-x-hidden;
  }
  
  .mobile-table {
    @apply overflow-x-auto -mx-4 sm:mx-0;
  }
  
  .mobile-table table {
    @apply min-w-full;
  }
  
  .mobile-card {
    @apply p-4 sm:p-6;
  }
  
  .mobile-text {
    @apply text-sm sm:text-base;
  }
  
  .mobile-heading {
    @apply text-2xl sm:text-3xl font-bold;
  }
  
  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
  
  .mobile-flex {
    @apply flex flex-col sm:flex-row gap-4;
  }
  
  .mobile-padding {
    @apply p-3 sm:p-4 lg:p-6;
  }
  
  .mobile-gap {
    @apply gap-4 sm:gap-6;
  }
  
  /* Mobile-specific login form centering */
  .mobile-login-container {
    @apply flex items-center justify-center h-screen px-4 py-4;
    min-height: 100vh;
    max-height: 100vh;
    max-width: 100vw;
    overflow: hidden;
    width: 100%;
  }
  
  .mobile-login-form {
    @apply w-full max-w-sm mx-auto;
    max-height: calc(100vh - 2rem);
    max-width: calc(100vw - 2rem);
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  /* Mobile-optimized components */
  .mobile-card {
    @apply p-4 sm:p-6;
    border-radius: 0.75rem;
  }
  
  .mobile-button {
    @apply py-2 px-4 text-sm font-medium;
    border-radius: 0.5rem;
  }
  
  .mobile-button-lg {
    @apply py-3 px-6 text-base font-semibold;
    border-radius: 0.75rem;
  }
  
  .mobile-input {
    @apply py-2 px-3 text-sm;
    border-radius: 0.5rem;
  }
  
  .mobile-text-sm {
    @apply text-xs sm:text-sm;
  }
  
  .mobile-text-base {
    @apply text-sm sm:text-base;
  }
  
  .mobile-text-lg {
    @apply text-base sm:text-lg;
  }
  
  .mobile-text-xl {
    @apply text-lg sm:text-xl;
  }
  
  .mobile-text-2xl {
    @apply text-xl sm:text-2xl;
  }
  
  .mobile-text-3xl {
    @apply text-2xl sm:text-3xl;
  }
  
  .mobile-gap-sm {
    @apply gap-2 sm:gap-3;
  }
  
  .mobile-gap-base {
    @apply gap-3 sm:gap-4;
  }
  
  .mobile-gap-lg {
    @apply gap-4 sm:gap-6;
  }
  
  .mobile-padding-sm {
    @apply p-3 sm:p-4;
  }
  
  .mobile-padding-base {
    @apply p-4 sm:p-6;
  }
  
  .mobile-padding-lg {
    @apply p-6 sm:p-8;
  }
  
  .mobile-margin-sm {
    @apply m-2 sm:m-3;
  }
  
  .mobile-margin-base {
    @apply m-3 sm:m-4;
  }
  
  .mobile-margin-lg {
    @apply m-4 sm:m-6;
  }
  
  /* Loading animation delays */
  .loading-dot-0 {
    animation-delay: 0ms;
  }
  
  .loading-dot-1 {
    animation-delay: 150ms;
  }
  
  .loading-dot-2 {
    animation-delay: 300ms;
  }
  
  /* Desktop login form centering - no scroll */
  .desktop-login-container {
    @apply flex items-center justify-center min-h-screen px-8 py-8;
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  .desktop-login-form {
    @apply w-full max-w-md mx-auto;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
  }
  
  /* Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

  /* Mobile-optimized components */
  .mobile-card {
    @apply p-4 sm:p-6;
    border-radius: 0.75rem;
  }
  
  .mobile-button {
    @apply py-2 px-4 text-sm font-medium;
    border-radius: 0.5rem;
  }
  
  .mobile-button-lg {
    @apply py-3 px-6 text-base font-semibold;
    border-radius: 0.75rem;
  }
  
  .mobile-input {
    @apply py-2 px-3 text-sm;
    border-radius: 0.5rem;
  }
  
  .mobile-text-sm {
    @apply text-xs sm:text-sm;
  }
  
  .mobile-text-base {
    @apply text-sm sm:text-base;
  }
  
  .mobile-text-lg {
    @apply text-base sm:text-lg;
  }
  
  .mobile-text-xl {
    @apply text-lg sm:text-xl;
  }
  
  .mobile-text-2xl {
    @apply text-xl sm:text-2xl;
  }
  
  .mobile-text-3xl {
    @apply text-2xl sm:text-3xl;
  }
  
  .mobile-gap-sm {
    @apply gap-2 sm:gap-3;
  }
  
  .mobile-gap-base {
    @apply gap-3 sm:gap-4;
  }
  
  .mobile-gap-lg {
    @apply gap-4 sm:gap-6;
  }
  
  .mobile-padding-sm {
    @apply p-3 sm:p-4;
  }
  
  .mobile-padding-base {
    @apply p-4 sm:p-6;
  }
  
  .mobile-padding-lg {
    @apply p-6 sm:p-8;
  }
  
  .mobile-margin-sm {
    @apply m-2 sm:m-3;
  }
  
  .mobile-margin-base {
    @apply m-3 sm:m-4;
  }
  
  .mobile-margin-lg {
    @apply m-4 sm:m-6;
  }
  
  /* Mobile table constraints */
  .mobile-table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
  }

  .mobile-table-container::-webkit-scrollbar {
    height: 6px;
  }

  .mobile-table-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .mobile-table-container::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 3px;
  }

  .mobile-table-container::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
  
  .mobile-table {
    @apply min-w-full;
    max-width: 100vw;
  }
  
  .mobile-table th,
  .mobile-table td {
    @apply px-2 py-2 text-xs sm:text-sm;
    white-space: nowrap;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* Mobile card constraints */
  .mobile-card-container {
    @apply max-w-full overflow-hidden;
  }
  
  /* Mobile dropdown constraints */
  .mobile-dropdown {
    @apply max-w-[90vw] sm:max-w-xs;
  }
  
  /* Mobile dialog constraints */
  .mobile-dialog {
    @apply max-w-[95vw] sm:max-w-md;
  }
  
  /* Mobile grid constraints */
  .mobile-grid {
    @apply grid gap-3 sm:gap-4;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  /* Mobile flex constraints */
  .mobile-flex {
    @apply flex flex-wrap gap-2 sm:gap-3;
  }
  
  /* Mobile text truncation */
  .mobile-truncate {
    @apply truncate max-w-full;
  }
  
  /* Mobile button constraints */
  .mobile-button-container {
    @apply flex flex-wrap gap-2 sm:gap-3;
  }
  
  /* Mobile form constraints */
  .mobile-form {
    @apply max-w-full space-y-4;
  }
  
  .mobile-form-field {
    @apply w-full max-w-full;
  }

  /* Shimmer animation for floating alerts */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

/* Enhanced glassmorphism for semi-circle */
.glass-semi-enhanced {
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Background image classes using document root custom properties */
.bg-image-current {
  background-image: var(--bg-image-current);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-image-next {
  background-image: var(--bg-image-next);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Desktop-specific background sizing - focus on people */
@media (min-width: 1024px) {
  .bg-image-current,
  .bg-image-next {
    background-size: 150% auto;
    background-position: center 30%;
  }
}

/* Glassmorphism animations */
@keyframes glassmorphism {
  0% {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.05);
  }
  50% {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
  }
  100% {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.05);
  }
}

.glassmorphism {
  animation: glassmorphism 3s ease-in-out infinite;
}

/* Quote transition animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.quote-enter {
  animation: fadeInUp 0.5s ease-out;
}

.quote-exit {
  animation: fadeOutDown 0.5s ease-in;
}

/* Shimmer effect for buttons */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse animation for active quote indicator */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.25);
  }
}

.pulse-active {
  animation: pulse 2s ease-in-out infinite;
}

/* Semi-circle shape for quote area */
.semi-circle {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 50%, 50% 0);
}

/* Background image transition effects */
.bg-transition {
  transition: background-image 1s ease-in-out;
}

/* Improved glassmorphism for semi-circle area */
.glass-semi {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Quote text animation for semi-circle */
.quote-semi {
  animation: fadeInUp 0.8s ease-out;
}

/* Floating elements for semi-circle */
.float-semi {
  animation: float 4s ease-in-out infinite;
}

.float-semi-delay-1 {
  animation: float 4s ease-in-out infinite;
  animation-delay: 1s;
}

.float-semi-delay-2 {
  animation: float 4s ease-in-out infinite;
  animation-delay: 2s;
}

/* Smooth crossfade for background images */
.bg-crossfade {
  transition: opacity 1.5s ease-in-out;
}

.bg-crossfade-enter {
  opacity: 0;
}

.bg-crossfade-enter-active {
  opacity: 1;
}

.bg-crossfade-exit {
  opacity: 1;
}

.bg-crossfade-exit-active {
  opacity: 0;
}

/* Standalone quote circle design */
.quote-circle-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quote-circle-border {
  position: absolute;
  inset: 0;
  width: 20rem;
  height: 20rem;
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
}

.quote-circle-content {
  position: relative;
  width: 20rem;
  height: 20rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.8) inset;
}

.quote-circle-icon {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border: 2px solid #3b82f6;
}

.quote-circle-text {
  color: #1e3a8a;
  font-weight: 500;
  line-height: 1.6;
}

.quote-circle-author {
  color: #1d4ed8;
  font-weight: 600;
}

.quote-circle-indicator {
  transition: all 0.3s ease;
}

.quote-circle-indicator.active {
  background: #2563eb;
  transform: scale(1.25);
  box-shadow: 0 0 10px rgba(37, 99, 235, 0.5);
}

.quote-circle-indicator.inactive {
  background: #93c5fd;
}

.quote-circle-decoration {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 4s ease-in-out infinite;
}

.quote-circle-decoration-delay-0 {
  animation-delay: 0s;
}

.quote-circle-decoration-delay-1 {
  animation-delay: 1s;
}

.quote-circle-decoration-delay-2 {
  animation-delay: 2s;
}

/* Collapsed sidebar tooltip styles */
.sidebar-collapsed .nav-item {
  position: relative;
}

.sidebar-collapsed .nav-item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 0.5rem;
  pointer-events: none;
}

/* Semi-circle arc design */
.quote-arc-content {
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.1));
  opacity: 1 !important;
  visibility: visible !important;
}

/* Arc container positioning */
.quote-arc-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  pointer-events: auto;
  z-index: 10;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Arc content positioning */
.quote-arc-content-wrapper {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  pointer-events: auto;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Enhanced arc styling */
.quote-arc-content-enhanced {
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15));
  opacity: 1 !important;
  visibility: visible !important;
}
