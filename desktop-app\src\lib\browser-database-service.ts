// Browser-compatible database service using IndexedDB
// This will be replaced with proper MongoDB integration through main process later

export interface DatabaseConfig {
  dbName: string;
  version: number;
  syncEnabled: boolean;
}

export interface SyncMetadata {
  lastModified: Date;
  source: 'firebase' | 'local';
  syncStatus: 'pending' | 'synced' | 'conflict';
  firebaseId?: string;
  localId?: string;
}

export interface DatabaseDocument {
  id?: string;
  syncMeta?: SyncMetadata;
  [key: string]: any;
}

export type ConnectionStatus = 'online' | 'offline' | 'syncing' | 'error';

// IndexedDB wrapper for offline storage
class IndexedDBWrapper {
  private dbName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(dbName: string, version: number = 1) {
    this.dbName = dbName;
    this.version = version;
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores for each collection
        const collections = ['users', 'students', 'results', 'feeStructures', 'studentPayments', 'subjects'];
        
        collections.forEach(collectionName => {
          if (!db.objectStoreNames.contains(collectionName)) {
            const store = db.createObjectStore(collectionName, { keyPath: 'id', autoIncrement: false });
            store.createIndex('syncMeta.lastModified', 'syncMeta.lastModified', { unique: false });
            store.createIndex('syncMeta.firebaseId', 'syncMeta.firebaseId', { unique: false });
          }
        });
      };
    });
  }

  async create(collection: string, data: any): Promise<string> {
    if (!this.db) throw new Error('Database not connected');

    const id = data.id || this.generateId();
    const document = {
      ...data,
      id,
      syncMeta: {
        lastModified: new Date(),
        source: 'local',
        syncStatus: 'pending',
        localId: id
      }
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([collection], 'readwrite');
      const store = transaction.objectStore(collection);
      const request = store.add(document);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(request.error);
    });
  }

  async read(collection: string, query: any = {}): Promise<any[]> {
    if (!this.db) throw new Error('Database not connected');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([collection], 'readonly');
      const store = transaction.objectStore(collection);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;
        
        // Simple query filtering (basic implementation)
        if (Object.keys(query).length > 0) {
          results = results.filter(item => {
            return Object.entries(query).every(([key, value]) => {
              return this.getNestedValue(item, key) === value;
            });
          });
        }
        
        resolve(results);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async update(collection: string, id: string, data: any): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([collection], 'readwrite');
      const store = transaction.objectStore(collection);
      
      // First get the existing document
      const getRequest = store.get(id);
      
      getRequest.onsuccess = () => {
        const existingDoc = getRequest.result;
        if (!existingDoc) {
          reject(new Error(`Document with id ${id} not found`));
          return;
        }

        const updatedDoc = {
          ...existingDoc,
          ...data,
          id, // Ensure ID doesn't change
          syncMeta: {
            ...existingDoc.syncMeta,
            lastModified: new Date(),
            source: 'local',
            syncStatus: 'pending'
          }
        };

        const putRequest = store.put(updatedDoc);
        putRequest.onsuccess = () => resolve();
        putRequest.onerror = () => reject(putRequest.error);
      };
      
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async delete(collection: string, id: string): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([collection], 'readwrite');
      const store = transaction.objectStore(collection);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Browser Database Service
export class BrowserDatabaseService {
  private indexedDB: IndexedDBWrapper;
  private connectionStatus: ConnectionStatus = 'offline';
  private config: DatabaseConfig;
  private subscriptions: Map<string, () => void> = new Map();
  private syncQueue: Array<{ collection: string; operation: string; data: any; timestamp: Date }> = [];

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.indexedDB = new IndexedDBWrapper(config.dbName, config.version);
  }

  // Connection Management
  async connect(): Promise<void> {
    try {
      console.log('🔄 Connecting to IndexedDB...');
      await this.indexedDB.connect();
      this.connectionStatus = 'offline'; // Start offline, will be updated by network monitor
      console.log('✅ Connected to IndexedDB:', this.config.dbName);
    } catch (error) {
      console.error('❌ IndexedDB connection failed:', error);
      this.connectionStatus = 'error';
      throw new Error(`Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async disconnect(): Promise<void> {
    // Clear all subscriptions
    this.subscriptions.forEach(unsubscribe => unsubscribe());
    this.subscriptions.clear();
    
    await this.indexedDB.close();
    this.connectionStatus = 'offline';
    console.log('🔌 Disconnected from IndexedDB');
  }

  // Connection Status
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
  }

  isOnline(): boolean {
    return this.connectionStatus === 'online' || this.connectionStatus === 'syncing';
  }

  // CRUD Operations
  async create(collectionName: string, data: any): Promise<string> {
    try {
      const documentId = await this.indexedDB.create(collectionName, data);

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'create', { ...data, id: documentId });
      }

      console.log(`✅ Created document in ${collectionName}:`, documentId);
      return documentId;
    } catch (error) {
      console.error(`❌ Failed to create document in ${collectionName}:`, error);
      throw error;
    }
  }

  async read(collectionName: string, query: any = {}): Promise<any[]> {
    try {
      return await this.indexedDB.read(collectionName, query);
    } catch (error) {
      console.error(`❌ Failed to read from ${collectionName}:`, error);
      throw error;
    }
  }

  async update(collectionName: string, id: string, data: any): Promise<void> {
    try {
      await this.indexedDB.update(collectionName, id, data);

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'update', { id, ...data });
      }

      console.log(`✅ Updated document in ${collectionName}:`, id);
    } catch (error) {
      console.error(`❌ Failed to update document in ${collectionName}:`, error);
      throw error;
    }
  }

  async delete(collectionName: string, id: string): Promise<void> {
    try {
      await this.indexedDB.delete(collectionName, id);

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'delete', { id });
      }

      console.log(`✅ Deleted document from ${collectionName}:`, id);
    } catch (error) {
      console.error(`❌ Failed to delete document from ${collectionName}:`, error);
      throw error;
    }
  }

  // Real-time Subscriptions (simplified polling)
  subscribe(collectionName: string, callback: (data: any[]) => void): () => void {
    const subscriptionKey = `${collectionName}_${Date.now()}`;
    
    // Poll for changes every 2 seconds
    const pollInterval = setInterval(async () => {
      try {
        const data = await this.read(collectionName);
        callback(data);
      } catch (error) {
        console.error(`❌ Subscription error for ${collectionName}:`, error);
      }
    }, 2000);

    const unsubscribe = () => {
      clearInterval(pollInterval);
      this.subscriptions.delete(subscriptionKey);
    };

    this.subscriptions.set(subscriptionKey, unsubscribe);
    
    // Initial data load
    this.read(collectionName).then(callback).catch(console.error);

    return unsubscribe;
  }

  // Sync Queue Management
  private addToSyncQueue(collection: string, operation: string, data: any): void {
    this.syncQueue.push({
      collection,
      operation,
      data,
      timestamp: new Date()
    });
  }

  getSyncQueue(): Array<{ collection: string; operation: string; data: any; timestamp: Date }> {
    return [...this.syncQueue];
  }

  clearSyncQueue(): void {
    this.syncQueue = [];
  }
}

// Default configuration
export const defaultDatabaseConfig: DatabaseConfig = {
  dbName: 'maggie_school_offline',
  version: 1,
  syncEnabled: true
};

// Singleton instance
let databaseServiceInstance: BrowserDatabaseService | null = null;

export function getDatabaseService(config?: DatabaseConfig): BrowserDatabaseService {
  if (!databaseServiceInstance) {
    databaseServiceInstance = new BrowserDatabaseService(config || defaultDatabaseConfig);
  }
  return databaseServiceInstance;
}
