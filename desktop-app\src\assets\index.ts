// Asset path resolver for proper handling in both dev and production
export const getLogoPath = () => {
  // Check if we're in Electron environment
  if (typeof window !== 'undefined' && window.electronAPI) {
    // In Electron, check if we're in production (file protocol) or development
    if (window.location.protocol === 'file:') {
      // Electron production build - assets are in the dist folder
      return './logo.jpg'
    } else {
      // Electron development - use dev server path
      return '/logo.jpg'
    }
  }
  // Web build fallback
  return '/logo.jpg'
}

export const getLogoIconPath = () => {
  // Check if we're in Electron environment
  if (typeof window !== 'undefined' && window.electronAPI) {
    // In Electron, check if we're in production (file protocol) or development
    if (window.location.protocol === 'file:') {
      // Electron production build - assets are in the dist folder
      return './logo.ico'
    } else {
      // Electron development - use dev server path
      return '/logo.ico'
    }
  }
  // Web build fallback
  return '/logo.ico'
}

// Enhanced logo path with fallback and error handling
export const getLogoPathWithFallback = () => {
  const primaryPath = getLogoPath()

  // Return an object with primary path and fallback options
  return {
    primary: primaryPath,
    fallbacks: [
      './logo.jpg',
      '/logo.jpg',
      'logo.jpg',
      // Base64 fallback could be added here if needed
    ]
  }
}

// Preload logo to ensure it's available
export const preloadLogo = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    const logoConfig = getLogoPathWithFallback()
    let currentIndex = 0

    const tryLoadLogo = (path: string) => {
      const img = new Image()

      img.onload = () => {
        console.log(`✅ Logo preloaded successfully: ${path}`)
        resolve(path)
      }

      img.onerror = () => {
        console.warn(`❌ Failed to preload logo: ${path}`)
        currentIndex++

        if (currentIndex < logoConfig.fallbacks.length) {
          tryLoadLogo(logoConfig.fallbacks[currentIndex])
        } else {
          console.error('❌ All logo paths failed to load')
          reject(new Error('Failed to load logo from any path'))
        }
      }

      img.src = path
    }

    // Start with primary path
    tryLoadLogo(logoConfig.primary)
  })
}

// Get a working logo path (async)
export const getWorkingLogoPath = async (): Promise<string> => {
  try {
    return await preloadLogo()
  } catch (error) {
    console.error('Failed to get working logo path:', error)
    // Return primary path as fallback
    return getLogoPath()
  }
}
