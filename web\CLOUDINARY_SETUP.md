# Cloudinary Setup Guide

## 1. Get Your Cloudinary Credentials

1. Go to [Cloudinary Console](https://console.cloudinary.com/)
2. Sign up or log in to your account
3. Go to your Dashboard
4. Copy your credentials:
   - **Cloud Name**
   - **API Key**
   - **API Secret**

## 2. Create Upload Preset

1. In your Cloudinary Console, go to **Settings** → **Upload**
2. Scroll down to **Upload presets**
3. Click **Add upload preset**
4. Set:
   - **Preset name**: `school_management` (or any name you prefer)
   - **Signing Mode**: `Unsigned`
   - **Folder**: `school-management/students` (optional)
5. Save the preset

## 3. Environment Variables

Create a `.env.local` file in your project root with:

```env
# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset_name
```

## 4. Features

✅ **Image Upload**: Students can upload photos during registration/editing
✅ **Image Preview**: See the image before uploading
✅ **Automatic Optimization**: Cloudinary optimizes images automatically
✅ **Secure URLs**: All images use HTTPS
✅ **Responsive Images**: Cloudinary can serve different sizes
✅ **Free Tier**: 25GB storage, 25GB bandwidth/month

## 5. Usage

- **Add Student**: Upload photo during registration
- **Edit Student**: Update photo anytime
- **Display**: Photos show in student cards and details
- **Fallback**: Shows initials if no photo is uploaded

## 6. Benefits over Firebase Storage

- ✅ **Free Tier**: More generous than Firebase
- ✅ **Image Optimization**: Automatic resizing and compression
- ✅ **CDN**: Faster global delivery
- ✅ **Transformations**: On-the-fly image editing
- ✅ **No Blaze Plan Required**: Works with free tier 