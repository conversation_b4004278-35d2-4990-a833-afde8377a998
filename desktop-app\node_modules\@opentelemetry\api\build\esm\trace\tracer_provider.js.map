{"version": 3, "file": "tracer_provider.js", "sourceRoot": "", "sources": ["../../../src/trace/tracer_provider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Tracer } from './tracer';\nimport { TracerOptions } from './tracer_options';\n\n/**\n * A registry for creating named {@link Tracer}s.\n */\nexport interface TracerProvider {\n  /**\n   * Returns a Tracer, creating one if one with the given name and version is\n   * not already created.\n   *\n   * This function may return different Tracer types (e.g.\n   * {@link NoopTracerProvider} vs. a functional tracer).\n   *\n   * @param name The name of the tracer or instrumentation library.\n   * @param version The version of the tracer or instrumentation library.\n   * @param options The options of the tracer or instrumentation library.\n   * @returns Tracer A Tracer with the given name and version\n   */\n  getTracer(name: string, version?: string, options?: TracerOptions): Tracer;\n}\n"]}