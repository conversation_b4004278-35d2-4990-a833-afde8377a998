import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { publicId } = await request.json();

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 });
    }

    const timestamp = Math.round(new Date().getTime() / 1000);
    const apiKey = process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET; // No NEXT_PUBLIC_ prefix for security
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

    if (!apiKey || !apiSecret || !cloudName) {
      console.error('Missing environment variables:', {
        apiKey: !!apiKey,
        apiSecret: !!apiSecret,
        cloudName: !!cloudName
      });
      return NextResponse.json({ error: 'Cloudinary configuration missing' }, { status: 500 });
    }

    // Generate signature
    const params = `public_id=${publicId}&timestamp=${timestamp}`;
    const signature = require('crypto')
      .createHash('sha1')
      .update(params + apiSecret)
      .digest('hex');

    const formData = new FormData();
    formData.append('public_id', publicId);
    formData.append('api_key', apiKey);
    formData.append('timestamp', timestamp.toString());
    formData.append('signature', signature);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Cloudinary deletion error:', errorData);
      return NextResponse.json({ error: 'Failed to delete image from Cloudinary' }, { status: 500 });
    }

    const result = await response.json();
    console.log('Successfully deleted image:', publicId);

    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 