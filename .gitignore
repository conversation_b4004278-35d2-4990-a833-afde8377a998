# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.genkit/*
.env*

# firebase
firebase-debug.log
firestore-debug.log

# electron
desktop/dist/
desktop/win-unpacked/
desktop/node_modules/

# desktop-app electron build outputs
desktop-app/dist/
desktop-app/dist-electron/
desktop-app/dist-installer/
desktop-app/win-unpacked/