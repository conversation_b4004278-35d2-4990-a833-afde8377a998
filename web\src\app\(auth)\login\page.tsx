"use client";

import { LoginForm } from "@/components/auth/login-form";
import { useAuth } from "@/hooks/use-auth";
import { redirect } from "next/navigation";
import { useEffect, useState } from "react";

const educationalQuotes = [
  "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
  "The function of education is to teach one to think intensively and to think critically. - <PERSON>",
  "Education is not preparation for life; education is life itself. - <PERSON>",
  "The roots of education are bitter, but the fruit is sweet. - <PERSON>",
  "Education is the key to success in life, and teachers make a lasting impact in the lives of their students. - <PERSON>",
  "Learning is not attained by chance, it must be sought for with ardor and attended to with diligence. - <PERSON>",
  "Education is what remains after one has forgotten what one has learned in school. - <PERSON>",
  "The goal of education is the advancement of knowledge and the dissemination of truth. - <PERSON>"
];

const backgroundImages = [
  "/family-image.jpg",
  "/Shuffle/231A2368.jpg",
  "/Shuffle/231A2366.jpg",
  "/Shuffle/231A2367.jpg",
  "/Shuffle/231A2371.jpg",
  "/Shuffle/231A2370.jpg",
  "/Shuffle/231A2373.jpg",
  "/Shuffle/231A2374.jpg",
  "/Shuffle/231A2375.jpg",
  "/Shuffle/231A2384.jpg",
  "/Shuffle/231A2380.jpg",
  "/Shuffle/231A2519.jpg",
  "/Shuffle/231A2518.jpg",
  "/Shuffle/231A2516.jpg",
  "/Shuffle/231A2481.jpg",
  "/Shuffle/231A2477.jpg",
  "/Shuffle/231A2475.jpg",
  "/Shuffle/231A2474.jpg",
  "/Shuffle/231A2470.jpg",
  "/Shuffle/231A2465.jpg",
  "/Shuffle/231A2464.jpg",
  "/Shuffle/231A2461.jpg",
  "/Shuffle/231A2457.jpg",
  "/Shuffle/231A2454.jpg",
  "/Shuffle/231A2450.jpg",
  "/Shuffle/231A2438.jpg",
  "/Shuffle/231A2439.jpg",
  "/Shuffle/231A2437.jpg",
  "/Shuffle/231A2435.jpg",
  "/Shuffle/231A2433.jpg",
  "/Shuffle/231A2432.jpg",
  "/Shuffle/231A2431.jpg",
  "/Shuffle/231A2426.jpg",
  "/Shuffle/231A2419.jpg",
  "/Shuffle/231A2415.jpg",
  "/Shuffle/231A2413.jpg",
  "/Shuffle/231A2402.jpg",
  "/Shuffle/231A2398.jpg",
  "/Shuffle/231A2462.jpg",
  "/Shuffle/231A2448.jpg"
];

export default function LoginPage() {
  const { user, isLoading } = useAuth();
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [nextImageIndex, setNextImageIndex] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (user && !isLoading) {
      redirect("/dashboard");
    }
  }, [user, isLoading]);

  // Quote rotation every 10 seconds
  useEffect(() => {
    const quoteInterval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % educationalQuotes.length);
    }, 10000);

    return () => clearInterval(quoteInterval);
  }, []);

  // Background image rotation every 7 seconds with crossfade
  useEffect(() => {
    const imageInterval = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex(nextImageIndex);
        setNextImageIndex((nextImageIndex + 1) % backgroundImages.length);
        setIsTransitioning(false);
      }, 500);
    }, 7000);

    return () => clearInterval(imageInterval);
  }, [nextImageIndex]);

  // Set CSS custom properties for background images
  useEffect(() => {
    document.documentElement.style.setProperty('--bg-image-current', `url('${backgroundImages[currentImageIndex]}')`);
    document.documentElement.style.setProperty('--bg-image-next', `url('${backgroundImages[nextImageIndex]}')`);
    
    // Cleanup function to remove custom properties
    return () => {
      document.documentElement.style.removeProperty('--bg-image-current');
      document.documentElement.style.removeProperty('--bg-image-next');
    };
  }, [currentImageIndex, nextImageIndex]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-blue-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Full Background */}
      <div className="absolute inset-0">
        {/* Background Images with Crossfade */}
        <div 
          className={`absolute inset-0 bg-cover bg-center bg-no-repeat transition-opacity duration-1000 ease-in-out bg-image-current ${
            isTransitioning ? 'opacity-0' : 'opacity-100'
          }`}
        />
        <div 
          className={`absolute inset-0 bg-cover bg-center bg-no-repeat transition-opacity duration-1000 ease-in-out bg-image-next ${
            isTransitioning ? 'opacity-100' : 'opacity-0'
          }`}
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-blue-900/40"></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-6">
        {/* Glassmorphism Form Container */}
        <div className="w-full max-w-md">
          {/* Logo/Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full mb-4">
              <img src="/logo.jpg" alt="Maggie Prep Logo" className="w-12 h-12 rounded-full object-cover" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">Maggie Prep</h1>
            <p className="text-white/80">School Management System</p>
            <p className="text-white/60 text-sm mt-2">Welcome back! Please sign in to your account.</p>
          </div>

          {/* Glassmorphism Form */}
          <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-2xl p-8 shadow-2xl glassmorphism">
            <LoginForm />
          </div>
        </div>
      </div>

      {/* Quote Display Area - Tall Rounded Rectangle */}
      <div className="absolute top-0 right-0 w-1/3 h-full hidden lg:block">
        {/* Tall rounded rectangle with soft curved edges */}
        <div className="absolute top-4 right-4 bottom-4 w-full max-w-sm">
          <div className="h-full bg-white/20 backdrop-blur-xl rounded-3xl border border-white/30 shadow-2xl p-8 flex items-center justify-center relative overflow-hidden">
            {/* Logo as background - fills the shape */}
            <div className="absolute inset-0 opacity-10">
              <img src="/logo.jpg" alt="Maggie Prep Logo" className="w-full h-full object-cover rounded-3xl" />
            </div>
            
            <div className="text-center max-w-sm relative z-10">
              {/* Quote Text */}
              <div className="space-y-4">
                <blockquote className="text-lg font-medium leading-relaxed text-blue-600">
                  "{educationalQuotes[currentQuoteIndex].split(' - ')[0]}"
                </blockquote>
                <cite className="text-sm font-medium text-blue-500">
                  — {educationalQuotes[currentQuoteIndex].split(' - ')[1]}
                </cite>
              </div>
              
              {/* Quote Indicator */}
              <div className="flex justify-center mt-6 space-x-2">
                {educationalQuotes.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index === currentQuoteIndex 
                        ? 'bg-blue-600' 
                        : 'bg-blue-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Floating decorative elements */}
        <div className="absolute top-20 right-20 w-8 h-8 bg-blue-400/30 backdrop-blur-sm rounded-full quote-circle-decoration quote-circle-decoration-delay-0"></div>
        <div className="absolute bottom-20 right-16 w-6 h-6 bg-blue-300/30 backdrop-blur-sm rounded-full quote-circle-decoration quote-circle-decoration-delay-1"></div>
        <div className="absolute top-1/2 right-8 w-4 h-4 bg-blue-500/30 backdrop-blur-sm rounded-full quote-circle-decoration quote-circle-decoration-delay-2"></div>
      </div>
    </div>
  );
}
