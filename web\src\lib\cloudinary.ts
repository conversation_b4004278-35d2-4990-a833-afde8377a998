// Client-side Cloudinary upload function
export const uploadImage = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'Maggie_Management');
    formData.append('cloud_name', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'daw47jhb0');

    fetch(`https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'daw47jhb0'}/image/upload`, {
      method: 'POST',
      body: formData,
    })
      .then(response => response.json())
      .then(data => {
        if (data.secure_url) {
          resolve(data.secure_url);
        } else {
          reject(new Error('Upload failed'));
        }
      })
      .catch(error => {
        reject(error);
      });
  });
};

// Upload image with automatic old image deletion
export const uploadImageWithReplacement = async (file: File, oldImageUrl?: string): Promise<string> => {
  try {
    // Upload new image
    const newImageUrl = await uploadImage(file);
    
    // If there's an old image, delete it
    if (oldImageUrl) {
      await deleteImageFromUrl(oldImageUrl);
    }
    
    return newImageUrl;
  } catch (error) {
    console.error('Error uploading image with replacement:', error);
    throw error;
  }
};

// Extract public ID from Cloudinary URL and delete
export const deleteImageFromUrl = async (imageUrl: string): Promise<void> => {
  try {
    // Extract public ID from Cloudinary URL
    const publicId = extractPublicIdFromUrl(imageUrl);
    if (publicId) {
      await deleteImage(publicId);
    }
  } catch (error) {
    console.error('Error deleting old image:', error);
    // Don't throw error - we don't want to fail the upload if deletion fails
  }
};

// Extract public ID from Cloudinary URL
const extractPublicIdFromUrl = (url: string): string | null => {
  try {
    // Handle different Cloudinary URL formats
    const urlPattern = /\/v\d+\/([^\/]+)\.(jpg|jpeg|png|gif|webp)/;
    const match = url.match(urlPattern);
    
    if (match) {
      return match[1]; // Return the public ID
    }
    
    // Alternative pattern for different URL formats
    const altPattern = /\/upload\/([^\/]+)\.(jpg|jpeg|png|gif|webp)/;
    const altMatch = url.match(altPattern);
    
    if (altMatch) {
      return altMatch[1];
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting public ID from URL:', error);
    return null;
  }
};

// Delete image from Cloudinary using server-side API
export const deleteImage = async (publicId: string): Promise<void> => {
  try {
    const response = await fetch('/api/cloudinary/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ publicId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete image');
    }

    const result = await response.json();
    console.log('Successfully deleted image:', publicId);
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    throw error;
  }
}; 