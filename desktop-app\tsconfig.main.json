{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "outDir": "dist/main", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/main/**/*"], "exclude": ["node_modules", "dist", "src/renderer/**/*"]}