export type NetworkStatus = 'online' | 'offline' | 'checking';

// Simple event system for browser environment
type EventCallback = (...args: any[]) => void;

class SimpleEventEmitter {
  private events: Map<string, EventCallback[]> = new Map();

  on(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }

  off(event: string, callback: EventCallback): void {
    const callbacks = this.events.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }

  removeAllListeners(): void {
    this.events.clear();
  }
}

export class NetworkMonitor extends SimpleEventEmitter {
  private status: NetworkStatus = 'checking';
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 5000; // 5 seconds
  private readonly TIMEOUT = 3000; // 3 seconds timeout for checks

  constructor() {
    super();
    this.initialize();
  }

  private initialize(): void {
    // Initial status check
    this.checkNetworkStatus();

    // Listen to browser online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
    }

    // Start periodic checks
    this.startPeriodicChecks();
  }

  private handleOnline(): void {
    console.log('🌐 Browser detected online');
    this.checkNetworkStatus();
  }

  private handleOffline(): void {
    console.log('📡 Browser detected offline');
    this.setStatus('offline');
  }

  private startPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      this.checkNetworkStatus();
    }, this.CHECK_INTERVAL);
  }

  private stopPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      // First check browser navigator.onLine
      if (typeof navigator !== 'undefined' && !navigator.onLine) {
        this.setStatus('offline');
        return;
      }

      // Try to reach Firebase (our primary service)
      const isFirebaseReachable = await this.checkFirebaseConnectivity();
      
      if (isFirebaseReachable) {
        this.setStatus('online');
      } else {
        // Try a fallback check
        const isInternetReachable = await this.checkInternetConnectivity();
        this.setStatus(isInternetReachable ? 'online' : 'offline');
      }

    } catch (error) {
      console.warn('⚠️ Network check failed:', error);
      this.setStatus('offline');
    }
  }

  private async checkFirebaseConnectivity(): Promise<boolean> {
    try {
      // Try to reach Firebase Firestore
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch('https://firestore.googleapis.com/', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.ok || response.status === 404; // 404 is expected for HEAD request
      
    } catch (error) {
      return false;
    }
  }

  private async checkInternetConnectivity(): Promise<boolean> {
    try {
      // Fallback: Try to reach a reliable service
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.ok;
      
    } catch (error) {
      return false;
    }
  }

  private setStatus(newStatus: NetworkStatus): void {
    if (this.status !== newStatus) {
      const previousStatus = this.status;
      this.status = newStatus;

      console.log(`🔄 Network status changed: ${previousStatus} → ${newStatus}`);

      // Emit events
      this.emit('status-change', newStatus);
      
      if (newStatus === 'online') {
        this.emit('online');
      } else if (newStatus === 'offline') {
        this.emit('offline');
      }
    }
  }

  // Public methods
  getStatus(): NetworkStatus {
    return this.status;
  }

  isOnline(): boolean {
    return this.status === 'online';
  }

  isOffline(): boolean {
    return this.status === 'offline';
  }

  async forceCheck(): Promise<NetworkStatus> {
    await this.checkNetworkStatus();
    return this.status;
  }

  // Event listener helpers with proper typing
  onStatusChange(callback: (status: NetworkStatus) => void): () => void {
    this.on('status-change', callback);
    return () => this.off('status-change', callback);
  }

  onOnline(callback: () => void): () => void {
    this.on('online', callback);
    return () => this.off('online', callback);
  }

  onOffline(callback: () => void): () => void {
    this.on('offline', callback);
    return () => this.off('offline', callback);
  }

  // Cleanup
  destroy(): void {
    this.stopPeriodicChecks();
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline.bind(this));
      window.removeEventListener('offline', this.handleOffline.bind(this));
    }

    this.removeAllListeners();
  }
}

// Singleton instance
let networkMonitorInstance: NetworkMonitor | null = null;

export function getNetworkMonitor(): NetworkMonitor {
  if (!networkMonitorInstance) {
    networkMonitorInstance = new NetworkMonitor();
  }
  return networkMonitorInstance;
}

// React hook for network status
export function useNetworkStatus() {
  const [status, setStatus] = useState<NetworkStatus>('checking');
  const [isOnline, setIsOnline] = useState<boolean>(false);

  useEffect(() => {
    const monitor = getNetworkMonitor();
    
    // Set initial status
    setStatus(monitor.getStatus());
    setIsOnline(monitor.isOnline());

    // Listen for changes
    const unsubscribe = monitor.onStatusChange((newStatus) => {
      setStatus(newStatus);
      setIsOnline(newStatus === 'online');
    });

    return unsubscribe;
  }, []);

  return {
    status,
    isOnline,
    isOffline: status === 'offline',
    isChecking: status === 'checking',
    forceCheck: () => getNetworkMonitor().forceCheck()
  };
}

// We need to import useState and useEffect for the hook
import { useState, useEffect } from 'react';
