import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import { collection, query, orderBy, onSnapshot, getDocs, addDoc, doc, setDoc, updateDoc, arrayUnion } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { Student, Gender, EnrollmentStatus } from '../lib/types'
import {
  getCurrentAcademicYear,
  generateDefaultEmail,
  getDefaultPassword,
  createUserWithDefaults,
  createStudentWithDefaults,
  validateStudentRegistration,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from '../lib/business-logic'
import { useNotifications } from '../components/providers/notification-provider'

export default function StudentRegistration() {
  const { user } = useAuth()
  const { notify } = useNotifications()
  const [showAddForm, setShowAddForm] = useState(false)
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [formMode, setFormMode] = useState<'add' | 'view' | 'edit'>('add')
  const [isReadOnly, setIsReadOnly] = useState(false)
  const [formData, setFormData] = useState({
    // Student Information (matching web app exactly)
    name: '',
    dob: '',
    gender: 'male', // male, female, other
    class: '',
    parentId: '', // Will be set after parent creation
    enrollmentStatus: 'active', // active, on_hold, inactive, completed
    enrollmentDate: new Date().toISOString().split('T')[0],
    academicYear: new Date().getFullYear().toString(),
    previousSchool: '',
    address: '',

    // Emergency Contact (object structure matching web app)
    emergencyContact: {
      name: '',
      relationship: '',
      phone: ''
    },

    // Medical Information
    medicalInfo: '',

    // Parent Information (for creating parent account)
    parentName: '',
    parentEmail: '',
    parentPhone: '',
    parentAddress: '',
    fatherName: '',
    motherName: '',
    fatherPhone: '',
    motherPhone: ''
  })

  // Image upload state (matching web app)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  // Real-time listener for students data
  useEffect(() => {
    if (!user || user.role !== 'admin') return;

    const studentsCollection = collection(db, 'students');
    const studentsQuery = query(studentsCollection, orderBy('dateAdded', 'desc'));

    // Use real-time listener for instant updates
    const unsubscribe = onSnapshot(studentsQuery, async (snapshot) => {
      const studentsData = await Promise.all(snapshot.docs.map(async (doc) => {
        const student = { id: doc.id, ...doc.data() } as Student;
        const resultsCollection = collection(db, `students/${student.id}/results`);
        const resultsSnapshot = await getDocs(resultsCollection);
        student.results = resultsSnapshot.docs.map(resDoc => ({ id: resDoc.id, ...resDoc.data() } as any));
        return student;
      }));

      setStudents(studentsData);
      setLoading(false);
    }, (error) => {
      console.error('❌ Error in students listener:', error);
      setLoading(false);
    });

    return unsubscribe;
  }, [user]);

  // Handle View Student - Opens the same form in read-only mode
  const handleViewStudent = (student: Student) => {
    setSelectedStudent(student);
    setFormMode('view');
    setIsReadOnly(true);

    // Populate form with student data
    setFormData({
      name: student.name,
      dob: student.dob,
      gender: student.gender,
      class: student.class,
      parentId: student.parentId,
      enrollmentStatus: student.enrollmentStatus,
      enrollmentDate: student.enrollmentDate,
      academicYear: student.academicYear || '',
      previousSchool: student.previousSchool || '',
      address: student.address || '',
      emergencyContact: student.emergencyContact || {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalInfo: student.medicalInfo || '',
      parentName: student.parentId, // We'll need to fetch parent details
      parentEmail: '',
      parentPhone: '',
      parentAddress: '',
      fatherName: '',
      motherName: '',
      fatherPhone: '',
      motherPhone: ''
    });

    setShowAddForm(true);
  };

  // Handle Edit Student - Opens the same form in edit mode
  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
    setFormMode('edit');
    setIsReadOnly(false);

    // Populate form with student data
    setFormData({
      name: student.name,
      dob: student.dob,
      gender: student.gender,
      class: student.class,
      parentId: student.parentId,
      enrollmentStatus: student.enrollmentStatus,
      enrollmentDate: student.enrollmentDate,
      academicYear: student.academicYear || '',
      previousSchool: student.previousSchool || '',
      address: student.address || '',
      emergencyContact: student.emergencyContact || {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalInfo: student.medicalInfo || '',
      parentName: student.parentId, // We'll need to fetch parent details
      parentEmail: '',
      parentPhone: '',
      parentAddress: '',
      fatherName: '',
      motherName: '',
      fatherPhone: '',
      motherPhone: ''
    });

    setShowAddForm(true);
  };

  // Handle Add New Student - Opens the form in add mode
  const handleAddStudent = () => {
    setSelectedStudent(null);
    setFormMode('add');
    setIsReadOnly(false);

    // Reset form to empty
    setFormData({
      name: '',
      dob: '',
      gender: 'male',
      class: '',
      parentId: '',
      enrollmentStatus: 'active',
      enrollmentDate: new Date().toISOString().split('T')[0],
      academicYear: getCurrentAcademicYear(),
      previousSchool: '',
      address: '',
      emergencyContact: {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalInfo: '',
      parentName: '',
      parentEmail: '',
      parentPhone: '',
      parentAddress: '',
      fatherName: '',
      motherName: '',
      fatherPhone: '',
      motherPhone: ''
    });

    setShowAddForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (formMode === 'view') return; // No submission in view mode

    try {
      if (formMode === 'edit' && selectedStudent) {
        // Update existing student
        await updateDoc(doc(db, 'students', selectedStudent.id), {
          name: formData.name,
          dob: formData.dob,
          gender: formData.gender,
          class: formData.class,
          enrollmentStatus: formData.enrollmentStatus,
          enrollmentDate: formData.enrollmentDate,
          academicYear: formData.academicYear,
          previousSchool: formData.previousSchool,
          address: formData.address,
          emergencyContact: formData.emergencyContact,
          medicalInfo: formData.medicalInfo,
          updatedAt: new Date().toISOString()
        });

        console.log('✅ Student updated successfully:', selectedStudent.id);
        notify.success(SUCCESS_MESSAGES.UPDATE_SUCCESS, `${formData.name} has been updated.`);

        // No need to manually refresh - real-time listener will update automatically
        setShowAddForm(false);
        return;
      }

      // Add new student (existing logic)
      // First, create parent account (matching web app logic)
      const parentEmail = formData.parentEmail || generateDefaultEmail(formData.parentName, 'parent')
      const defaultPassword = getDefaultPassword()

      // Create parent user data (matching web app User interface)
      const parentData = createUserWithDefaults({
        name: formData.parentName,
        email: parentEmail,
        role: 'parent' as const,
        phone: formData.parentPhone,
        address: formData.parentAddress,
        fatherName: formData.fatherName,
        motherName: formData.motherName,
        fatherPhone: formData.fatherPhone,
        motherPhone: formData.motherPhone,
        password: defaultPassword
      })

      // Handle image upload (matching web app logic)
      let avatarUrl = ""
      if (selectedFile) {
        try {
          // In a real app, you would upload to Cloudinary or Firebase Storage
          // For now, we'll create a placeholder URL
          avatarUrl = URL.createObjectURL(selectedFile)
          console.log('Image selected for upload:', selectedFile.name)
        } catch (error) {
          console.error('Error processing image:', error)
          alert('Maggie Preparatory School - Image Processing\n\nError processing image. Student will be created without photo.')
        }
      }

      // Create student data (matching web app Student interface exactly)
      const studentData = createStudentWithDefaults({
        name: formData.name,
        dob: formData.dob,
        gender: formData.gender as Gender,
        class: formData.class,
        parentId: parentEmail, // Use parent email as ID
        enrollmentStatus: formData.enrollmentStatus as EnrollmentStatus,
        enrollmentDate: formData.enrollmentDate,
        academicYear: formData.academicYear,
        previousSchool: formData.previousSchool,
        address: formData.address,
        emergencyContact: formData.emergencyContact,
        medicalInfo: formData.medicalInfo,
        avatarUrl: avatarUrl, // Include avatar URL
      })

      console.log('Creating parent account:', parentData)
      console.log('Registering student:', studentData)

      // 1. Create parent document in Firestore
      await setDoc(doc(db, "users", parentEmail), parentData);

      // 2. Create student document in Firestore
      const studentRef = await addDoc(collection(db, "students"), studentData);

      // 3. Update parent document with student reference
      await updateDoc(doc(db, "users", parentEmail), {
        childrenIds: arrayUnion(studentRef.id)
      });

      console.log('✅ Student registered successfully:', studentRef.id);

      notify.success(
        SUCCESS_MESSAGES.REGISTRATION_SUCCESS,
        `${formData.name} has been registered. Parent credentials: ${parentEmail} / ${defaultPassword}`
      );

      // Reset form
      setFormData({
        name: '',
        dob: '',
        gender: 'male',
        class: '',
        parentId: '',
        enrollmentStatus: 'active',
        enrollmentDate: new Date().toISOString().split('T')[0],
        academicYear: getCurrentAcademicYear(),
        previousSchool: '',
        address: '',
        emergencyContact: {
          name: '',
          relationship: '',
          phone: ''
        },
        medicalInfo: '',
        parentName: '',
        parentEmail: '',
        parentPhone: '',
        parentAddress: '',
        fatherName: '',
        motherName: '',
        fatherPhone: '',
        motherPhone: ''
      })
      setShowAddForm(false)

      // No need to manually refresh - real-time listener will update automatically

    } catch (error) {
      console.error('Error registering student:', error)
      notify.error(ERROR_MESSAGES.REGISTRATION_FAILED, 'Please check your data and try again.')
    }
  }

  // Check if user is admin
  if (user?.role !== 'admin') {
    return (
      <div className="p-6 flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Access Denied</h3>
            <p className="text-sm text-gray-600">
              You don't have permission to access this page.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Student Registration</h1>
          <p className="text-gray-600">Register new students and manage enrollment</p>
        </div>
        <button
          type="button"
          onClick={handleAddStudent}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>Register Student</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Registered</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-green-600">{students.filter(s => s.enrollmentStatus === EnrollmentStatus.Active).length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-orange-600">{students.filter(s => s.enrollmentStatus === EnrollmentStatus.OnHold).length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-purple-600">3</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Registration Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl w-full max-w-4xl border border-white/20 shadow-2xl h-[85vh] flex flex-col">
            {/* Fixed Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-xl font-semibold text-gray-900">
                {formMode === 'add' && 'Register New Student'}
                {formMode === 'view' && `View Student: ${formData.name}`}
                {formMode === 'edit' && `Edit Student: ${formData.name}`}
              </h2>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                aria-label="Close modal"
                title="Close modal"
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form id="student-registration-form" onSubmit={handleSubmit} className="space-y-6">
              {/* Student Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Student Information</h3>

                {/* Student Photo Upload */}
                <div className="mb-6">
                  <label htmlFor="student-photo-input" className="block text-sm font-medium text-gray-700 mb-2">Student Photo (Optional)</label>
                  <div className="flex items-center space-x-4">
                    {/* Image Preview */}
                    <div className="w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
                      {imagePreview ? (
                        <img
                          src={imagePreview}
                          alt="Student preview"
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : (
                        <svg className="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      )}
                    </div>

                    {/* File Input */}
                    <div className="flex-1">
                      <input
                        id="student-photo-input"
                        type="file"
                        accept="image/*"
                        title="Upload student photo"
                        placeholder="Choose a photo file"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          setSelectedFile(file || null)
                          if (file) {
                            setImagePreview(URL.createObjectURL(file))
                          } else {
                            setImagePreview(null)
                          }
                        }}
                        className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      <p className="text-xs text-gray-500 mt-1">Upload a photo of the student (JPG, PNG, GIF up to 5MB)</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter student's full name"
                      className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                        isReadOnly
                          ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                          : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                      }`}
                      readOnly={isReadOnly}
                      required={!isReadOnly}
                    />
                  </div>
                  <div>
                    <label htmlFor="dob-input" className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                    <input
                      id="dob-input"
                      type="date"
                      value={formData.dob}
                      onChange={(e) => setFormData({...formData, dob: e.target.value})}
                      className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                        isReadOnly
                          ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                          : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                      }`}
                      title="Student's date of birth"
                      placeholder="Select date of birth"
                      readOnly={isReadOnly}
                      required={!isReadOnly}
                    />
                  </div>
                  <div>
                    <label htmlFor="gender-select" className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <select
                      id="gender-select"
                      value={formData.gender}
                      onChange={(e) => setFormData({...formData, gender: e.target.value})}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      title="Select student's gender"
                      required
                    >
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="class-select" className="block text-sm font-medium text-gray-700 mb-2">Class</label>
                    <select
                      id="class-select"
                      value={formData.class}
                      onChange={(e) => setFormData({...formData, class: e.target.value})}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      title="Select student's class"
                      required
                    >
                      <option value="">Select Class</option>
                      <option value="Nursery 1">Nursery 1</option>
                      <option value="Nursery 2">Nursery 2</option>
                      <option value="KG 1">KG 1</option>
                      <option value="KG 2">KG 2</option>
                      <option value="Basic 1">Basic 1</option>
                      <option value="Basic 2">Basic 2</option>
                      <option value="Basic 3">Basic 3</option>
                      <option value="Basic 4">Basic 4</option>
                      <option value="Basic 5">Basic 5</option>
                      <option value="Basic 6">Basic 6</option>
                      <option value="Basic 7">Basic 7</option>
                      <option value="Basic 8">Basic 8</option>
                      <option value="Basic 9">Basic 9</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="enrollment-status-select" className="block text-sm font-medium text-gray-700 mb-2">Enrollment Status</label>
                    <select
                      id="enrollment-status-select"
                      value={formData.enrollmentStatus}
                      onChange={(e) => setFormData({...formData, enrollmentStatus: e.target.value})}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      title="Select student's enrollment status"
                      required
                    >
                      <option value="active">Active</option>
                      <option value="on_hold">On Hold</option>
                      <option value="inactive">Inactive</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="enrollment-date-input" className="block text-sm font-medium text-gray-700 mb-2">Enrollment Date</label>
                    <input
                      id="enrollment-date-input"
                      type="date"
                      value={formData.enrollmentDate}
                      onChange={(e) => setFormData({...formData, enrollmentDate: e.target.value})}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      title="Select enrollment date"
                      placeholder="Select enrollment date"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                    <input
                      type="text"
                      value={formData.academicYear}
                      onChange={(e) => setFormData({...formData, academicYear: e.target.value})}
                      placeholder="2024"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Previous School (Optional)</label>
                    <input
                      type="text"
                      value={formData.previousSchool}
                      onChange={(e) => setFormData({...formData, previousSchool: e.target.value})}
                      placeholder="Enter previous school name"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Address (Optional)</label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    rows={3}
                    placeholder="Enter student's address"
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              {/* Emergency Contact */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Name</label>
                    <input
                      type="text"
                      value={formData.emergencyContact.name}
                      onChange={(e) => setFormData({
                        ...formData,
                        emergencyContact: {...formData.emergencyContact, name: e.target.value}
                      })}
                      placeholder="Emergency contact name"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Relationship</label>
                    <input
                      type="text"
                      value={formData.emergencyContact.relationship}
                      onChange={(e) => setFormData({
                        ...formData,
                        emergencyContact: {...formData.emergencyContact, relationship: e.target.value}
                      })}
                      placeholder="e.g., Uncle, Aunt, Guardian"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input
                      type="tel"
                      value={formData.emergencyContact.phone}
                      onChange={(e) => setFormData({
                        ...formData,
                        emergencyContact: {...formData.emergencyContact, phone: e.target.value}
                      })}
                      placeholder="Emergency contact phone"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>
              </div>

              {/* Medical Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Medical Information (Optional)</label>
                  <textarea
                    value={formData.medicalInfo}
                    onChange={(e) => setFormData({...formData, medicalInfo: e.target.value})}
                    rows={3}
                    placeholder="Any medical conditions, allergies, or important medical information"
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              {/* Parent Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Parent/Guardian Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Parent/Guardian Name</label>
                    <input
                      type="text"
                      value={formData.parentName}
                      onChange={(e) => setFormData({...formData, parentName: e.target.value})}
                      placeholder="Full name of parent/guardian"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Parent Email (Optional)</label>
                    <input
                      type="email"
                      value={formData.parentEmail}
                      onChange={(e) => setFormData({...formData, parentEmail: e.target.value})}
                      placeholder="Will auto-generate if empty"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Parent Phone</label>
                    <input
                      type="tel"
                      value={formData.parentPhone}
                      onChange={(e) => setFormData({...formData, parentPhone: e.target.value})}
                      placeholder="Parent phone number"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Father's Name (Optional)</label>
                    <input
                      type="text"
                      value={formData.fatherName}
                      onChange={(e) => setFormData({...formData, fatherName: e.target.value})}
                      placeholder="Father's full name"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mother's Name (Optional)</label>
                    <input
                      type="text"
                      value={formData.motherName}
                      onChange={(e) => setFormData({...formData, motherName: e.target.value})}
                      placeholder="Mother's full name"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Father's Phone (Optional)</label>
                    <input
                      type="tel"
                      value={formData.fatherPhone}
                      onChange={(e) => setFormData({...formData, fatherPhone: e.target.value})}
                      placeholder="Father's phone number"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mother's Phone (Optional)</label>
                    <input
                      type="tel"
                      value={formData.motherPhone}
                      onChange={(e) => setFormData({...formData, motherPhone: e.target.value})}
                      placeholder="Mother's phone number"
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Parent Address (Optional)</label>
                  <textarea
                    value={formData.parentAddress}
                    onChange={(e) => setFormData({...formData, parentAddress: e.target.value})}
                    rows={3}
                    placeholder="Parent's address"
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>
            </form>
            </div>

            {/* Fixed Footer */}
            <div className="p-6 border-t border-white/20 bg-white/50">
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl hover:bg-gray-300 transition-all duration-200"
                >
                  {formMode === 'view' ? 'Close' : 'Cancel'}
                </button>
                {formMode !== 'view' && (
                  <button
                    type="submit"
                    form="student-registration-form"
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
                  >
                    {formMode === 'add' ? 'Register Student' : 'Update Student'}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Registrations */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-white/10">
          <h3 className="text-lg font-semibold text-gray-900">Recent Registrations</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50/50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Added</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200/50">
              {students.map((student) => (
                <tr key={student.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                        {student.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{student.name}</div>
                        <div className="text-sm text-gray-500">
                          {new Date().getFullYear() - new Date(student.dob).getFullYear()} years old
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {student.class}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{student.parentId}</div>
                    <div className="text-sm text-gray-500">{student.emergencyContact?.phone || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      student.enrollmentStatus === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {student.enrollmentStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {student.dateAdded}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {/* View Button - Opens original form in read-only mode */}
                      <button
                        type="button"
                        onClick={() => handleViewStudent(student)}
                        aria-label={`View ${student.name}`}
                        title={`View ${student.name}`}
                        className="text-blue-600 hover:text-blue-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>

                      {/* Edit Button - Opens original form in edit mode */}
                      <button
                        type="button"
                        onClick={() => handleEditStudent(student)}
                        aria-label={`Edit ${student.name}`}
                        title={`Edit ${student.name}`}
                        className="text-green-600 hover:text-green-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>

                      {/* Approve Button - For enrollment approval */}
                      <button
                        type="button"
                        aria-label={`Approve ${student.name}`}
                        title={`Approve ${student.name}`}
                        className="text-purple-600 hover:text-purple-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
