{"name": "kerber<PERSON>", "version": "2.2.2", "description": "Kerberos library for Node.js", "main": "lib/index.js", "files": ["lib", "src", "binding.gyp", "HISTORY.md", "README.md"], "repository": {"type": "git", "url": "https://github.com/mongodb-js/kerberos.git"}, "keywords": ["kerber<PERSON>", "security", "authentication"], "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "bugs": {"url": "https://jira.mongodb.org/projects/NODE/issues/"}, "dependencies": {"node-addon-api": "^6.1.0", "prebuild-install": "^7.1.2"}, "devDependencies": {"@types/node": "^22.2.0", "chai": "^4.4.1", "chai-string": "^1.5.0", "chalk": "^4.1.2", "clang-format": "^1.8.0", "dmd-clear": "^0.1.2", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jsdoc-to-markdown": "^9.0.5", "mocha": "^10.7.3", "mongodb": "^6.8.0", "node-gyp": "^10.1.0", "prebuild": "^13.0.0", "prettier": "^3.3.3", "request": "^2.88.2"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "scripts": {"install": "prebuild-install --runtime napi || node-gyp rebuild", "format-cxx": "clang-format -i 'src/**/*'", "format-js": "ESLINT_USE_FLAT_CONFIG=false eslint lib test --fix", "check:lint": "ESLINT_USE_FLAT_CONFIG=false eslint lib test", "precommit": "check-clang-format", "docs": "jsdoc2md --template etc/README.hbs --plugin dmd-clear --files lib/kerberos.js > README.md", "test": "mocha 'test/*_tests.js'", "prebuild": "prebuild --runtime napi --strip --verbose --all"}, "engines": {"node": ">=12.9.0"}, "binary": {"napi_versions": [4]}, "license": "Apache-2.0", "readmeFilename": "README.md"}