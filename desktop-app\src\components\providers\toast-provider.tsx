import React, { createContext, useContext, useState, useCallback } from 'react'
import { DynamicIslandToast, DynamicIslandContainer } from '../ui/toast'

interface ToastProps {
  title: string
  description?: string
  variant?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

interface ToastState extends ToastProps {
  id: string
  onClose: () => void
}

interface DynamicIslandContextType {
  toast: (props: ToastProps) => void
  dismissAll: () => void
}

const DynamicIslandContext = createContext<DynamicIslandContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<ToastState[]>([])

  const toast = useCallback((props: ToastProps) => {
    const id = Math.random().toString(36).substring(2, 11)

    // Dismiss any existing toast first (only one at a time for Dynamic Island)
    setToasts([])

    const newToast: ToastState = {
      ...props,
      id,
      onClose: () => {
        setToasts(prev => prev.filter(t => t.id !== id))
      }
    }

    // Small delay to ensure clean transition
    setTimeout(() => {
      setToasts([newToast])
    }, 100)
  }, [])

  const dismissAll = useCallback(() => {
    setToasts([])
  }, [])

  return (
    <DynamicIslandContext.Provider value={{ toast, dismissAll }}>
      {children}
      <DynamicIslandContainer>
        {toasts.map((toastProps) => (
          <DynamicIslandToast key={toastProps.id} {...toastProps} />
        ))}
      </DynamicIslandContainer>
    </DynamicIslandContext.Provider>
  )
}

export function useToastContext() {
  const context = useContext(DynamicIslandContext)
  if (context === undefined) {
    throw new Error('useToastContext must be used within a ToastProvider')
  }
  return context
}
