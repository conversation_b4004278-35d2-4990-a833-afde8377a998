import { DatabaseService, getDatabaseService, ConnectionStatus } from './database-service';
import { SyncService, getSyncService, SyncResult } from './sync-service';
import { OfflineAuthService, getOfflineAuthService } from './offline-auth';
import { NetworkMonitor, getNetworkMonitor, NetworkStatus } from './network-monitor';
import { User } from './types';

// Types
export interface OfflineManagerStatus {
  network: NetworkStatus;
  database: ConnectionStatus;
  isOnline: boolean;
  canWorkOffline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  offlineTimeRemaining: number;
}

export interface OfflineManagerConfig {
  mongoUrl?: string;
  mongoDatabase?: string;
  maxOfflineDays?: number;
  autoSync?: boolean;
  syncInterval?: number;
}

// Offline Manager - Main orchestrator
export class OfflineManager {
  private databaseService: DatabaseService;
  private syncService: SyncService;
  private authService: OfflineAuthService;
  private networkMonitor: NetworkMonitor;
  private config: Required<OfflineManagerConfig>;
  private isInitialized: boolean = false;
  private lastSyncTime: Date | null = null;
  private autoSyncInterval: NodeJS.Timeout | null = null;

  constructor(config: OfflineManagerConfig = {}) {
    // Set default configuration
    this.config = {
      mongoUrl: config.mongoUrl || 'mongodb://localhost:27017',
      mongoDatabase: config.mongoDatabase || 'maggie_school_offline',
      maxOfflineDays: config.maxOfflineDays || 7,
      autoSync: config.autoSync !== false, // Default to true
      syncInterval: config.syncInterval || 30000 // 30 seconds
    };

    // Initialize services
    this.databaseService = getDatabaseService({
      mongoUrl: this.config.mongoUrl,
      mongoDatabase: this.config.mongoDatabase,
      syncEnabled: true
    });

    this.syncService = getSyncService(this.databaseService);
    
    this.authService = getOfflineAuthService({
      maxOfflineDays: this.config.maxOfflineDays
    });

    this.networkMonitor = getNetworkMonitor();

    this.setupEventListeners();
  }

  // Initialization
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ OfflineManager already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing OfflineManager...');

      // Step 1: Connect to MongoDB
      await this.databaseService.connect();
      console.log('✅ Database connected');

      // Step 2: Check network status
      const networkStatus = await this.networkMonitor.forceCheck();
      console.log('📡 Network status:', networkStatus);

      // Step 3: Update database connection status based on network
      this.databaseService.setConnectionStatus(
        networkStatus === 'online' ? 'online' : 'offline'
      );

      // Step 4: Perform initial sync if online
      if (networkStatus === 'online') {
        console.log('🔄 Performing initial sync...');
        const syncResult = await this.syncService.performFullSync();
        this.lastSyncTime = new Date();
        console.log('✅ Initial sync completed:', syncResult);
      }

      // Step 5: Start auto-sync if enabled
      if (this.config.autoSync) {
        this.startAutoSync();
      }

      this.isInitialized = true;
      console.log('✅ OfflineManager initialized successfully');

    } catch (error) {
      console.error('❌ OfflineManager initialization failed:', error);
      throw new Error(`Offline manager initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down OfflineManager...');

    // Stop auto-sync
    this.stopAutoSync();

    // Disconnect from database
    await this.databaseService.disconnect();

    // Cleanup network monitor
    this.networkMonitor.destroy();

    this.isInitialized = false;
    console.log('✅ OfflineManager shut down');
  }

  // Event Listeners
  private setupEventListeners(): void {
    // Network status changes
    this.networkMonitor.onOnline(() => {
      console.log('🌐 Network online - updating database status');
      this.databaseService.setConnectionStatus('online');
      
      if (this.config.autoSync) {
        this.performSync();
      }
    });

    this.networkMonitor.onOffline(() => {
      console.log('📡 Network offline - switching to offline mode');
      this.databaseService.setConnectionStatus('offline');
    });
  }

  // Sync Management
  async performSync(): Promise<SyncResult> {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }

    if (!this.networkMonitor.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    try {
      console.log('🔄 Starting manual sync...');
      const result = await this.syncService.performFullSync();
      this.lastSyncTime = new Date();
      console.log('✅ Manual sync completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Manual sync failed:', error);
      throw error;
    }
  }

  private startAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
    }

    this.autoSyncInterval = setInterval(async () => {
      if (this.networkMonitor.isOnline() && !this.syncService.isSyncInProgress()) {
        try {
          await this.performSync();
        } catch (error) {
          console.error('❌ Auto-sync failed:', error);
        }
      }
    }, this.config.syncInterval);

    console.log(`⏰ Auto-sync started (interval: ${this.config.syncInterval}ms)`);
  }

  private stopAutoSync(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
      this.autoSyncInterval = null;
      console.log('⏹️ Auto-sync stopped');
    }
  }

  // Database Operations (Proxy to DatabaseService)
  async create(collection: string, data: any): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }
    return this.databaseService.create(collection, data);
  }

  async read(collection: string, query: any = {}): Promise<any[]> {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }
    return this.databaseService.read(collection, query);
  }

  async update(collection: string, id: string, data: any): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }
    return this.databaseService.update(collection, id, data);
  }

  async delete(collection: string, id: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }
    return this.databaseService.delete(collection, id);
  }

  subscribe(collection: string, callback: (data: any[]) => void): () => void {
    if (!this.isInitialized) {
      throw new Error('OfflineManager not initialized');
    }
    return this.databaseService.subscribe(collection, callback);
  }

  // Authentication Methods (Proxy to OfflineAuthService)
  async authenticateOffline(): Promise<User | null> {
    return this.authService.authenticateOffline();
  }

  async canWorkOffline(): Promise<boolean> {
    return this.authService.canWorkOffline();
  }

  async getOfflineTimeRemaining(): Promise<number> {
    return this.authService.getOfflineTimeRemaining();
  }

  async requiresOnlineAuth(): Promise<boolean> {
    return this.authService.requiresOnlineAuth();
  }

  clearAuthSession(): void {
    this.authService.clearCachedSession();
  }

  // Status and Information
  async getStatus(): Promise<OfflineManagerStatus> {
    const canWorkOffline = await this.canWorkOffline();
    const offlineTimeRemaining = await this.getOfflineTimeRemaining();

    return {
      network: this.networkMonitor.getStatus(),
      database: this.databaseService.getConnectionStatus(),
      isOnline: this.networkMonitor.isOnline(),
      canWorkOffline,
      isSyncing: this.syncService.isSyncInProgress(),
      lastSyncTime: this.lastSyncTime,
      offlineTimeRemaining
    };
  }

  getConfig(): Required<OfflineManagerConfig> {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<OfflineManagerConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // Handle auto-sync changes
    if (newConfig.autoSync !== undefined) {
      if (newConfig.autoSync && !oldConfig.autoSync) {
        this.startAutoSync();
      } else if (!newConfig.autoSync && oldConfig.autoSync) {
        this.stopAutoSync();
      }
    }

    // Handle sync interval changes
    if (newConfig.syncInterval && newConfig.syncInterval !== oldConfig.syncInterval) {
      if (this.config.autoSync) {
        this.startAutoSync(); // Restart with new interval
      }
    }

    console.log('⚙️ Configuration updated:', this.config);
  }

  // Utility Methods
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  async testConnection(): Promise<{
    mongodb: boolean;
    firebase: boolean;
    network: boolean;
  }> {
    const results = {
      mongodb: false,
      firebase: false,
      network: false
    };

    try {
      // Test MongoDB connection
      if (this.databaseService) {
        await this.databaseService.read('users', {});
        results.mongodb = true;
      }
    } catch (error) {
      console.warn('MongoDB connection test failed:', error);
    }

    try {
      // Test network connection
      results.network = this.networkMonitor.isOnline();
    } catch (error) {
      console.warn('Network test failed:', error);
    }

    try {
      // Test Firebase connection (if online)
      if (results.network) {
        // This would be implemented based on your Firebase setup
        results.firebase = true; // Placeholder
      }
    } catch (error) {
      console.warn('Firebase connection test failed:', error);
    }

    return results;
  }
}

// Singleton instance
let offlineManagerInstance: OfflineManager | null = null;

export function getOfflineManager(config?: OfflineManagerConfig): OfflineManager {
  if (!offlineManagerInstance) {
    offlineManagerInstance = new OfflineManager(config);
  }
  return offlineManagerInstance;
}

// React Hook for Offline Manager
export function useOfflineManager() {
  const [status, setStatus] = useState<OfflineManagerStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const manager = getOfflineManager();

  useEffect(() => {
    let mounted = true;

    const updateStatus = async () => {
      try {
        const currentStatus = await manager.getStatus();
        if (mounted) {
          setStatus(currentStatus);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Failed to get offline manager status:', error);
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    // Initial status
    updateStatus();

    // Update status periodically
    const interval = setInterval(updateStatus, 5000); // Every 5 seconds

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, [manager]);

  return {
    status,
    isLoading,
    manager,
    performSync: () => manager.performSync(),
    testConnection: () => manager.testConnection()
  };
}

// Import React hooks
import { useState, useEffect } from 'react';
