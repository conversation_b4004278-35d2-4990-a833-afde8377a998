"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  StudentResult, 
  ClassLevel, 
  Term,
  ResultSummary
} from "@/lib/results-types";
import { 
  getGradeColor,
  formatResultSummary,
  calculateOverallGrade,
  sortResults
} from "@/lib/results-utils";
import { getCurrentAcademicYear } from "@/lib/business-logic";
import { Student } from "@/lib/types";
import { 
  collection, 
  query,
  where,
  orderBy,
  getDocs,
  onSnapshot
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Search, Download, FileText, TrendingUp, Award } from "lucide-react";

export default function ViewResultsPage() {
  const { user } = useAuth();

  // Restrict access for parent users - redirect them to My Children page
  if (user?.role === 'parent') {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-blue-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">View Results in My Children</h3>
          <p className="text-gray-600 mb-6">
            Results viewing has been moved to the My Children page for a better experience with filtering and detailed views.
          </p>
          <a
            href="/my-children"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to My Children
          </a>
        </div>
      </div>
    );
  }

  const [students, setStudents] = useState<Student[]>([]);
  const [results, setResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [selectedTerm, setSelectedTerm] = useState<Term | "all">("all");
  const [selectedYear, setSelectedYear] = useState(getCurrentAcademicYear());
  const [searchTerm, setSearchTerm] = useState("");
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [modalStudent, setModalStudent] = useState<Student | null>(null);

  // Load data based on user role
  useEffect(() => {
    const loadData = async () => {
      if (!user) return;

      try {
        let studentsData: Student[] = [];
        
        if (user.role === 'parent') {
          // Parents can only see their children
          if (user.childrenIds && user.childrenIds.length > 0) {
            const studentsPromises = user.childrenIds.map(async (childId) => {
              const studentQuery = query(
                collection(db, "students"),
                where("__name__", "==", childId)
              );
              const snapshot = await getDocs(studentQuery);
              return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })) as Student[];
            });
            
            const studentsArrays = await Promise.all(studentsPromises);
            studentsData = studentsArrays.flat();
          }
        } else if (user.role === 'teacher') {
          // Teachers can see students from their assigned classes
          if (user.assignedClasses && user.assignedClasses.length > 0) {
            const studentQuery = query(
              collection(db, "students"),
              where("class", "in", user.assignedClasses),
              orderBy("name")
            );
            const snapshot = await getDocs(studentQuery);
            studentsData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as Student[];
          }
        } else if (user.role === 'admin') {
          // Admins can see all students
          const studentQuery = query(collection(db, "students"), orderBy("name"));
          const snapshot = await getDocs(studentQuery);
          studentsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Student[];
        }

        setStudents(studentsData);

        // Load results for accessible students
        if (studentsData.length > 0) {
          const studentIds = studentsData.map(s => s.id);
          const resultsQuery = query(
            collection(db, "results"),
            where("studentId", "in", studentIds.slice(0, 10)), // Firestore 'in' limit
            orderBy("createdAt", "desc")
          );
          
          const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
            const resultsData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as StudentResult[];
            setResults(resultsData);
          });

          setLoading(false);
          return () => unsubscribeResults();
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error("Error loading data:", error);
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Helper functions
  const handleViewResults = (student: Student) => {
    setModalStudent(student);
    setShowResultsModal(true);
  };

  const closeModal = () => {
    setShowResultsModal(false);
    setModalStudent(null);
  };

  const getStudentResults = (studentId: string) => {
    if (!Array.isArray(results)) {
      return [];
    }
    return results.filter(r => r.studentId === studentId);
  };

  const getAllStudentResults = (studentId: string) => {
    if (!Array.isArray(results)) {
      return [];
    }
    return results.filter(r => r.studentId === studentId);
  };

  const getStudentSummary = (studentId: string) => {
    const studentResults = getStudentResults(studentId);
    if (!Array.isArray(studentResults) || studentResults.length === 0) return null;

    try {
      const totalMarks = studentResults.reduce((sum, r) => sum + (r.marks || 0), 0);
      const totalPossible = studentResults.reduce((sum, r) => sum + (r.totalMarks || 0), 0);
      const averagePercentage = totalPossible > 0 ? Math.round((totalMarks / totalPossible) * 100) : 0;
      const passedSubjects = studentResults.filter(r => r.isPassed).length;

      return {
        totalSubjects: studentResults.length,
        passedSubjects,
        averagePercentage,
        overallGrade: calculateOverallGrade(studentResults)
      };
    } catch (error) {
      console.error('Error calculating student summary:', error);
      return null;
    }
  };

  const getFilteredResults = () => {
    if (!Array.isArray(results)) {
      return [];
    }

    let filtered = results;

    if (selectedTerm !== "all") {
      filtered = filtered.filter(r => r.term === selectedTerm);
    }

    if (selectedYear) {
      filtered = filtered.filter(r => r.academicYear === selectedYear);
    }

    if (searchTerm) {
      filtered = filtered.filter(r =>
        r.studentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.subjectName?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return sortResults(filtered, 'name', 'asc');
  };

  // Filter students based on search and selected filters
  const filteredStudents = Array.isArray(students) ? students.filter(student => {
    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSelectedClass = selectedClass === "all" || student.class === selectedClass;
    return matchesSearch && matchesSelectedClass;
  }) : [];

  // Get unique classes for the dropdown
  const uniqueClasses = Array.isArray(students)
    ? [...new Set(students.map(s => s.class))].sort()
    : [];

  const generateResultSummary = (studentId: string, term: Term, year: string): ResultSummary | null => {
    const studentResults = results.filter(r => 
      r.studentId === studentId && 
      r.term === term && 
      r.academicYear === year
    );

    if (studentResults.length === 0) return null;

    const student = students.find(s => s.id === studentId);
    if (!student) return null;

    const summary = formatResultSummary(studentResults);
    const overallGrade = calculateOverallGrade(studentResults);

    return {
      studentId,
      studentName: student.name,
      classLevel: student.class as ClassLevel,
      term,
      academicYear: year,
      totalSubjects: summary.totalSubjects,
      totalMarks: summary.totalMarks,
      totalObtained: summary.totalObtained,
      averagePercentage: summary.averagePercentage,
      overallGrade,
      isPassed: summary.averagePercentage >= 40, // Assuming 40% pass mark
      results: studentResults,
      generatedAt: new Date().toISOString()
    };
  };

  const filteredResults = getFilteredResults();
  const groupedResults = filteredResults.reduce((acc, result) => {
    const key = `${result.studentId}-${result.term}-${result.academicYear}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(result);
    return acc;
  }, {} as Record<string, StudentResult[]>);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (students.length === 0) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Access to Results</h3>
          <p className="text-gray-600">
            {user?.role === 'parent' 
              ? "No children found in your account. Please contact the school administration."
              : user?.role === 'teacher'
              ? "No classes assigned to you. Please contact the school administration."
              : "No students found in the system."}
          </p>
        </div>
      </div>
    );
  }

  // Calculate stats
  const totalStudents = students.length;
  const studentsWithResults = students.filter(s => getStudentResults(s.id).length > 0).length;
  const totalResults = results.length;
  const averagePassRate = studentsWithResults > 0 && Array.isArray(students)
    ? Math.round((students.reduce((sum, s) => {
        const summary = getStudentSummary(s.id);
        return sum + (summary ? (summary.passedSubjects / summary.totalSubjects) : 0);
      }, 0) / studentsWithResults) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">View Results</h1>
          <p className="text-gray-600 mt-2">
            {user?.role === 'parent' && "View your children's academic results"}
            {user?.role === 'teacher' && "View results for your assigned classes"}
            {user?.role === 'admin' && "View all student results"}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-blue-600">{totalStudents}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <Award className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">With Results</p>
                <p className="text-2xl font-bold text-green-600">{studentsWithResults}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Results</p>
                <p className="text-2xl font-bold text-purple-600">{totalResults}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Pass Rate</p>
                <p className="text-2xl font-bold text-orange-600">{averagePassRate}%</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <Award className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex flex-wrap gap-4">
          <div className="relative flex-1 min-w-64">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search students by name or class..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>

          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
            <option value="all">All Classes</option>
            {uniqueClasses.map(className => (
              <option key={className} value={className}>
                {className}
              </option>
            ))}
          </select>

          <select
            value={selectedTerm}
            onChange={(e) => setSelectedTerm(e.target.value as Term | "all")}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
            <option value="all">All Terms</option>
            <option value="First">First Term</option>
            <option value="Second">Second Term</option>
            <option value="Third">Third Term</option>
          </select>

          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
            <option value={getCurrentAcademicYear()}>{getCurrentAcademicYear()}</option>
            <option value="2023-2024">2023-2024</option>
            <option value="2022-2023">2022-2023</option>
          </select>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            Students ({filteredStudents.length})
          </h2>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {filteredStudents.length === 0 ? (
            <div className="text-center py-12">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No students found</h3>
              <p className="text-gray-600">No students match your search criteria.</p>
            </div>
          ) : (
            filteredStudents.map((student, index) => {
              const studentResults = getAllStudentResults(student.id);
              const summary = getStudentSummary(student.id);

              return (
                <div
                  key={student.id}
                  className={`flex items-center justify-between p-4 hover:bg-white/50 transition-all duration-200 ${
                    index !== filteredStudents.length - 1 ? 'border-b border-gray-100' : ''
                  }`}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                      {student.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-1">
                        <h3 className="font-semibold text-gray-900 truncate">{student.name}</h3>
                        <Badge variant="outline" className="text-xs">
                          {student.class}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <FileText className="w-4 h-4 text-green-600" />
                          {studentResults.length} results
                        </span>

                        {summary && (
                          <>
                            <span className="flex items-center gap-1">
                              <TrendingUp className="w-4 h-4 text-blue-600" />
                              {summary.averagePercentage}% avg
                            </span>
                            <Badge
                              style={{ backgroundColor: getGradeColor(summary.overallGrade), color: 'white' }}
                              className="text-xs"
                            >
                              {summary.overallGrade}
                            </Badge>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => handleViewResults(student)}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg ml-4"
                  >
                    <Award className="w-4 h-4" />
                    <span className="text-sm font-medium">View Results</span>
                  </button>
                </div>
              )
            })
          )}
        </div>
      </div>
      {/* Results Modal */}
      <Dialog open={showResultsModal} onOpenChange={setShowResultsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Results for {modalStudent?.name || ''}</DialogTitle>
          </DialogHeader>

          {modalStudent && (
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold">
                    {modalStudent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{modalStudent.name}</h3>
                    <p className="text-sm text-gray-600">Class: {modalStudent.class}</p>
                  </div>
                </div>
              </div>

              {(() => {
                const studentResults = getAllStudentResults(modalStudent.id);
                const summary = getStudentSummary(modalStudent.id);

                if (!studentResults.length) {
                  return (
                    <div className="text-center py-12">
                      <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Available</h3>
                      <p className="text-gray-600">This student has no results recorded yet.</p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-6">
                    {/* Summary Stats */}
                    {summary && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-xl">
                          <div className="text-2xl font-bold text-blue-600">{summary.totalSubjects}</div>
                          <div className="text-sm text-gray-600">Subjects</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-xl">
                          <div className="text-2xl font-bold text-green-600">{summary.averagePercentage}%</div>
                          <div className="text-sm text-gray-600">Average</div>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-xl">
                          <div className="text-2xl font-bold text-purple-600">{summary.passedSubjects}</div>
                          <div className="text-sm text-gray-600">Passed</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 rounded-xl">
                          <Badge
                            style={{ backgroundColor: getGradeColor(summary.overallGrade), color: 'white' }}
                            className="text-lg px-3 py-1"
                          >
                            {summary.overallGrade}
                          </Badge>
                          <div className="text-sm text-gray-600 mt-1">Overall Grade</div>
                        </div>
                      </div>
                    )}

                    {/* Individual Results */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">Subject Results</h4>
                      {studentResults.map(result => (
                        <div key={result.id} className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h5 className="font-medium text-gray-900">{result.subjectName}</h5>
                              <Badge variant="outline" className="text-xs">
                                {result.subjectCode}
                              </Badge>
                              {result.isPassed ? (
                                <Badge className="bg-green-100 text-green-800 text-xs">Passed</Badge>
                              ) : (
                                <Badge className="bg-red-100 text-red-800 text-xs">Failed</Badge>
                              )}
                            </div>
                            {result.remarks && (
                              <p className="text-sm text-gray-600">{result.remarks}</p>
                            )}
                            {result.teacherComment && (
                              <p className="text-sm text-blue-600 italic">"{result.teacherComment}"</p>
                            )}
                          </div>
                          <div className="text-right">
                            <Badge
                              style={{ backgroundColor: getGradeColor(result.grade), color: 'white' }}
                              className="text-lg px-3 py-1 mb-1"
                            >
                              {result.grade}
                            </Badge>
                            <p className="text-sm text-gray-600">
                              {result.marks}/{result.totalMarks} ({result.percentage}%)
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
