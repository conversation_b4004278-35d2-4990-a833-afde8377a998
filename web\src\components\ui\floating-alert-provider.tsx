"use client";

import * as React from "react";
import { FloatingAlert, FloatingAlertProps } from "./floating-alert";

export interface FloatingAlertItem extends Omit<FloatingAlertProps, "id"> {
  id: string;
}

interface FloatingAlertProviderProps {
  children: React.ReactNode;
}

interface FloatingAlertContextType {
  alerts: FloatingAlertItem[];
  addAlert: (alert: Omit<FloatingAlertItem, "id">) => string;
  removeAlert: (id: string) => void;
  updateAlert: (id: string, alert: Partial<FloatingAlertItem>) => void;
}

const FloatingAlertContext = React.createContext<FloatingAlertContextType | undefined>(undefined);

export function FloatingAlertProvider({ children }: FloatingAlertProviderProps) {
  const [alerts, setAlerts] = React.useState<FloatingAlertItem[]>([]);

  const addAlert = React.useCallback((alert: Omit<FloatingAlertItem, "id">) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newAlert: FloatingAlertItem = { ...alert, id };
    
    setAlerts((prev) => [...prev, newAlert]);
    
    return id;
  }, []);

  const removeAlert = React.useCallback((id: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id));
  }, []);

  const updateAlert = React.useCallback((id: string, alert: Partial<FloatingAlertItem>) => {
    setAlerts((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...alert } : item))
    );
  }, []);

  const value = React.useMemo(
    () => ({
      alerts,
      addAlert,
      removeAlert,
      updateAlert,
    }),
    [alerts, addAlert, removeAlert, updateAlert]
  );

  return (
    <FloatingAlertContext.Provider value={value}>
      {children}
      <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none">
        {alerts.map((alert) => (
          <div key={alert.id} className="pointer-events-auto">
            <FloatingAlert
              {...alert}
              onOpenChange={(open) => {
                if (!open) {
                  removeAlert(alert.id);
                }
                alert.onOpenChange?.(open);
              }}
            />
          </div>
        ))}
      </div>
    </FloatingAlertContext.Provider>
  );
}

export function useFloatingAlert() {
  const context = React.useContext(FloatingAlertContext);
  if (!context) {
    throw new Error("useFloatingAlert must be used within a FloatingAlertProvider");
  }
  return context;
} 