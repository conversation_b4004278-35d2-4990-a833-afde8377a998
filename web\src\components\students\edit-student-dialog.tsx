
"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"
import { Student, StudentFormData, User, EnrollmentStatus, Gender } from "@/lib/types";
import { collection, getDocs, query, where } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { ClickableAvatar } from "../ui/clickable-avatar";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { uploadImageWithReplacement } from "@/lib/cloudinary";
import { useFloatingToast } from "@/hooks/use-floating-toast";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  dob: z.string().refine((val) => !isNaN(Date.parse(val)), "Invalid date format."),
  class: z.string().min(1, "Class is required."),
  parentId: z.string().min(1, "Parent is required."),
  gender: z.nativeEnum(Gender, { required_error: "Gender is required." }),
  enrollmentStatus: z.nativeEnum(EnrollmentStatus, { required_error: "Enrollment status is required." }),
  enrollmentDate: z.string().refine((val) => !isNaN(Date.parse(val)), "Invalid enrollment date format."),
  academicYear: z.string().min(1, "Academic year is required."),
  previousSchool: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.object({
    name: z.string().optional(),
    relationship: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  medicalInfo: z.string().optional(),
  photo: z.any().optional(),
});

type EditStudentFormValues = z.infer<typeof formSchema>;

interface EditStudentDialogProps {
  children: React.ReactNode;
  student: Student;
  onUpdateStudent: (studentId: string, data: EditStudentFormValues) => Promise<void>;
}

const ghanaianClasses = [
    "Nursery 1", "Nursery 2",
    "KG 1", "KG 2",
    "Basic 1", "Basic 2", "Basic 3",
    "Basic 4", "Basic 5", "Basic 6",
    "Basic 7", "Basic 8", "Basic 9",
];


export function EditStudentDialog({ children, student, onUpdateStudent }: EditStudentDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [parentOptions, setParentOptions] = useState<User[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { toast } = useFloatingToast();

  const form = useForm<EditStudentFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: student.name || "",
      dob: student.dob || "",
      class: student.class || "",
      parentId: student.parentId || "",
      gender: student.gender || Gender.Male,
      enrollmentStatus: student.enrollmentStatus || EnrollmentStatus.Active,
      enrollmentDate: student.enrollmentDate || new Date().toISOString().split('T')[0],
      academicYear: student.academicYear || new Date().getFullYear().toString(),
      previousSchool: student.previousSchool || "",
      address: student.address || "",
      emergencyContact: {
        name: student.emergencyContact?.name || "",
        relationship: student.emergencyContact?.relationship || "",
        phone: student.emergencyContact?.phone || "",
      },
      medicalInfo: student.medicalInfo || "",
    },
  });

  useEffect(() => {
    if (student) {
        form.reset({
            name: student.name || "",
            dob: student.dob || "",
            class: student.class || "",
            parentId: student.parentId || "",
            gender: student.gender || Gender.Male,
            enrollmentStatus: student.enrollmentStatus || EnrollmentStatus.Active,
            enrollmentDate: student.enrollmentDate || new Date().toISOString().split('T')[0],
            academicYear: student.academicYear || new Date().getFullYear().toString(),
            previousSchool: student.previousSchool || "",
            address: student.address || "",
            emergencyContact: {
              name: student.emergencyContact?.name || "",
              relationship: student.emergencyContact?.relationship || "",
              phone: student.emergencyContact?.phone || "",
            },
            medicalInfo: student.medicalInfo || "",
        });
    }
  }, [student, form, open]);

  useEffect(() => {
    const fetchParents = async () => {
      const q = query(collection(db, "users"), where("role", "==", "parent"));
      const querySnapshot = await getDocs(q);
      const parents = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User));
      setParentOptions(parents);
    };
    if (open) {
      fetchParents();
    }
  }, [open]);

  async function onSubmit(values: EditStudentFormValues) {
    setIsSubmitting(true);
    try {
      let avatarUrl = student.avatarUrl || "";
      
      // Upload image to Cloudinary if selected
      if (selectedFile) {
        try {
          // Pass the old image URL for automatic deletion
          avatarUrl = await uploadImageWithReplacement(selectedFile, student.avatarUrl);
        } catch (error) {
          console.error("Error uploading image:", error);
          toast({
            title: "Image Upload Failed",
            description: "Failed to upload image. Student will be updated without new photo.",
            variant: "error"
          });
        }
      }

      // Prepare the updated student data
      const updatedStudentData = {
        name: values.name,
        dob: values.dob,
        gender: values.gender,
        class: values.class,
        parentId: values.parentId,
        enrollmentStatus: values.enrollmentStatus,
        enrollmentDate: values.enrollmentDate,
        academicYear: values.academicYear,
        previousSchool: values.previousSchool || "",
        address: values.address || "",
        emergencyContact: {
          name: values.emergencyContact?.name || "",
          relationship: values.emergencyContact?.relationship || "",
          phone: values.emergencyContact?.phone || "",
        },
        medicalInfo: values.medicalInfo || "",
        avatarUrl: avatarUrl,
        lastUpdated: new Date().toISOString(),
      };

      await onUpdateStudent(student.id, updatedStudentData);
      form.reset();
      setImagePreview(null);
      setSelectedFile(null);
      setIsSubmitting(false);
      setOpen(false);
    } catch (error) {
      console.error("Error updating student:", error);
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Edit Student: {student.name}</DialogTitle>
          <DialogDescription>
            Update the student's details. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="max-h-[calc(90vh-200px)] overflow-y-auto pr-2">
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <ClickableAvatar
                    src={imagePreview || student.avatarUrl}
                    alt={student.name}
                    fallback={student.name.charAt(0)}
                    size="lg"
                    className="border"
                  />
                  <FormField
                    control={form.control}
                    name="photo"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Update Photo (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            type="file" 
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              setSelectedFile(file);
                              if (file) {
                                setImagePreview(URL.createObjectURL(file));
                              } else {
                                setImagePreview(null);
                              }
                            }}
                          />
                        </FormControl>
                        {imagePreview && (
                          <div className="mt-2">
                            <img 
                              src={imagePreview} 
                              alt="Preview" 
                              className="w-20 h-20 object-cover rounded-lg border"
                            />
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dob"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date of Birth</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="class"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Class/Grade</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ghanaianClasses.map((className) => (
                        <SelectItem key={className} value={className}>
                          {className}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent/Guardian</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a parent" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {parentOptions.map((parent) => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.name} ({parent.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* New Fields Section */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={Gender.Male}>Male</SelectItem>
                        <SelectItem value={Gender.Female}>Female</SelectItem>
                        <SelectItem value={Gender.Other}>Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="enrollmentStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Enrollment Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={EnrollmentStatus.Active}>Active</SelectItem>
                        <SelectItem value={EnrollmentStatus.OnHold}>On Hold</SelectItem>
                        <SelectItem value={EnrollmentStatus.Inactive}>Inactive</SelectItem>
                        <SelectItem value={EnrollmentStatus.Completed}>Completed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="enrollmentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Enrollment Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="academicYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Academic Year</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. 2024" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="previousSchool"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Previous School (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. ABC Primary School" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Student's residential address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="medicalInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Medical Information (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Any medical conditions, allergies, or special needs" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Emergency Contact Information */}
            <div className="space-y-4 pt-4 border-t">
              <h4 className="font-medium text-sm">Emergency Contact (Optional)</h4>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="emergencyContact.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Emergency contact name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emergencyContact.relationship"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Relationship</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Uncle, Aunt" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emergencyContact.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. ************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={isSubmitting}>{isSubmitting ? "Saving..." : "Save changes"}</Button>
            </DialogFooter>
          </div>
        </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
