import React, { useState, useEffect } from 'react';
import { getUpdateClient, UpdateInfo, UpdateProgress, UpdateStatus } from '../lib/update-client';

interface UpdateNotificationProps {
  onClose?: () => void;
}

export const UpdateNotification: React.FC<UpdateNotificationProps> = ({ onClose }) => {
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus | null>(null);
  const [showNotification, setShowNotification] = useState(false);
  const updateClient = getUpdateClient();

  useEffect(() => {
    // Get initial status
    updateClient.getUpdateStatus().then(setUpdateStatus);

    // Listen for update events
    const unsubscribeChecking = updateClient.on('checking', () => {
      setUpdateStatus(prev => prev ? { ...prev, checking: true, error: null } : null);
    });

    const unsubscribeAvailable = updateClient.on('available', (updateInfo: UpdateInfo) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        available: true, 
        updateInfo 
      } : null);
      setShowNotification(true);
    });

    const unsubscribeNotAvailable = updateClient.on('not-available', () => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        available: false 
      } : null);
    });

    const unsubscribeError = updateClient.on('error', (error: string) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        downloading: false, 
        error 
      } : null);
    });

    const unsubscribeDownloadStarted = updateClient.on('download-started', () => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        downloading: true, 
        progress: null 
      } : null);
    });

    const unsubscribeDownloadProgress = updateClient.on('download-progress', (progress: UpdateProgress) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        progress 
      } : null);
    });

    const unsubscribeDownloaded = updateClient.on('downloaded', (updateInfo: UpdateInfo) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        downloading: false, 
        downloaded: true, 
        updateInfo 
      } : null);
    });

    return () => {
      unsubscribeChecking();
      unsubscribeAvailable();
      unsubscribeNotAvailable();
      unsubscribeError();
      unsubscribeDownloadStarted();
      unsubscribeDownloadProgress();
      unsubscribeDownloaded();
    };
  }, [updateClient]);

  const handleDownload = async () => {
    await updateClient.downloadUpdate();
  };

  const handleInstall = async () => {
    await updateClient.installUpdate();
  };

  const handleCheckForUpdates = async () => {
    await updateClient.checkForUpdates();
  };

  const handleClose = () => {
    setShowNotification(false);
    onClose?.();
  };

  if (!updateStatus || (!showNotification && !updateStatus.checking && !updateStatus.downloading)) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      {/* Update Available Notification */}
      {updateStatus.available && !updateStatus.downloading && !updateStatus.downloaded && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-blue-800">
                Update Available
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>Version {updateStatus.updateInfo?.version} is available.</p>
                {updateStatus.updateInfo?.downloadSize && (
                  <p className="text-xs text-blue-600 mt-1">
                    Size: {updateClient.formatFileSize(updateStatus.updateInfo.downloadSize)}
                  </p>
                )}
              </div>
              <div className="mt-3 flex space-x-2">
                <button
                  onClick={handleDownload}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  Download
                </button>
                <button
                  onClick={handleClose}
                  className="bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm hover:bg-gray-300 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="flex-shrink-0 ml-2 text-blue-400 hover:text-blue-600"
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Downloading Progress */}
      {updateStatus.downloading && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-yellow-800">
                Downloading Update
              </h3>
              <div className="mt-2">
                {updateStatus.progress && (
                  <>
                    <div className="w-full bg-yellow-200 rounded-full h-2">
                      <div 
                        className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${updateStatus.progress.percent}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-yellow-700 mt-1">
                      {updateClient.formatProgress(updateStatus.progress)}
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Update Downloaded */}
      {updateStatus.downloaded && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-green-800">
                Update Ready
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>Update has been downloaded successfully!</p>
                <p className="text-xs mt-1">The app will restart to apply the update.</p>
              </div>
              <div className="mt-3 flex space-x-2">
                <button
                  onClick={handleInstall}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  Restart Now
                </button>
                <button
                  onClick={handleClose}
                  className="bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm hover:bg-gray-300 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="flex-shrink-0 ml-2 text-green-400 hover:text-green-600"
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Checking for Updates */}
      {updateStatus.checking && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-gray-700">Checking for updates...</p>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {updateStatus.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Update Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{updateStatus.error}</p>
              </div>
              <div className="mt-3">
                <button
                  onClick={handleCheckForUpdates}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="flex-shrink-0 ml-2 text-red-400 hover:text-red-600"
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateNotification;
