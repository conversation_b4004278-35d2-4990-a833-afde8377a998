
"use client";

import { useState, useRef, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { collection, getDocs, doc, writeBatch } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { useFloatingToast } from '@/hooks/use-floating-toast'

export default function Settings() {
  const { user, logout } = useAuth()
  const { toast } = useFloatingToast()
  const [activeTab, setActiveTab] = useState('profile')

  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [passwordInput, setPasswordInput] = useState('')
  const [isResetting, setIsResetting] = useState(false)
  const passwordInputRef = useRef<HTMLInputElement>(null)

  const [notifications, setNotifications] = useState({
    email: true,
    push: false,
    sms: true,
    marketing: false
  })

  // Effect to maintain focus on password input
  useEffect(() => {
    if (showPasswordDialog && passwordInputRef.current) {
      // Focus the input when dialog opens
      const timer = setTimeout(() => {
        passwordInputRef.current?.focus()
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [showPasswordDialog])

  const handleResetPaymentRecords = async () => {
    try {
      setIsResetting(true)
      console.log('🔄 Starting smart payment records reset...')

      // Get all current students in the system
      const studentsCollection = collection(db, 'students')
      const studentsSnapshot = await getDocs(studentsCollection)
      const existingStudentIds = new Set(studentsSnapshot.docs.map(doc => doc.id))

      console.log(`👥 Found ${existingStudentIds.size} students currently in system`)

      // Get all student payment records
      const studentPaymentsCollection = collection(db, 'studentPayments')
      const studentPaymentsSnapshot = await getDocs(studentPaymentsCollection)

      // Get all payment transaction records
      const paymentsCollection = collection(db, 'payments')
      const paymentsSnapshot = await getDocs(paymentsCollection)

      console.log(`📊 Found ${studentPaymentsSnapshot.docs.length} student payment records`)
      console.log(`📊 Found ${paymentsSnapshot.docs.length} payment transaction records`)

      // Create a batch for efficient operations
      const batch = writeBatch(db)

      let resetCount = 0
      let deletedOrphanedPayments = 0
      let deletedOrphanedTransactions = 0

      // Process student payment records
      studentPaymentsSnapshot.docs.forEach((docSnapshot) => {
        const paymentData = docSnapshot.data()
        const studentId = paymentData.studentId
        const studentPaymentRef = doc(db, 'studentPayments', docSnapshot.id)

        if (existingStudentIds.has(studentId)) {
          // Student exists - reset their payments to zero
          batch.update(studentPaymentRef, {
            payments: [],
            totalAmountPaid: 0,
            amountRemaining: paymentData.totalAmountDue || 0,
            paymentStatus: 'pending',
            lastPaymentDate: null,
            updatedAt: new Date().toISOString()
          })
          resetCount++
        } else {
          // Student doesn't exist - delete the orphaned payment record
          batch.delete(studentPaymentRef)
          deletedOrphanedPayments++
          console.log(`🗑️ Deleting orphaned payment record for student: ${studentId} (${paymentData.studentName || 'Unknown'})`)
        }
      })

      // Process payment transaction records
      paymentsSnapshot.docs.forEach((docSnapshot) => {
        const transactionData = docSnapshot.data()
        const studentId = transactionData.studentId
        const paymentRef = doc(db, 'payments', docSnapshot.id)

        if (existingStudentIds.has(studentId)) {
          // Student exists - delete their transaction (part of reset)
          batch.delete(paymentRef)
        } else {
          // Student doesn't exist - delete the orphaned transaction
          batch.delete(paymentRef)
          deletedOrphanedTransactions++
          console.log(`🗑️ Deleting orphaned transaction for student: ${studentId} (${transactionData.studentName || 'Unknown'})`)
        }
      })

      // Commit all changes
      await batch.commit()

      console.log('🎉 Smart payment reset completed!')
      toast({
        title: "Payment Records Reset Successfully!",
        description: `Reset ${resetCount} students, deleted ${deletedOrphanedPayments} orphaned payment records and ${deletedOrphanedTransactions} orphaned transactions.`,
        variant: "success",
        duration: 6000
      })

      // Close dialog and reset state
      setShowPasswordDialog(false)
      setPasswordInput('')

    } catch (error) {
      console.error('❌ Error in smart payment reset:', error)
      toast({
        title: "Error",
        description: "Failed to reset payment records. Check console for details.",
        variant: "error"
      })
    } finally {
      setIsResetting(false)
    }
  }

  // Tab configuration matching desktop app
  const tabs = [
    {
      id: 'profile',
      name: 'Profile',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    },
    {
      id: 'notifications',
      name: 'Notifications',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
        </svg>
      )
    },
    {
      id: 'security',
      name: 'Security',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      )
    },
    {
      id: 'preferences',
      name: 'Preferences',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    }
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Manage your account and application preferences</p>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:w-64">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-xl">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-white/50'
                  }`}
                >
                  {tab.icon}
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              {/* Profile Information */}
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span>Profile Information</span>
                </h3>

                <div className="flex items-center space-x-6 mb-8">
                  <div className="relative">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      {user?.name?.charAt(0).toUpperCase()}
                    </div>
                    <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-100 flex items-center justify-center hover:bg-gray-50 transition-colors">
                      <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </button>
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{user?.name}</h4>
                    <p className="text-gray-600 capitalize">{user?.role}</p>
                    <p className="text-sm text-gray-500 mt-1">{user?.email}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      defaultValue={user?.name}
                      placeholder="Enter your full name"
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input
                      type="email"
                      defaultValue={user?.email}
                      placeholder="Enter your email address"
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input
                      type="tel"
                      placeholder="+****************"
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <input
                      type="text"
                      value={user?.role}
                      disabled
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 text-gray-500 cursor-not-allowed"
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-8">
                  <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                  </svg>
                </div>
                <span>Notification Preferences</span>
              </h3>

              <div className="space-y-6">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                    <div>
                      <h4 className="font-medium text-gray-900 capitalize">{key} Notifications</h4>
                      <p className="text-sm text-gray-600">
                        {key === 'email' && 'Receive notifications via email'}
                        {key === 'push' && 'Receive push notifications in browser'}
                        {key === 'sms' && 'Receive SMS notifications'}
                        {key === 'marketing' && 'Receive marketing and promotional emails'}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setNotifications(prev => ({ ...prev, [key]: !value }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        value ? 'bg-blue-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          value ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              {/* Security Settings */}
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <span>Security Settings</span>
                </h3>

                <div className="space-y-4">
                  <button className="w-full flex items-center justify-between p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-all duration-200">
                    <div className="text-left">
                      <h4 className="font-medium text-gray-900">Change Password</h4>
                      <p className="text-sm text-gray-600">Update your account password</p>
                    </div>
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>

                  <button className="w-full flex items-center justify-between p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-all duration-200">
                    <div className="text-left">
                      <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                      <p className="text-sm text-gray-600">Add an extra layer of security</p>
                    </div>
                    <span className="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full font-medium">Enabled</span>
                  </button>

                  <button className="w-full flex items-center justify-between p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-all duration-200">
                    <div className="text-left">
                      <h4 className="font-medium text-gray-900">Login Sessions</h4>
                      <p className="text-sm text-gray-600">Manage your active sessions</p>
                    </div>
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Admin-only destructive operations */}
              {user?.role === 'admin' && (
                <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-red-200 shadow-xl">
                  <h3 className="text-lg font-semibold text-red-600 mb-6 flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <span>Security Danger Zone</span>
                  </h3>

                  <div className="p-4 bg-red-50 border border-red-200 rounded-xl mb-4">
                    <h4 className="font-medium text-red-900 mb-2">Reset Payment Records</h4>
                    <p className="text-sm text-red-700 mb-4">
                      Clean up payment system by resetting existing student payments to zero and removing orphaned records from deleted students.
                      This action cannot be undone.
                    </p>
                    <button
                      type="button"
                      onClick={async () => {
                        console.log('Reset button clicked!');
                        try {
                          const confirmed = window.confirm(
                            "⚠️ DANGER: Reset Payment Records\n\n" +
                            "This will clean up all payment records in the system! This action will:\n" +
                            "- Reset payments to zero for existing students\n" +
                            "- Remove payment records for deleted students\n" +
                            "- Delete all payment transaction history\n\n" +
                            "⚠️ This action cannot be undone!\n\n" +
                            "Are you absolutely sure you want to continue?"
                          );

                          if (confirmed) {
                            setShowPasswordDialog(true);
                          }
                        } catch (error) {
                          console.error('Error showing confirmation:', error);
                        }
                      }}
                      disabled={isResetting}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isResetting ? '🔄 Resetting...' : '🔄 Reset Payment Records'}
                    </button>
                  </div>
                </div>
              )}

              {/* Sign Out */}
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 className="text-lg font-semibold text-red-600 mb-4 flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </div>
                  <span>Sign Out</span>
                </h3>
                <p className="text-gray-600 mb-4">Sign out of your account and return to the login screen.</p>
                <button
                  onClick={logout}
                  className="px-6 py-3 bg-red-600 text-white rounded-xl font-medium hover:bg-red-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Sign Out
                </button>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <span>Application Preferences</span>
              </h3>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                  <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white">
                    <option value="en-us">English (US)</option>
                    <option value="en-uk">English (UK)</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                  <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white">
                    <option value="et">Eastern Time (ET)</option>
                    <option value="ct">Central Time (CT)</option>
                    <option value="mt">Mountain Time (MT)</option>
                    <option value="pt">Pacific Time (PT)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                  <div className="grid grid-cols-3 gap-4">
                    <button className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-xl hover:bg-white/50 transition-all duration-200">
                      <div className="w-8 h-8 bg-white border rounded mb-2 shadow-sm"></div>
                      <span className="text-sm font-medium">Light</span>
                    </button>
                    <button className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-xl hover:bg-white/50 transition-all duration-200">
                      <div className="w-8 h-8 bg-gray-800 rounded mb-2"></div>
                      <span className="text-sm font-medium">Dark</span>
                    </button>
                    <button className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-xl hover:bg-white/50 transition-all duration-200">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded mb-2"></div>
                      <span className="text-sm font-medium">Auto</span>
                    </button>
                  </div>
                </div>

                <div className="flex justify-end mt-8">
                  <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    Save Preferences
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Password Dialog Modal */}
      {showPasswordDialog && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-2xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-red-600 mb-4">⚠️ Admin Password Required</h3>
            <p className="text-sm text-gray-600 mb-4">
              Please enter the admin password to proceed with the payment records reset.
            </p>
            <input
              ref={passwordInputRef}
              type="password"
              value={passwordInput}
              onChange={(e) => setPasswordInput(e.target.value)}
              placeholder="Enter admin password"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 mb-4"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && passwordInput.trim()) {
                  e.preventDefault();
                  if (passwordInput === 'admin123') {
                    handleResetPaymentRecords();
                  } else {
                    // Show toast for wrong password
                    toast({
                      title: "Incorrect Password",
                      description: "Invalid admin password. Please try again.",
                      variant: "error",
                      duration: 3000
                    });
                    // Clear password and maintain focus
                    setPasswordInput('');
                    // Re-focus after a short delay to ensure toast doesn't interfere
                    setTimeout(() => {
                      passwordInputRef.current?.focus();
                    }, 150);
                  }
                }
              }}
              autoFocus
            />
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowPasswordDialog(false);
                  setPasswordInput('');
                }}
                className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                disabled={isResetting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  if (passwordInput === 'admin123') {
                    handleResetPaymentRecords();
                  } else {
                    // Show toast for wrong password
                    toast({
                      title: "Incorrect Password",
                      description: "Invalid admin password. Please try again.",
                      variant: "error",
                      duration: 3000
                    });
                    // Clear password and maintain focus
                    setPasswordInput('');
                    // Re-focus after a short delay to ensure toast doesn't interfere
                    setTimeout(() => {
                      passwordInputRef.current?.focus();
                    }, 150);
                  }
                }}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!passwordInput.trim() || isResetting}
              >
                {isResetting ? 'Processing...' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
