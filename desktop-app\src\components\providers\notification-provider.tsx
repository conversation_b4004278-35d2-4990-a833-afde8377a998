import React, { createContext, useContext, useState, useCallback } from 'react'
import { ToastProvider, useToastContext } from './toast-provider'
import { ConfirmModal, InputModal } from '../ui/modal'

interface ConfirmOptions {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'danger' | 'warning' | 'info'
}

interface InputOptions {
  title: string
  message: string
  placeholder?: string
  inputType?: 'text' | 'password'
  confirmText?: string
  cancelText?: string
}

interface NotificationContextType {
  notify: {
    success: (title: string, description?: string, duration?: number) => void
    error: (title: string, description?: string, duration?: number) => void
    warning: (title: string, description?: string, duration?: number) => void
    info: (title: string, description?: string, duration?: number) => void
  }
  confirm: (options: ConfirmOptions) => Promise<boolean>
  prompt: (options: InputOptions) => Promise<string | null>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  return (
    <ToastProvider>
      <NotificationContent>
        {children}
      </NotificationContent>
    </ToastProvider>
  )
}

function NotificationContent({ children }: { children: React.ReactNode }) {
  const { toast } = useToastContext()
  const [confirmState, setConfirmState] = useState<{
    isOpen: boolean
    options: ConfirmOptions | null
    resolve: ((value: boolean) => void) | null
  }>({ isOpen: false, options: null, resolve: null })

  const [inputState, setInputState] = useState<{
    isOpen: boolean
    options: InputOptions | null
    resolve: ((value: string | null) => void) | null
  }>({ isOpen: false, options: null, resolve: null })

  // Dynamic Island notifications
  const notify = {
    success: useCallback((title: string, description?: string, duration?: number) => {
      toast({ title, description, variant: 'success', duration: duration || 4000 })
    }, [toast]),
    error: useCallback((title: string, description?: string, duration?: number) => {
      toast({ title, description, variant: 'error', duration: duration || 5000 })
    }, [toast]),
    warning: useCallback((title: string, description?: string, duration?: number) => {
      toast({ title, description, variant: 'warning', duration: duration || 4000 })
    }, [toast]),
    info: useCallback((title: string, description?: string, duration?: number) => {
      toast({ title, description, variant: 'info', duration: duration || 4000 })
    }, [toast])
  }

  // Confirm modal
  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmState({ isOpen: true, options, resolve })
    })
  }, [])

  // Input modal
  const prompt = useCallback((options: InputOptions): Promise<string | null> => {
    return new Promise((resolve) => {
      setInputState({ isOpen: true, options, resolve })
    })
  }, [])

  const handleConfirm = useCallback(() => {
    if (confirmState.resolve) {
      confirmState.resolve(true)
      setConfirmState({ isOpen: false, options: null, resolve: null })
    }
  }, [confirmState.resolve])

  const handleConfirmCancel = useCallback(() => {
    if (confirmState.resolve) {
      confirmState.resolve(false)
      setConfirmState({ isOpen: false, options: null, resolve: null })
    }
  }, [confirmState.resolve])

  const handleInputConfirm = useCallback((value: string) => {
    if (inputState.resolve) {
      inputState.resolve(value)
      setInputState({ isOpen: false, options: null, resolve: null })
    }
  }, [inputState.resolve])

  const handleInputCancel = useCallback(() => {
    if (inputState.resolve) {
      inputState.resolve(null)
      setInputState({ isOpen: false, options: null, resolve: null })
    }
  }, [inputState.resolve])

  return (
    <NotificationContext.Provider value={{ notify, confirm, prompt }}>
      {children}

      {/* Confirm Modal */}
      <ConfirmModal
        isOpen={confirmState.isOpen}
        onClose={handleConfirmCancel}
        onConfirm={handleConfirm}
        title={confirmState.options?.title || ''}
        message={confirmState.options?.message || ''}
        confirmText={confirmState.options?.confirmText}
        cancelText={confirmState.options?.cancelText}
        variant={confirmState.options?.variant}
        isLoading={false}
      />

      {/* Input Modal */}
      <InputModal
        isOpen={inputState.isOpen}
        onClose={handleInputCancel}
        onConfirm={handleInputConfirm}
        title={inputState.options?.title || ''}
        message={inputState.options?.message || ''}
        placeholder={inputState.options?.placeholder}
        inputType={inputState.options?.inputType}
        confirmText={inputState.options?.confirmText}
        cancelText={inputState.options?.cancelText}
        isLoading={false}
      />
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}
