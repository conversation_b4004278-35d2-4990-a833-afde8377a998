# lodash.escaperegexp v4.1.2

The [lodash](https://lodash.com/) method `_.escapeRegExp` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.escaperegexp
```

In Node.js:
```js
var escapeRegExp = require('lodash.escaperegexp');
```

See the [documentation](https://lodash.com/docs#escapeRegExp) or [package source](https://github.com/lodash/lodash/blob/4.1.2-npm-packages/lodash.escaperegexp) for more details.
