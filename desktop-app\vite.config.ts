import { defineConfig } from 'vite'
import path from 'node:path'
import electron from 'vite-plugin-electron/simple'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    electron({
      main: {
        // Shortcut of `build.lib.entry`.
        entry: 'electron/main.ts',
      },
      preload: {
        // Shortcut of `build.rollupOptions.input`.
        // Preload scripts may contain Web assets, so use the `build.rollupOptions.input` instead `build.lib.entry`.
        input: path.join(__dirname, 'electron/preload.ts'),
      },
      // Disable renderer polyfills since we're using IPC for Node.js functionality
      renderer: undefined,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    exclude: [
      // Exclude MongoDB and native modules from pre-bundling
      'mongodb',
      'kerberos',
      'bson-ext',
      'snappy',
      '@mongodb-js/zstd',
      'gcp-metadata'
    ]
  },
  define: {
    // Prevent MongoDB from being bundled in renderer
    'process.env.MONGODB_DISABLED': '"true"'
  },
  build: {
    rollupOptions: {
      external: (id) => {
        // Exclude MongoDB and all its dependencies from renderer bundle
        if (id.includes('mongodb') ||
            id.includes('kerberos') ||
            id.includes('bson') ||
            id.includes('snappy') ||
            id.includes('@mongodb-js') ||
            id.includes('gcp-metadata')) {
          return true;
        }
        return false;
      },
      output: {
        assetFileNames: (assetInfo) => {
          // Keep logo files in root for easy access
          if (assetInfo.name && (assetInfo.name.includes('logo.jpg') || assetInfo.name.includes('logo.ico'))) {
            return '[name][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    }
  },
})
