"use client";

import React, { useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { X } from 'lucide-react';

interface ProfileImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  studentName: string;
}

export function ProfileImageModal({ isOpen, onClose, imageUrl, studentName }: ProfileImageModalProps) {
  // Close modal when clicking outside or pressing Escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-none w-auto p-0 bg-transparent border-none shadow-none">
        <div className="relative group">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-50 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-all duration-200 opacity-0 group-hover:opacity-100"
            aria-label="Close profile image"
            title="Close profile image"
          >
            <X className="w-5 h-5" />
          </button>
          
          {/* Image container with animation */}
          <div className="relative overflow-hidden rounded-lg">
            <img
              src={imageUrl}
              alt={`${studentName}'s profile`}
              className="max-w-[90vw] max-h-[90vh] object-contain rounded-lg shadow-2xl"
              style={{
                animation: 'profileImageEnter 0.3s ease-out'
              }}
            />
          </div>
          
          {/* Student name overlay */}
          <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-md text-sm font-medium">
            {studentName}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes profileImageEnter {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;
document.head.appendChild(style); 