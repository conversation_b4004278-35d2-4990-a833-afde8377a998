// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAjVTuDKnVSskcaQWllNI94leXojDo7IyI",
  authDomain: "maggie-management.firebaseapp.com",
  projectId: "maggie-management",
  storageBucket: "maggie-management.appspot.com",
  messagingSenderId: "412831967427",
  appId: "1:412831967427:web:a51a60b5d8278ef1081a76",
  measurementId: "G-CVSBVPLFPL"
};


// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

export { app, auth, db, storage };
