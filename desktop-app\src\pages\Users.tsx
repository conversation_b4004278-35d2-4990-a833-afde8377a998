import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import { collection, onSnapshot, doc, setDoc, updateDoc, deleteDoc } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { User } from '../lib/types'
import {
  generateDefaultEmail,
  getDefaultPassword,
  createUserWithDefaults,
  validateUserRegistration,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from '../lib/business-logic'
import { useNotifications } from '../components/providers/notification-provider'
import { useOperationFeedback } from '../hooks/use-operation-feedback'

export default function Users() {
  const { user } = useAuth()
  const { notify } = useNotifications()
  const { executeWithFeedback, showSuccess, showError } = useOperationFeedback()
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedUserType, setSelectedUserType] = useState<'teacher' | 'admin' | 'parent'>('teacher')
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [formMode, setFormMode] = useState<'add' | 'view' | 'edit'>('add')
  const [isReadOnly, setIsReadOnly] = useState(false)

  // Form data matching web app User interface exactly
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'teacher' as 'admin' | 'teacher' | 'parent',
    phone: '',
    address: '',
    assignedClasses: [] as string[], // For teachers only
    password: 'password' // Default password
  })

  // Fetch users data from Firebase
  useEffect(() => {
    const usersCollection = collection(db, 'users');

    // Use real-time listener for instant updates
    const unsubscribe = onSnapshot(usersCollection, (snapshot) => {
      const usersList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User));
      setUsers(usersList);
      setLoading(false);
    }, (error) => {
      console.error('❌ Error in users listener:', error);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const roles = ['all', 'admin', 'teacher', 'parent']

  // Available classes for teacher assignment
  const availableClasses = [
    'Nursery 1', 'Nursery 2', 'KG 1', 'KG 2',
    'Basic 1', 'Basic 2', 'Basic 3', 'Basic 4', 'Basic 5', 'Basic 6',
    'Basic 7', 'Basic 8', 'Basic 9'
  ]

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    return matchesSearch && matchesRole
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (formMode === 'view') return; // No submission in view mode

    try {
      if (formMode === 'edit' && selectedUser) {
        // Update existing user
        await handleUpdateUser();
        return;
      }

      // Add new user (existing logic)
      // Generate email if not provided
      const email = formData.email || generateDefaultEmail(formData.name, formData.role)

      const userData = createUserWithDefaults({
        ...formData,
        email,
        password: getDefaultPassword(),
      })

      console.log('Creating user:', userData)

      // Create user document in Firestore
      await setDoc(doc(db, "users", email), userData);

      console.log('✅ User created successfully:', email);

      notify.success(
        SUCCESS_MESSAGES.REGISTRATION_SUCCESS,
        `${formData.role.charAt(0).toUpperCase() + formData.role.slice(1)} ${formData.name} created. Credentials: ${email} / password`
      );

      // Reset form
      setFormData({
        name: '',
        email: '',
        role: 'teacher',
        phone: '',
        address: '',
        assignedClasses: [],
        password: 'password'
      })
      handleCloseModal()

      // No need to manually refresh - real-time listener will update automatically

    } catch (error) {
      console.error('Error creating user:', error)
      showError('Error creating user. Please try again.')
    }
  }

  // CRUD Functions - Use original form for view/edit
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setFormMode('view');
    setIsReadOnly(true);

    // Set the user type based on the actual user's role
    setSelectedUserType(user.role as 'teacher' | 'admin' | 'parent');

    // Populate original form with user data
    setFormData({
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      role: user.role,
      assignedClasses: user.assignedClasses || [],
      address: '',
      password: ''
    });

    setShowAddModal(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormMode('edit');
    setIsReadOnly(false);

    // Set the user type based on the actual user's role
    setSelectedUserType(user.role as 'teacher' | 'admin' | 'parent');

    // Populate original form with user data
    setFormData({
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      role: user.role,
      assignedClasses: user.assignedClasses || [],
      address: '',
      password: ''
    });

    setShowAddModal(true);
  };

  // Clean up function to reset form state
  const handleCloseModal = () => {
    setShowAddModal(false);
    setSelectedUser(null);
    setFormMode('add');
    setIsReadOnly(false);
    setSelectedUserType('teacher');

    // Reset form data
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'teacher',
      assignedClasses: [],
      address: '',
      password: ''
    });
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setFormMode('add');
    setIsReadOnly(false);

    // Reset to default user type (teacher) for new users
    setSelectedUserType('teacher');

    // Reset form to empty
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'teacher',
      assignedClasses: [],
      address: '',
      password: ''
    });

    setShowAddModal(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedUser || formMode !== 'edit') return;

    await executeWithFeedback(
      async () => {
        await updateDoc(doc(db, 'users', selectedUser.id), {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          assignedClasses: formData.assignedClasses,
          updatedAt: new Date().toISOString()
        });

        // No need to manually refresh - real-time listener will update automatically
        handleCloseModal();
      },
      {
        successMessage: 'User updated successfully!',
        errorMessage: 'Failed to update user'
      }
    );
  };

  const handleConfirmDelete = async () => {
    if (!selectedUser) return;

    await executeWithFeedback(
      async () => {
        await deleteDoc(doc(db, 'users', selectedUser.id));

        // Remove from local state
        setUsers(prev => prev.filter(u => u.id !== selectedUser.id));
        setShowDeleteModal(false);
        setSelectedUser(null);
      },
      {
        successMessage: 'User deleted successfully!',
        errorMessage: 'Failed to delete user'
      }
    );
  };

  // Check if user is admin
  if (user?.role !== 'admin') {
    return (
      <div className="p-6 flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Access Denied</h3>
            <p className="text-sm text-gray-600">
              You don't have permission to access this page.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
          <p className="text-gray-600">Manage user accounts, roles, and permissions</p>
        </div>
        <button
          type="button"
          onClick={handleAddUser}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>Add User</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Admins</p>
              <p className="text-2xl font-bold text-red-600">{users.filter(u => u.role === 'admin').length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Teachers</p>
              <p className="text-2xl font-bold text-green-600">{users.filter(u => u.role === 'teacher').length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Parents</p>
              <p className="text-2xl font-bold text-purple-600">{users.filter(u => u.role === 'parent').length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>

          {/* Role Filter */}
          <div className="flex items-center space-x-4">
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              aria-label="Filter by role"
              className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              {roles.map(role => (
                <option key={role} value={role}>
                  {role === 'all' ? 'All Roles' : role.charAt(0).toUpperCase() + role.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-white/10">
          <h3 className="text-lg font-semibold text-gray-900">
            Users ({filteredUsers.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50/50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200/50">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user.role === 'teacher' ? 'bg-green-100 text-green-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Never
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {/* View button */}
                      <button
                        type="button"
                        onClick={() => handleViewUser(user)}
                        aria-label={`View ${user.name}`}
                        title={`View ${user.name}`}
                        className="text-blue-600 hover:text-blue-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      {/* Edit button */}
                      <button
                        type="button"
                        onClick={() => handleEditUser(user)}
                        aria-label={`Edit ${user.name}`}
                        title={`Edit ${user.name}`}
                        className="text-green-600 hover:text-green-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      {/* Delete button */}
                      <button
                        type="button"
                        onClick={() => handleDeleteUser(user)}
                        aria-label={`Delete ${user.name}`}
                        title={`Delete ${user.name}`}
                        className="text-red-600 hover:text-red-900 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add User Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl w-full max-w-3xl border border-white/20 shadow-2xl h-[85vh] flex flex-col">
            {/* Fixed Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-xl font-semibold text-gray-900">
                {formMode === 'add' && 'Add New User'}
                {formMode === 'view' && `View User: ${formData.name}`}
                {formMode === 'edit' && `Edit User: ${formData.name}`}
              </h2>
              <button
                type="button"
                onClick={handleCloseModal}
                aria-label="Close modal"
                title="Close modal"
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form
                key={`${formMode}-${selectedUser?.id || 'new'}`}
                id="user-creation-form"
                onSubmit={handleSubmit}
                className="space-y-6"
              >
              {/* User Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">User Type</label>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    disabled={isReadOnly}
                    onClick={() => {
                      setSelectedUserType('teacher')
                      setFormData({...formData, role: 'teacher', assignedClasses: []})
                    }}
                    className={`px-4 py-2 rounded-lg border transition-all duration-200 ${
                      selectedUserType === 'teacher'
                        ? 'bg-blue-500 text-white border-blue-500'
                        : isReadOnly
                        ? 'bg-gray-100 text-gray-500 border-gray-200 cursor-not-allowed'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                    }`}
                  >
                    Teacher
                  </button>
                  <button
                    type="button"
                    disabled={isReadOnly}
                    onClick={() => {
                      setSelectedUserType('admin')
                      setFormData({...formData, role: 'admin', assignedClasses: []})
                    }}
                    className={`px-4 py-2 rounded-lg border transition-all duration-200 ${
                      selectedUserType === 'admin'
                        ? 'bg-red-500 text-white border-red-500'
                        : isReadOnly
                        ? 'bg-gray-100 text-gray-500 border-gray-200 cursor-not-allowed'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-red-300'
                    }`}
                  >
                    Admin
                  </button>
                  <button
                    type="button"
                    disabled={isReadOnly}
                    onClick={() => {
                      setSelectedUserType('parent')
                      setFormData({...formData, role: 'parent', assignedClasses: []})
                    }}
                    className={`px-4 py-2 rounded-lg border transition-all duration-200 ${
                      selectedUserType === 'parent'
                        ? 'bg-purple-500 text-white border-purple-500'
                        : isReadOnly
                        ? 'bg-gray-100 text-gray-500 border-gray-200 cursor-not-allowed'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-purple-300'
                    }`}
                  >
                    Parent
                  </button>
                </div>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="Enter full name"
                    className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                      isReadOnly
                        ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                        : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                    }`}
                    readOnly={isReadOnly}
                    required={!isReadOnly}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email (Optional)</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    placeholder="Will auto-generate if empty"
                    className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                      isReadOnly
                        ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                        : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                    }`}
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    placeholder="Phone number"
                    className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                      isReadOnly
                        ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                        : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                    }`}
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Default Password</label>
                  <input
                    type="text"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    aria-label="Default password"
                    title="Default password for new user"
                    className="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-xl text-gray-600"
                    readOnly
                  />
                </div>
              </div>

              {/* Teacher-specific fields - Show only for teachers */}
              {(formMode === 'add' ? selectedUserType === 'teacher' : selectedUser?.role === 'teacher') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Assigned Classes</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto p-3 bg-gray-50 rounded-xl">
                    {availableClasses.map((className) => (
                      <label key={className} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.assignedClasses.includes(className)}
                          disabled={isReadOnly}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                assignedClasses: [...formData.assignedClasses, className]
                              })
                            } else {
                              setFormData({
                                ...formData,
                                assignedClasses: formData.assignedClasses.filter(c => c !== className)
                              })
                            }
                          }}
                          className={`rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
                            isReadOnly ? 'cursor-not-allowed opacity-50' : ''
                          }`}
                        />
                        <span className="text-sm text-gray-700">{className}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Address (Optional)</label>
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  rows={3}
                  placeholder="Enter address"
                  readOnly={isReadOnly}
                  className={`w-full px-4 py-3 backdrop-blur-sm border rounded-xl transition-all duration-200 ${
                    isReadOnly
                      ? 'bg-gray-100 border-gray-200 text-gray-700 cursor-not-allowed'
                      : 'bg-white/50 border-white/30 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  }`}
                />
              </div>

            </form>
            </div>

            {/* Fixed Footer */}
            <div className="p-6 border-t border-white/20 bg-white/50">
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl hover:bg-gray-300 transition-all duration-200"
                >
                  {formMode === 'view' ? 'Close' : 'Cancel'}
                </button>
                {formMode !== 'view' && (
                  <button
                    type="submit"
                    form="user-creation-form"
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
                  >
                    {formMode === 'add'
                      ? `Create ${selectedUserType.charAt(0).toUpperCase() + selectedUserType.slice(1)}`
                      : 'Update User'
                    }
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}





      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl w-full max-w-md border border-white/20 shadow-2xl">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                <svg className="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">Delete User</h3>
              <p className="text-gray-600 text-center mb-6">
                Are you sure you want to delete <strong>{selectedUser.name}</strong>? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmDelete}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
