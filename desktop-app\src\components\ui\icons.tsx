import {
  BarChart3,
  Users,
  Settings,
  Home,
  LogOut,
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  X,
  Check,
  AlertCircle,
  Info,
  Calendar,
  Clock,
  Phone,
  MapPin,
  GraduationCap,
  BookOpen,
  FileText,
  TrendingUp,
  TrendingDown,
  Activity,
  Bell,
  Menu,
  Loader2
} from "lucide-react"

export const Icons = {
  // Navigation
  dashboard: Home,
  students: Users,
  analytics: BarChart3,
  settings: Settings,
  
  // Actions
  logout: LogOut,
  add: Plus,
  search: Search,
  filter: Filter,
  download: Download,
  upload: Upload,
  edit: Edit,
  delete: Trash2,
  more: MoreHorizontal,
  
  // UI
  chevronDown: ChevronDown,
  chevronRight: ChevronRight,
  close: X,
  check: Check,
  loading: Loader2,
  menu: Menu,
  
  // User & Auth
  user: User,
  mail: Mail,
  lock: Lock,
  eye: Eye,
  eyeOff: EyeOff,
  
  // Status & Alerts
  alert: AlertCircle,
  info: Info,
  bell: Bell,
  
  // Data & Time
  calendar: Calendar,
  clock: Clock,
  
  // Contact
  phone: Phone,
  location: MapPin,
  
  // Education
  graduation: GraduationCap,
  book: BookOpen,
  file: FileText,
  
  // Charts & Trends
  trendingUp: TrendingUp,
  trendingDown: TrendingDown,
  activity: Activity,
}
