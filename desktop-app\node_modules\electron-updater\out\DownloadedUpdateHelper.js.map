{"version": 3, "file": "DownloadedUpdateHelper.js", "sourceRoot": "", "sources": ["../src/DownloadedUpdateHelper.ts"], "names": [], "mappings": ";;;AAqLA,oDAkBC;AAtMD,mCAAmC;AACnC,2BAAqC;AACrC,aAAa;AACb,0CAAyC;AAGzC,uCAA6E;AAC7E,6BAA4B;AAE5B,gBAAgB;AAChB,MAAa,sBAAsB;IAOjC,YAAqB,QAAgB;QAAhB,aAAQ,GAAR,QAAQ,CAAQ;QAN7B,UAAK,GAAkB,IAAI,CAAA;QAC3B,iBAAY,GAAkB,IAAI,CAAA;QAElC,gBAAW,GAAsB,IAAI,CAAA;QACrC,aAAQ,GAAkC,IAAI,CAAA;QAI9C,wBAAmB,GAA4B,IAAI,CAAA;IAFnB,CAAC;IAGzC,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACjC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;IAC5C,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,UAAsB,EAAE,QAAgC,EAAE,MAAc;QACvH,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAClF,gEAAgE;YAChE,0CAA0C;YAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAA,qBAAU,EAAC,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC1H,OAAO,UAAU,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QAED,mEAAmE;QACnE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QAC9E,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAA;QACb,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,yCAAyC,UAAU,IAAI,CAAC,CAAA;QACpE,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAA;QAC7B,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,cAAsB,EACtB,WAA0B,EAC1B,WAAuB,EACvB,QAAgC,EAChC,cAAsB,EACtB,WAAoB;QAEpB,IAAI,CAAC,KAAK,GAAG,cAAc,CAAA;QAC3B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,mBAAmB,GAAG;YACzB,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;YAC5B,qBAAqB,EAAE,QAAQ,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI;SACpE,CAAA;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAA;IAC5C,CAAC;IAEO,KAAK,CAAC,6BAA6B;QACzC,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;QAC/C,CAAC;QAAC,OAAO,OAAO,EAAE,CAAC;YACjB,SAAS;QACX,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,wBAAwB,CAAC,QAAgC,EAAE,MAAc;QACrF,MAAM,kBAAkB,GAAW,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE3D,MAAM,uBAAuB,GAAG,MAAM,IAAA,qBAAU,EAAC,kBAAkB,CAAC,CAAA;QACpE,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,UAA4B,CAAA;QAChC,IAAI,CAAC;YACH,UAAU,GAAG,MAAM,IAAA,mBAAQ,EAAC,kBAAkB,CAAC,CAAA;QACjD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,OAAO,GAAG,iCAAiC,CAAA;YAC/C,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAA;gBAC1C,OAAO,IAAI,oBAAoB,KAAK,CAAC,OAAO,GAAG,CAAA;YACjD,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACpB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,yBAAyB,GAAG,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,MAAK,IAAI,CAAA;QAC/D,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAA;YACxG,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAA;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CACT,mHAAmH,UAAU,CAAC,MAAM,eAAe,QAAQ,CAAC,IAAI,CAAC,MAAM,+CAA+C,CACvN,CAAA;YACD,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAA;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;QAChF,IAAI,CAAC,CAAC,MAAM,IAAA,qBAAU,EAAC,UAAU,CAAC,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAA;YAC/C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,CAAA;QACzC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,qGAAqG,MAAM,eAAe,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;YAC7J,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAA;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAA;QACrC,OAAO,UAAU,CAAA;IACnB,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAA;IACrE,CAAC;CACF;AAnJD,wDAmJC;AAQD,SAAS,QAAQ,CAAC,IAAY,EAAE,SAAS,GAAG,QAAQ,EAAE,WAA6B,QAAQ,EAAE,OAAa;IACxG,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,SAAS,CAAC,CAAA;QAClC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAE9C,IAAA,qBAAgB,EAAC,IAAI,EAAE,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,IAAI,GAAG,IAAI,CAAC,+CAA+C,EAAE,CAAC;aAC/G,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACnB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACd,IAAI,CAAC,GAAG,EAAE,CAAA;YACV,OAAO,CAAC,IAAI,CAAC,IAAI,EAAY,CAAC,CAAA;QAChC,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;IAC/B,CAAC,CAAC,CAAA;AACJ,CAAC;AAEM,KAAK,UAAU,oBAAoB,CAAC,IAAY,EAAE,QAAgB,EAAE,GAAW;IACpF,yFAAyF;IACzF,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;IACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3B,IAAI,CAAC;YACH,MAAM,IAAA,iBAAM,EAAC,MAAM,CAAC,CAAA;YACpB,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,OAAO,MAAM,CAAA;YACf,CAAC;YAED,GAAG,CAAC,IAAI,CAAC,qCAAqC,CAAC,EAAE,CAAC,CAAA;YAClD,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { UpdateInfo } from \"builder-util-runtime\"\nimport { createHash } from \"crypto\"\nimport { createReadStream } from \"fs\"\n// @ts-ignore\nimport * as isEqual from \"lodash.isequal\"\nimport { ResolvedUpdateFileInfo } from \"./types\"\nimport { Logger } from \"./types\"\nimport { pathExists, readJson, emptyDir, outputJson, unlink } from \"fs-extra\"\nimport * as path from \"path\"\n\n/** @private **/\nexport class DownloadedUpdateHelper {\n  private _file: string | null = null\n  private _packageFile: string | null = null\n\n  private versionInfo: UpdateInfo | null = null\n  private fileInfo: ResolvedUpdateFileInfo | null = null\n\n  constructor(readonly cacheDir: string) {}\n\n  private _downloadedFileInfo: CachedUpdateInfo | null = null\n  get downloadedFileInfo(): CachedUpdateInfo | null {\n    return this._downloadedFileInfo\n  }\n\n  get file(): string | null {\n    return this._file\n  }\n\n  get packageFile(): string | null {\n    return this._packageFile\n  }\n\n  get cacheDirForPendingUpdate(): string {\n    return path.join(this.cacheDir, \"pending\")\n  }\n\n  async validateDownloadedPath(updateFile: string, updateInfo: UpdateInfo, fileInfo: ResolvedUpdateFileInfo, logger: Logger): Promise<string | null> {\n    if (this.versionInfo != null && this.file === updateFile && this.fileInfo != null) {\n      // update has already been downloaded from this running instance\n      // check here only existence, not checksum\n      if (isEqual(this.versionInfo, updateInfo) && isEqual(this.fileInfo.info, fileInfo.info) && (await pathExists(updateFile))) {\n        return updateFile\n      } else {\n        return null\n      }\n    }\n\n    // update has already been downloaded from some previous app launch\n    const cachedUpdateFile = await this.getValidCachedUpdateFile(fileInfo, logger)\n    if (cachedUpdateFile === null) {\n      return null\n    }\n    logger.info(`Update has already been downloaded to ${updateFile}).`)\n    this._file = cachedUpdateFile\n    return cachedUpdateFile\n  }\n\n  async setDownloadedFile(\n    downloadedFile: string,\n    packageFile: string | null,\n    versionInfo: UpdateInfo,\n    fileInfo: ResolvedUpdateFileInfo,\n    updateFileName: string,\n    isSaveCache: boolean\n  ): Promise<void> {\n    this._file = downloadedFile\n    this._packageFile = packageFile\n    this.versionInfo = versionInfo\n    this.fileInfo = fileInfo\n    this._downloadedFileInfo = {\n      fileName: updateFileName,\n      sha512: fileInfo.info.sha512,\n      isAdminRightsRequired: fileInfo.info.isAdminRightsRequired === true,\n    }\n\n    if (isSaveCache) {\n      await outputJson(this.getUpdateInfoFile(), this._downloadedFileInfo)\n    }\n  }\n\n  async clear(): Promise<void> {\n    this._file = null\n    this._packageFile = null\n    this.versionInfo = null\n    this.fileInfo = null\n    await this.cleanCacheDirForPendingUpdate()\n  }\n\n  private async cleanCacheDirForPendingUpdate(): Promise<void> {\n    try {\n      // remove stale data\n      await emptyDir(this.cacheDirForPendingUpdate)\n    } catch (_ignore) {\n      // ignore\n    }\n  }\n\n  /**\n   * Returns \"update-info.json\" which is created in the update cache directory's \"pending\" subfolder after the first update is downloaded.  If the update file does not exist then the cache is cleared and recreated.  If the update file exists then its properties are validated.\n   * @param fileInfo\n   * @param logger\n   */\n  private async getValidCachedUpdateFile(fileInfo: ResolvedUpdateFileInfo, logger: Logger): Promise<string | null> {\n    const updateInfoFilePath: string = this.getUpdateInfoFile()\n\n    const doesUpdateInfoFileExist = await pathExists(updateInfoFilePath)\n    if (!doesUpdateInfoFileExist) {\n      return null\n    }\n\n    let cachedInfo: CachedUpdateInfo\n    try {\n      cachedInfo = await readJson(updateInfoFilePath)\n    } catch (error: any) {\n      let message = `No cached update info available`\n      if (error.code !== \"ENOENT\") {\n        await this.cleanCacheDirForPendingUpdate()\n        message += ` (error on read: ${error.message})`\n      }\n      logger.info(message)\n      return null\n    }\n\n    const isCachedInfoFileNameValid = cachedInfo?.fileName !== null\n    if (!isCachedInfoFileNameValid) {\n      logger.warn(`Cached update info is corrupted: no fileName, directory for cached update will be cleaned`)\n      await this.cleanCacheDirForPendingUpdate()\n      return null\n    }\n\n    if (fileInfo.info.sha512 !== cachedInfo.sha512) {\n      logger.info(\n        `Cached update sha512 checksum doesn't match the latest available update. New update must be downloaded. Cached: ${cachedInfo.sha512}, expected: ${fileInfo.info.sha512}. Directory for cached update will be cleaned`\n      )\n      await this.cleanCacheDirForPendingUpdate()\n      return null\n    }\n\n    const updateFile = path.join(this.cacheDirForPendingUpdate, cachedInfo.fileName)\n    if (!(await pathExists(updateFile))) {\n      logger.info(\"Cached update file doesn't exist\")\n      return null\n    }\n\n    const sha512 = await hashFile(updateFile)\n    if (fileInfo.info.sha512 !== sha512) {\n      logger.warn(`Sha512 checksum doesn't match the latest available update. New update must be downloaded. Cached: ${sha512}, expected: ${fileInfo.info.sha512}`)\n      await this.cleanCacheDirForPendingUpdate()\n      return null\n    }\n    this._downloadedFileInfo = cachedInfo\n    return updateFile\n  }\n\n  private getUpdateInfoFile(): string {\n    return path.join(this.cacheDirForPendingUpdate, \"update-info.json\")\n  }\n}\n\ninterface CachedUpdateInfo {\n  fileName: string\n  sha512: string\n  readonly isAdminRightsRequired: boolean\n}\n\nfunction hashFile(file: string, algorithm = \"sha512\", encoding: \"base64\" | \"hex\" = \"base64\", options?: any): Promise<string> {\n  return new Promise<string>((resolve, reject) => {\n    const hash = createHash(algorithm)\n    hash.on(\"error\", reject).setEncoding(encoding)\n\n    createReadStream(file, { ...options, highWaterMark: 1024 * 1024 /* better to use more memory but hash faster */ })\n      .on(\"error\", reject)\n      .on(\"end\", () => {\n        hash.end()\n        resolve(hash.read() as string)\n      })\n      .pipe(hash, { end: false })\n  })\n}\n\nexport async function createTempUpdateFile(name: string, cacheDir: string, log: Logger): Promise<string> {\n  // https://github.com/electron-userland/electron-builder/pull/2474#issuecomment-366481912\n  let nameCounter = 0\n  let result = path.join(cacheDir, name)\n  for (let i = 0; i < 3; i++) {\n    try {\n      await unlink(result)\n      return result\n    } catch (e: any) {\n      if (e.code === \"ENOENT\") {\n        return result\n      }\n\n      log.warn(`Error on remove temp update file: ${e}`)\n      result = path.join(cacheDir, `${nameCounter++}-${name}`)\n    }\n  }\n  return result\n}\n"]}