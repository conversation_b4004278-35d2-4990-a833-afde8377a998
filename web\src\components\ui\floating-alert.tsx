"use client";

import * as React from "react";
import { X, CheckCircle, AlertCircle, Info, XCircle } from "lucide-react";
import { cn } from "@/lib/utils";

export interface FloatingAlertProps {
  id: string;
  title?: string;
  description?: string;
  variant?: "default" | "success" | "error" | "warning" | "info";
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  duration?: number;
}

const FloatingAlert = React.forwardRef<HTMLDivElement, FloatingAlertProps>(
  ({ id, title, description, variant = "default", open = true, onOpenChange, duration = 5000 }, ref) => {
    const [isVisible, setIsVisible] = React.useState(false);
    const [isExiting, setIsExiting] = React.useState(false);

    React.useEffect(() => {
      if (open) {
        // Small delay to trigger animation
        const timer = setTimeout(() => setIsVisible(true), 100);
        return () => clearTimeout(timer);
      }
    }, [open]);

    React.useEffect(() => {
      if (duration && open) {
        const timer = setTimeout(() => {
          handleDismiss();
        }, duration);
        return () => clearTimeout(timer);
      }
    }, [duration, open]);

    const handleDismiss = () => {
      setIsExiting(true);
      setTimeout(() => {
        onOpenChange?.(false);
        setIsExiting(false);
        setIsVisible(false);
      }, 300);
    };

    const getVariantStyles = () => {
      switch (variant) {
        case "success":
          return {
            bg: "bg-gradient-to-r from-green-500 to-green-600",
            border: "border-green-400/30",
            icon: CheckCircle,
            iconColor: "text-green-100",
          };
        case "error":
          return {
            bg: "bg-gradient-to-r from-red-500 to-red-600",
            border: "border-red-400/30",
            icon: XCircle,
            iconColor: "text-red-100",
          };
        case "warning":
          return {
            bg: "bg-gradient-to-r from-yellow-500 to-yellow-600",
            border: "border-yellow-400/30",
            icon: AlertCircle,
            iconColor: "text-yellow-100",
          };
        case "info":
          return {
            bg: "bg-gradient-to-r from-blue-500 to-blue-600",
            border: "border-blue-400/30",
            icon: Info,
            iconColor: "text-blue-100",
          };
        default:
          return {
            bg: "bg-gradient-to-r from-gray-700 to-gray-800",
            border: "border-gray-600/30",
            icon: Info,
            iconColor: "text-gray-100",
          };
      }
    };

    const variantStyles = getVariantStyles();
    const IconComponent = variantStyles.icon;

    if (!open) return null;

    return (
      <div
        ref={ref}
        className={cn(
          "fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50",
          "transition-all duration-300 ease-out",
          isVisible && !isExiting
            ? "translate-y-0 opacity-100 scale-100"
            : "translate-y-full opacity-0 scale-95"
        )}
      >
        <div
          className={cn(
            "relative max-w-md mx-4",
            "rounded-xl shadow-2xl backdrop-blur-sm",
            "border border-white/20",
            variantStyles.bg,
            variantStyles.border,
            "overflow-hidden"
          )}
        >
          {/* Floating island effect */}
          <div className="absolute inset-0 bg-gradient-to-b from-white/10 to-transparent" />
          
          {/* Shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer" />
          
          <div className="relative py-3 px-4 pr-10">
            <div className="flex items-center gap-3">
              <IconComponent className={cn("w-4 h-4 flex-shrink-0", variantStyles.iconColor)} />
              <div className="flex-1 min-w-0">
                {title && (
                  <h4 className="text-xs font-semibold text-white mb-0.5 leading-tight">
                    {title}
                  </h4>
                )}
                {description && (
                  <p className="text-xs text-white/90 leading-tight">
                    {description}
                  </p>
                )}
              </div>
            </div>
            
            {/* Close button */}
            <button
              onClick={handleDismiss}
              className="absolute top-2 right-2 p-1 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-200"
              aria-label="Close alert"
              title="Close alert"
            >
              <X className="w-3 h-3 text-white" />
            </button>
          </div>
        </div>
      </div>
    );
  }
);

FloatingAlert.displayName = "FloatingAlert";

export { FloatingAlert }; 