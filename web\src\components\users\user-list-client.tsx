"use client";

import * as React from "react";
import {
  ChevronsUpDown,
  ChevronDown,
  MoreHorizontal,
  PlusCircle,
  Copy,
  Edit,
  Trash2,
} from "lucide-react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { User, UserRole } from "@/lib/types";
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
  } from "@/components/ui/avatar";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
  } from "@/components/ui/alert-dialog";
import { AddUserDialog } from "./add-user-dialog";
import { useAuth } from "@/hooks/use-auth";
import { doc, setDoc, deleteDoc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { EditUserDialog } from "./edit-user-dialog";

export default function UserListClient({ users: initialUsers }: { users: User[] }) {
    const { user: currentUser } = useAuth();
    const { toast } = useFloatingToast();
    const [users, setUsers] = React.useState(initialUsers);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({
      // Hide more columns on mobile by default
      email: window.innerWidth < 768 ? false : true,
      role: window.innerWidth < 1024 ? false : true,
      phone: false, // Always hidden as it's not essential
      childrenIds: false, // Always hidden as it's not essential
    });
    const [rowSelection, setRowSelection] = React.useState({});

    React.useEffect(() => {
        setUsers(initialUsers);
    }, [initialUsers]);

    const addUser = async (newUserData: Omit<User, 'id' | 'avatarUrl' | 'childrenIds'>) => {
        const standardPassword = 'password';
        const userWithDefaults = {
            ...newUserData,
            avatarUrl: `https://placehold.co/100x100.png`,
            childrenIds: [], // Parents get children added when students are created
        };
        
        // This is a placeholder for creating the user in Firebase Auth.
        // In a real app, you would use a Cloud Function to create the user to avoid exposing credentials.
        // For this demo, we'll just add them to the Firestore 'users' collection.
        // You MUST manually create a corresponding user in the Firebase Authentication console.

        try {
            // Since we can't create an auth user on the client, we'll show a toast
            // and add to firestore. The admin needs to create the auth user manually.
            await setDoc(doc(db, "users", newUserData.email), userWithDefaults);

            setUsers(prev => [{...userWithDefaults, id: newUserData.email}, ...prev]);

            toast({
                title: "User Created in Firestore",
                description: (
                    <div className="text-sm">
                        <p>Account for {newUserData.name} has been created in the database.</p>
                        <p className="font-semibold mt-2 text-destructive">ACTION REQUIRED:</p>
                        <p>Manually create a new user in Firebase Authentication with:</p>
                        <p>Email: <span className="font-mono bg-muted px-1 rounded">{newUserData.email}</span></p>
                        <p>Password: <span className="font-mono bg-muted px-1 rounded">{standardPassword}</span></p>
                    </div>
                ),
                duration: 20000,
            });
        } catch (error) {
            console.error("Error adding user:", error);
            toast({
                title: "Error",
                description: "Failed to add user to Firestore.",
                variant: "error"
            });
        }
    }

    const updateUser = async (userId: string, updatedData: Partial<Omit<User, 'id' | 'avatarUrl' | 'childrenIds' | 'email'>>) => {
        try {
            const userRef = doc(db, "users", userId);
            await updateDoc(userRef, updatedData);

            setUsers(prev => prev.map(u => u.id === userId ? { ...u, ...updatedData } : u));
            
            toast({
                title: "User Updated",
                description: `${updatedData.name}'s record has been successfully updated.`,
            });
        } catch (error) {
            console.error("Error updating user:", error);
            toast({
                title: "Error",
                description: "Failed to update user. Please try again.",
                variant: "error",
            });
        }
    };
  
    const handleDeleteUser = async (userId: string) => {
        // As with adding, deleting from Auth should be a backend operation.
        // We will only delete from Firestore here.
        try {
            await deleteDoc(doc(db, "users", userId));
            setUsers(prev => prev.filter(u => u.id !== userId));
            toast({
                title: "User Deleted from Firestore",
                description: "The user has been removed. Remember to delete them from Firebase Auth too.",
                variant: 'error'
            });
        } catch (error) {
            console.error("Error deleting user:", error);
            toast({
                title: "Error",
                description: "Failed to delete user.",
                variant: "error"
            })
        }
    }

    const columns: ColumnDef<User>[] = [
        {
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected() ||
                (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              aria-label="Select row"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        },
        {
          accessorKey: "name",
          header: "Name",
          cell: ({ row }) => {
              const user = row.original;
              const initials = user.name.split(' ').map(n => n[0]).join('');
              return (
                  <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8 border" data-ai-hint="person portrait">
                          <AvatarImage src={user.avatarUrl} alt={user.name} />
                          <AvatarFallback>{initials}</AvatarFallback>
                      </Avatar>
                      <div className="font-medium">{user.name}</div>
                  </div>
              )
          },
        },
        {
          accessorKey: "email",
          header: "Email",
          cell: ({ row }) => <div>{row.getValue("email")}</div>,
        },
        {
          accessorKey: "role",
          header: ({ column }) => {
            return (
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              >
                Role
                <ChevronsUpDown className="ml-2 h-4 w-4" />
              </Button>
            );
          },
          cell: ({ row }) => {
              const role = row.getValue("role") as UserRole;
              return <Badge variant="outline" className="capitalize">{role}</Badge>;
          },
        },
        {
            accessorKey: "assignedClasses",
            header: "Details",
            cell: ({ row }) => {
              const user = row.original;
              if (user.role === 'teacher' && user.assignedClasses?.length) {
                return (
                  <div className="flex flex-wrap gap-1">
                    {user.assignedClasses.map(c => <Badge key={c} variant="secondary">{c}</Badge>)}
                  </div>
                )
              }
              if (user.role === 'parent') {
                const details = [];
                if (user.childrenIds?.length) {
                    details.push(`${user.childrenIds.length} children`);
                }
                if (user.phone) {
                    details.push(user.phone);
                }
                return <span className="text-muted-foreground text-sm">{details.join(' | ')}</span>
              }
              return <span className="text-muted-foreground text-sm">-</span>;
            },
          },
        {
          id: "actions",
          enableHiding: false,
          cell: ({ row }) => {
            const user = row.original;
            const isSelf = user.id === currentUser?.id;
            return (
                <AlertDialog>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0" disabled={isSelf}>
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                            onClick={() => {
                                navigator.clipboard.writeText(user.id);
                                toast({ title: "Copied!", description: "User ID copied to clipboard." });
                            }}
                        >
                            <Copy className="mr-2 h-4 w-4" />
                            Copy user ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <EditUserDialog user={user} onUpdateUser={updateUser}>
                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Edit className="mr-2 h-4 w-4" />Edit user
                            </DropdownMenuItem>
                        </EditUserDialog>
                        <AlertDialogTrigger asChild>
                            <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10">
                                <Trash2 className="mr-2 h-4 w-4"/>
                                Delete user
                            </DropdownMenuItem>
                        </AlertDialogTrigger>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the user's data from Firestore. You must also delete the user from Firebase Authentication manually.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
                                Continue
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
              </AlertDialog>
            );
          },
        },
      ];


  const table = useReactTable({
    data: users,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-4 justify-between flex-wrap gap-4">
        <Input
          placeholder="Filter users by name..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex items-center gap-2">
            <AddUserDialog onAddUser={addUser}>
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add User
                </Button>
            </AddUserDialog>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuItem
                      key={column.id}
                      className="capitalize"
                      // @ts-ignore
                      onSelect={(e) => {e.preventDefault(); column.toggleVisibility()}}
                    >
                      <Checkbox
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) =>
                            column.toggleVisibility(!!value)
                          }
                          aria-label="Toggle column"
                          className="mr-2"
                        />
                      {column.id.replace(/([A-Z])/g, ' $1')}
                    </DropdownMenuItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="whitespace-nowrap">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="whitespace-nowrap">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No users found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4 flex-wrap gap-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
