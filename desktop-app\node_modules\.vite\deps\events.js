import {
  EventEmitter,
  EventEmitterAsyncResource,
  addAbortListener,
  captureRejectionSymbol,
  captureRejections,
  defaultMaxListeners,
  errorMonitor,
  getEventListeners,
  getMaxListeners,
  init,
  init_events,
  kMaxEventTargetListeners,
  kMaxEventTargetListenersWarned,
  keyword_default,
  length,
  listenerCount,
  name,
  on,
  once,
  prototype,
  setMaxListeners,
  usingDomains
} from "./chunk-S2CAEYFU.js";
import "./chunk-PLDDJCW6.js";
init_events();
export {
  EventEmitter,
  EventEmitterAsyncResource,
  addAbortListener,
  captureRejectionSymbol,
  captureRejections,
  keyword_default as default,
  defaultMaxListeners,
  errorMonitor,
  getEventListeners,
  getMaxListeners,
  init,
  kMaxEventTargetListeners,
  kMaxEventTargetListenersWarned,
  length,
  listenerCount,
  name,
  on,
  once,
  prototype,
  setMaxListeners,
  usingDomains
};
