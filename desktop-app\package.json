{"name": "maggie-preparatory-school", "productName": "Maggie Preparatory School", "description": "School Management System for Maggie Preparatory School", "private": true, "version": "1.0.0", "type": "module", "author": "Maggie Preparatory School", "repository": {"type": "git", "url": "https://github.com/maggieprep/school-management.git"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron": "wait-on tcp:5173 && cross-env IS_DEV=true electron .", "electron:pack": "electron-builder", "electron:dist": "npm run build && electron-builder", "build:installer": "npm run build && node build-installer.js", "preelectron:pack": "npm run build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "electron-store": "^8.2.0", "firebase": "^12.0.0", "lucide-react": "^0.525.0", "mongodb": "^6.18.0", "mongoose": "^8.16.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.14", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.2.2", "vite": "^7.0.5", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js", "build": {"appId": "com.maggieprep.management", "productName": "Maggie Preparatory School", "directories": {"output": "dist-installer"}, "files": ["dist/**/*", "dist-electron/**/*", "public/**/*"], "win": {"target": "nsis", "icon": "public/logo.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Maggie Preparatory School", "installerIcon": "public/logo.ico", "uninstallerIcon": "public/logo.ico", "installerHeaderIcon": "public/logo.ico", "deleteAppDataOnUninstall": true, "runAfterFinish": true, "artifactName": "Maggie-Preparatory-School-Setup-${version}.${ext}", "displayLanguageSelector": false, "installerLanguages": ["en_US"], "language": "1033", "multiLanguageInstaller": false, "packElevateHelper": false, "perMachine": false, "allowElevation": true, "include": "installer/installer.nsh", "license": "installer/license.txt"}, "mac": {"target": "dmg", "icon": "public/logo.ico", "category": "public.app-category.education"}, "linux": {"target": "AppImage", "icon": "public/logo.ico", "category": "Education"}, "publish": null}}