{"name": "maggie-preparatory-school", "version": "1.0.0", "main": "dist-electron/main/main.js", "description": "School Management System", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "electron:dev": "electron .", "electron:build": "vite build && electron-builder", "electron:dist": "electron-builder --publish always", "electron:release": "electron-builder --publish always", "release": "standard-version && git push --follow-tags origin main"}, "build": {"appId": "com.maggieprep.management", "productName": "Maggie Preparatory School", "publish": [{"provider": "github", "owner": "Bosiakoba", "repo": "School-Management-Web-App-"}]}, "repository": {"type": "git", "url": "https://github.com/Bosiakoba/School-Management-Web-App-.git"}}