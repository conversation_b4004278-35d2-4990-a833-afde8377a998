import { ComponentLoggerOptions, Diag<PERSON>og<PERSON>unction, DiagLogger, <PERSON>agLogger<PERSON><PERSON> } from '../diag/types';
/**
 * Singleton object which represents the entry point to the OpenTelemetry internal
 * diagnostic API
 */
export declare class DiagAPI implements DiagLogger, DiagLogger<PERSON><PERSON> {
    private static _instance?;
    /** Get the singleton instance of the DiagAPI API */
    static instance(): DiagAPI;
    /**
     * Private internal constructor
     * @private
     */
    private constructor();
    setLogger: DiagLoggerApi['setLogger'];
    /**
     *
     */
    createComponentLogger: (options: ComponentLoggerOptions) => DiagLogger;
    verbose: DiagLogFunction;
    debug: DiagLogFunction;
    info: DiagLogFunction;
    warn: DiagLogFunction;
    error: DiagLogFunction;
    /**
     * Unregister the global logger and return to Noop
     */
    disable: () => void;
}
//# sourceMappingURL=diag.d.ts.map