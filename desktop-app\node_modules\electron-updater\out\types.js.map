{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": ";;;AA6CA,gCASC;AAtDD,+DAAmH;AAK1G,kGALA,wCAAiB,OAKA;AAEb,QAAA,iBAAiB,GAAG,mBAAmB,CAAA;AACvC,QAAA,iBAAiB,GAAG,mBAAmB,CAAA;AAYpD,MAAa,aAAa;IACxB,YAAoB,OAAqB;QAArB,YAAO,GAAP,OAAO,CAAc;IAAG,CAAC;IAE7C;;OAEG;IACH,KAAK,CAAC,OAAqB;QACzB,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IAC5C,CAAC;IAED,QAAQ,CAAC,OAAqC;QAC5C,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,yBAAiB,EAAE,OAAO,CAAC,CAAA;IACtD,CAAC;IAED,gBAAgB,CAAC,OAA8C;QAC7D,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,yBAAiB,EAAE,OAAO,CAAC,CAAA;IACtD,CAAC;IAED,eAAe,CAAC,OAAmC;QACjD,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC;CACF;AArBD,sCAqBC;AAED,MAAM,UAAU,GAAG,KAAK,CAAA;AAExB,SAAgB,UAAU,CAAC,OAAqB,EAAE,KAAoB,EAAE,OAAsC;IAC5G,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,IAAgB,EAAE,EAAE;YACxC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YACjC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAA;QAClB,CAAC,CAAC,CAAA;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;IAC5B,CAAC;AACH,CAAC", "sourcesContent": ["import { CancellationToken, PackageFileInfo, ProgressInfo, UpdateFileInfo, UpdateInfo } from \"builder-util-runtime\"\nimport { EventEmitter } from \"events\"\nimport { URL } from \"url\"\nimport { LoginCallback } from \"./electronHttpExecutor\"\n\nexport { CancellationToken, PackageFileInfo, ProgressInfo, UpdateFileInfo, UpdateInfo }\n\nexport const DOWNLOAD_PROGRESS = \"download-progress\"\nexport const UPDATE_DOWNLOADED = \"update-downloaded\"\n\nexport interface Logger {\n  info(message?: any): void\n\n  warn(message?: any): void\n\n  error(message?: any): void\n\n  debug?(message: string): void\n}\n\nexport class UpdaterSignal {\n  constructor(private emitter: EventEmitter) {}\n\n  /**\n   * Emitted when an authenticating proxy is [asking for user credentials](https://github.com/electron/electron/blob/master/docs/api/client-request.md#event-login).\n   */\n  login(handler: LoginHandler): void {\n    addHandler(this.emitter, \"login\", handler)\n  }\n\n  progress(handler: (info: ProgressInfo) => void): void {\n    addHandler(this.emitter, DOWNLOAD_PROGRESS, handler)\n  }\n\n  updateDownloaded(handler: (info: UpdateDownloadedEvent) => void): void {\n    addHandler(this.emitter, UPDATE_DOWNLOADED, handler)\n  }\n\n  updateCancelled(handler: (info: UpdateInfo) => void): void {\n    addHandler(this.emitter, \"update-cancelled\", handler)\n  }\n}\n\nconst isLogEvent = false\n\nexport function addHandler(emitter: EventEmitter, event: UpdaterEvents, handler: (...args: Array<any>) => void): void {\n  if (isLogEvent) {\n    emitter.on(event, (...args: Array<any>) => {\n      console.log(\"%s %s\", event, args)\n      handler(...args)\n    })\n  } else {\n    emitter.on(event, handler)\n  }\n}\n\nexport interface UpdateCheckResult {\n  readonly isUpdateAvailable: boolean\n\n  readonly updateInfo: UpdateInfo\n\n  readonly downloadPromise?: Promise<Array<string>> | null\n\n  readonly cancellationToken?: CancellationToken\n\n  /** @deprecated */\n  readonly versionInfo: UpdateInfo\n}\n\nexport interface UpdateDownloadedEvent extends UpdateInfo {\n  downloadedFile: string\n}\n\nexport interface ResolvedUpdateFileInfo {\n  readonly url: URL\n  readonly info: UpdateFileInfo\n\n  packageInfo?: PackageFileInfo\n}\n\nexport type UpdaterEvents = \"login\" | \"checking-for-update\" | \"update-available\" | \"update-not-available\" | \"update-cancelled\" | \"download-progress\" | \"update-downloaded\" | \"error\"\n\nexport type LoginHandler = (authInfo: any, callback: LoginCallback) => void\n"]}