
"use client";

import React, { useEffect, useState } from "react";
import { Result, StudentR<PERSON>ult, type Student, type User } from "@/lib/types";
import { Users } from "lucide-react";
import { 
  collection, 
  onSnapshot, 
  getDocs, 
  query, 
  where,
  orderBy
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { FileText, Award, TrendingUp } from 'lucide-react';
import { 
  getGradeColor,
  calculateOverallGrade
} from '@/lib/results-utils';
import { getCurrentAcademicYear, getAvailableAcademicYears } from "@/lib/business-logic";

interface ParentDashboardProps {
  user: User;
}

export default function ParentDashboard({ user }: ParentDashboardProps) {
  const [children, setChildren] = useState<Student[]>([]);
  const [results, setResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedChild, setSelectedChild] = useState<Student | null>(null);
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [selectedTerm, setSelectedTerm] = useState<string>("all");
  const [selectedYear, setSelectedYear] = useState(getCurrentAcademicYear());

  useEffect(() => {
    if (!user.childrenIds || user.childrenIds.length === 0) {
      setLoading(false);
      return;
    }

    // Fetch students
    const studentsQuery = query(
      collection(db, "students"), 
      where("__name__", "in", user.childrenIds)
    );

    // Fetch results
    const resultsQuery = query(
      collection(db, "results"),
      where("studentId", "in", user.childrenIds.slice(0, 10)), // Firestore 'in' limit
      orderBy("createdAt", "desc")
    );

    // Set up real-time listeners
    const unsubscribeStudents = onSnapshot(studentsQuery, async (snapshot) => {
      const childrenData = await Promise.all(
        snapshot.docs.map(async (studentDoc) => {
          const student = { id: studentDoc.id, ...studentDoc.data() } as Student;
          return student;
        })
      );

      setChildren(childrenData);
    });

    const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
      const resultsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as StudentResult[];
      setResults(resultsData);
      setLoading(false);
    });

    // Cleanup subscriptions
    return () => {
      unsubscribeStudents();
      unsubscribeResults();
    };
  }, [user.childrenIds]);

  // Helper functions
  const getStudentResults = (studentId: string) => {
    return results.filter(r => r.studentId === studentId);
  };

  const getStudentSummary = (studentId: string) => {
    const studentResults = getStudentResults(studentId);
    
    if (studentResults.length === 0) return null;

    // Filter results by selected term and year if applicable
    const filteredResults = studentResults.filter(r => 
      (selectedTerm === "all" || r.term === selectedTerm) &&
      (selectedYear === "all" || r.academicYear === selectedYear)
    );

    if (filteredResults.length === 0) return null;

    const totalMarks = filteredResults.reduce((sum, r) => sum + (r.marks || 0), 0);
    const totalPossible = filteredResults.reduce((sum, r) => sum + (r.totalMarks || 0), 0);
    const averagePercentage = totalPossible > 0 ? Math.round((totalMarks / totalPossible) * 100) : 0;
    const passedSubjects = filteredResults.filter(r => r.isPassed).length;

    return {
      totalSubjects: filteredResults.length,
      passedSubjects,
      averagePercentage,
      overallGrade: calculateOverallGrade(filteredResults)
    };
  };

  const handleViewResults = (child: Student) => {
    setSelectedChild(child);
    setShowResultsModal(true);
  };

  const closeResultsModal = () => {
    setSelectedChild(null);
    setShowResultsModal(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (children.length === 0) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Access to Results</h3>
          <p className="text-gray-600">
            No children found in your account. Please contact the school administration.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Children</h1>
          <p className="text-gray-600 mt-2">View and track your children's academic progress</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex flex-wrap gap-4">
          <select
            value={selectedTerm}
            onChange={(e) => setSelectedTerm(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
            <option value="all">All Terms</option>
            <option value="First">First Term</option>
            <option value="Second">Second Term</option>
            <option value="Third">Third Term</option>
          </select>

          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
            {getAvailableAcademicYears().map((year) => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Children List */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            Children ({children.length})
          </h2>
          {/* Mobile scroll indicator */}
          <div className="md:hidden text-xs text-gray-500 flex items-center gap-1">
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
            </svg>
            Scroll
          </div>
        </div>

        {/* Desktop: Vertical list, Mobile: Horizontal scroll */}
        <div className="md:space-y-4">
          {/* Mobile horizontal scroll container */}
          <div className="md:hidden">
            <div className="flex gap-4 overflow-x-auto pb-4 px-1 scrollbar-hide snap-x snap-mandatory">
              {children.map((child) => {
                const studentResults = getStudentResults(child.id);
                const summary = getStudentSummary(child.id);

                return (
                  <div
                    key={child.id}
                    className="flex-shrink-0 w-80 p-5 bg-gradient-to-br from-white/60 to-white/40 backdrop-blur-xl rounded-2xl hover:from-white/70 hover:to-white/50 transition-all duration-300 border border-white/40 shadow-lg hover:shadow-xl snap-center"
                  >
                    {/* Mobile card content */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-white font-bold text-sm shadow-lg">
                          {child.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-gray-900 truncate text-lg">{child.name}</h3>
                          <Badge variant="outline" className="text-xs mt-1 bg-white/50">
                            {child.class}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-white/30 rounded-xl">
                          <span className="flex items-center gap-2 text-gray-700 font-medium">
                            <FileText className="w-4 h-4 text-green-600" />
                            Results
                          </span>
                          <span className="font-bold text-gray-900">{studentResults.length}</span>
                        </div>

                        {summary && (
                          <>
                            <div className="flex items-center justify-between p-3 bg-white/30 rounded-xl">
                              <span className="flex items-center gap-2 text-gray-700 font-medium">
                                <TrendingUp className="w-4 h-4 text-blue-600" />
                                Average
                              </span>
                              <span className="font-bold text-gray-900">{summary.averagePercentage}%</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-white/30 rounded-xl">
                              <span className="text-gray-700 font-medium">Overall Grade</span>
                              <Badge
                                style={{ backgroundColor: getGradeColor(summary.overallGrade), color: 'white' }}
                                className="text-sm font-bold px-3 py-1"
                              >
                                {summary.overallGrade}
                              </Badge>
                            </div>
                          </>
                        )}
                      </div>

                      <button
                        type="button"
                        onClick={() => handleViewResults(child)}
                        className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl font-semibold"
                      >
                        <Award className="w-5 h-5" />
                        <span className="text-sm">View Results</span>
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Desktop vertical list */}
          <div className="hidden md:block space-y-4">
            {children.map((child) => {
              const studentResults = getStudentResults(child.id);
              const summary = getStudentSummary(child.id);

              return (
                <div
                  key={child.id}
                  className="flex items-center justify-between p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-all duration-200"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                      {child.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-1">
                        <h3 className="font-semibold text-gray-900 truncate">{child.name}</h3>
                        <Badge variant="outline" className="text-xs">
                          {child.class}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <FileText className="w-4 h-4 text-green-600" />
                          {studentResults.length} results
                        </span>

                        {summary && (
                          <>
                            <span className="flex items-center gap-1">
                              <TrendingUp className="w-4 h-4 text-blue-600" />
                              {summary.averagePercentage}% avg
                            </span>
                            <Badge
                              style={{ backgroundColor: getGradeColor(summary.overallGrade), color: 'white' }}
                              className="text-xs"
                            >
                              {summary.overallGrade}
                            </Badge>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => handleViewResults(child)}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg ml-4"
                  >
                    <Award className="w-4 h-4" />
                    <span className="text-sm font-medium">View Results</span>
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Results Modal */}
      <Dialog open={showResultsModal} onOpenChange={closeResultsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Results for {selectedChild?.name || ''}</DialogTitle>
          </DialogHeader>

          {selectedChild && (
            <div className="space-y-6">
              {/* Student Header */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold">
                    {selectedChild.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{selectedChild.name}</h3>
                    <p className="text-sm text-gray-600">Class: {selectedChild.class}</p>
                  </div>
                </div>
              </div>

              {/* Results Content */}
              {(() => {
                const childResults = getStudentResults(selectedChild.id);
                const summary = getStudentSummary(selectedChild.id);

                // Filter results by selected term and year
                const filteredResults = childResults.filter(r => 
                  (selectedTerm === "all" || r.term === selectedTerm) &&
                  (selectedYear === "all" || r.academicYear === selectedYear)
                );

                if (!filteredResults.length) {
                  return (
                    <div className="text-center py-12">
                      <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Available</h3>
                      <p className="text-gray-600">
                        No results found for the selected term and year.
                      </p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-6">
                    {/* Summary Stats */}
                    {summary && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-xl">
                          <div className="text-2xl font-bold text-blue-600">{summary.totalSubjects}</div>
                          <div className="text-sm text-gray-600">Subjects</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-xl">
                          <div className="text-2xl font-bold text-green-600">{summary.averagePercentage}%</div>
                          <div className="text-sm text-gray-600">Average</div>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-xl">
                          <div className="text-2xl font-bold text-purple-600">{summary.passedSubjects}</div>
                          <div className="text-sm text-gray-600">Passed</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 rounded-xl">
                          <Badge
                            style={{ 
                              backgroundColor: getGradeColor(summary.overallGrade), 
                              color: 'white' 
                            }}
                            className="text-lg px-3 py-1"
                          >
                            {summary.overallGrade}
                          </Badge>
                          <div className="text-sm text-gray-600 mt-1">Overall Grade</div>
                        </div>
                      </div>
                    )}

                    {/* Individual Results */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">Subject Results</h4>
                      {filteredResults.map(result => (
                        <div 
                          key={result.id} 
                          className="flex justify-between items-center p-4 bg-gray-50 rounded-xl"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h5 className="font-medium text-gray-900">{result.subjectName}</h5>
                              <Badge variant="outline" className="text-xs">
                                {result.subjectCode}
                              </Badge>
                              {result.isPassed ? (
                                <Badge className="bg-green-100 text-green-800 text-xs">Passed</Badge>
                              ) : (
                                <Badge className="bg-red-100 text-red-800 text-xs">Failed</Badge>
                              )}
                            </div>
                            {result.remarks && (
                              <p className="text-sm text-gray-600">{result.remarks}</p>
                            )}
                            {result.teacherComment && (
                              <p className="text-sm text-blue-600 italic">"{result.teacherComment}"</p>
                            )}
                          </div>
                          <div className="text-right">
                            <Badge
                              style={{ 
                                backgroundColor: getGradeColor(result.grade), 
                                color: 'white' 
                              }}
                              className="text-lg px-3 py-1 mb-1"
                            >
                              {result.grade}
                            </Badge>
                            <p className="text-sm text-gray-600">
                              {result.marks}/{result.totalMarks} ({result.percentage}%)
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
