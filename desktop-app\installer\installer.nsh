# Professional NSIS installer customizations for Maggie Preparatory School Management System
# This script enhances the installation experience with custom branding and messages

# Professional welcome page customization
!define MUI_WELCOMEPAGE_TITLE "Welcome to Maggie Preparatory School Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of Maggie Preparatory School Management System.$\r$\n$\r$\nThis comprehensive school management solution helps administrators, teachers, and parents manage student information, academic records, and school operations efficiently.$\r$\n$\r$\n• Student Information Management$\r$\n• Academic Records & Grading$\r$\n• Parent-Teacher Communication$\r$\n• Administrative Tools$\r$\n$\r$\nClick Next to continue with the installation."

# License page customization
!define MUI_LICENSEPAGE_TEXT_TOP "Please review the license terms before installing Maggie Preparatory School Management System."
!define MUI_LICENSEPAGE_TEXT_BOTTOM "If you accept the terms of the agreement, click I Agree to continue. You must accept the agreement to install the software."

# Directory page customization
!define MUI_DIRECTORYPAGE_TEXT_TOP "Setup will install Maggie Preparatory School Management System in the following folder. To install in a different folder, click Browse and select another folder. Click Next to continue."

# Installation progress customization
!define MUI_INSTFILESPAGE_FINISHHEADER_TEXT "Installation Complete"
!define MUI_INSTFILESPAGE_FINISHHEADER_SUBTEXT "Maggie Preparatory School Management System has been successfully installed."

# Professional finish page customization
!define MUI_FINISHPAGE_TITLE "Completing the Maggie Preparatory School Setup Wizard"
!define MUI_FINISHPAGE_TEXT "Maggie Preparatory School Management System has been installed on your computer.$\r$\n$\r$\nThe application is now ready to use and can be launched from:$\r$\n• Desktop shortcut$\r$\n• Start Menu$\r$\n• Programs folder$\r$\n$\r$\nThank you for choosing our school management solution!$\r$\n$\r$\nClick Finish to close this wizard."

# Run after finish customization
!define MUI_FINISHPAGE_RUN_TEXT "Launch Maggie Preparatory School Management System"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "View README file"

# Note: Custom functions removed to avoid conflicts with electron-builder's built-in functions
# The professional messages above will be used by electron-builder's installer wizard
