{"version": 3, "file": "DebUpdater.js", "sourceRoot": "", "sources": ["../src/DebUpdater.ts"], "names": [], "mappings": ";;;AAGA,+CAA2D;AAC3D,mDAA+C;AAC/C,mCAA2C;AAE3C,MAAa,UAAW,SAAQ,yBAAW;IACzC,YAAY,OAAkC,EAAE,GAAgB;QAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACrB,CAAC;IAED,gBAAgB;IACN,gBAAgB,CAAC,qBAA4C;QACrE,MAAM,QAAQ,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAA;QACrE,MAAM,QAAQ,GAAG,IAAA,mBAAQ,EAAC,QAAQ,CAAC,YAAY,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAE,CAAA;QACzI,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,aAAa,EAAE,KAAK;YACpB,QAAQ;YACR,qBAAqB;YACrB,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,EAAE;gBAC1C,IAAI,IAAI,CAAC,aAAa,CAAC,yBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC9C,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,EAAE,CAAC,CAAA;gBACrE,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;YAC7E,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAc,aAAa;;QACzB,OAAO,MAAA,MAAA,KAAK,CAAC,aAAa,0CAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,mCAAI,IAAI,CAAA;IAC1D,CAAC;IAES,SAAS,CAAC,OAAuB;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC5B,4DAA4D;QAC5D,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAA;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACxC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAA;YAClF,OAAO,KAAK,CAAA;QACd,CAAC;QACD,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACjF,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC,CAAA;QACtF,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;QACrB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AA1CD,gCA0CC", "sourcesContent": ["import { AllPublishOptions } from \"builder-util-runtime\"\nimport { AppAdapter } from \"./AppAdapter\"\nimport { DownloadUpdateOptions } from \"./AppUpdater\"\nimport { BaseUpdater, InstallOptions } from \"./BaseUpdater\"\nimport { findFile } from \"./providers/Provider\"\nimport { DOWNLOAD_PROGRESS } from \"./types\"\n\nexport class DebUpdater extends BaseUpdater {\n  constructor(options?: AllPublishOptions | null, app?: AppAdapter) {\n    super(options, app)\n  }\n\n  /*** @private */\n  protected doDownloadUpdate(downloadUpdateOptions: DownloadUpdateOptions): Promise<Array<string>> {\n    const provider = downloadUpdateOptions.updateInfoAndProvider.provider\n    const fileInfo = findFile(provider.resolveFiles(downloadUpdateOptions.updateInfoAndProvider.info), \"deb\", [\"AppImage\", \"rpm\", \"pacman\"])!\n    return this.executeDownload({\n      fileExtension: \"deb\",\n      fileInfo,\n      downloadUpdateOptions,\n      task: async (updateFile, downloadOptions) => {\n        if (this.listenerCount(DOWNLOAD_PROGRESS) > 0) {\n          downloadOptions.onProgress = it => this.emit(DOWNLOAD_PROGRESS, it)\n        }\n        await this.httpExecutor.download(fileInfo.url, updateFile, downloadOptions)\n      },\n    })\n  }\n\n  protected get installerPath(): string | null {\n    return super.installerPath?.replace(/ /g, \"\\\\ \") ?? null\n  }\n\n  protected doInstall(options: InstallOptions): boolean {\n    const sudo = this.wrapSudo()\n    // pkexec doesn't want the command to be wrapped in \" quotes\n    const wrapper = /pkexec/i.test(sudo) ? \"\" : `\"`\n    const installerPath = this.installerPath\n    if (installerPath == null) {\n      this.dispatchError(new Error(\"No valid update available, can't quit and install\"))\n      return false\n    }\n    const cmd = [\"dpkg\", \"-i\", installerPath, \"||\", \"apt-get\", \"install\", \"-f\", \"-y\"]\n    this.spawnSyncLog(sudo, [`${wrapper}/bin/bash`, \"-c\", `'${cmd.join(\" \")}'${wrapper}`])\n    if (options.isForceRunAfter) {\n      this.app.relaunch()\n    }\n    return true\n  }\n}\n"]}