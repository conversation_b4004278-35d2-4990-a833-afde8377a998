export type ClassLevel = 
  | "Nursery 1" | "Nursery 2" | "KG 1" | "KG 2"
  | "Basic 1" | "Basic 2" | "Basic 3" | "Basic 4" | "Basic 5" | "Basic 6"
  | "Basic 7" | "Basic 8" | "Basic 9";

export type Grade = "A" | "B" | "C" | "D" | "E" | "F";

export type Term = "First" | "Second" | "Third";

export interface Subject {
  id: string;
  name: string;
  code: string; // e.g., "MATH", "ENG", "SCI"
  classLevels: ClassLevel[]; // Which classes study this subject
  isCore: boolean; // Core subjects vs electives
  createdAt: string;
  updatedAt: string;
  createdBy: string; // Admin user ID
}

export interface GradeScale {
  id: string;
  classLevel: ClassLevel;
  gradeRanges: {
    A: { min: number; max: number }; // e.g., 80-100
    B: { min: number; max: number }; // e.g., 70-79
    C: { min: number; max: number }; // e.g., 60-69
    D: { min: number; max: number }; // e.g., 50-59
    E: { min: number; max: number }; // e.g., 40-49
    F: { min: number; max: number }; // e.g., 0-39
  };
  totalMarks: number; // e.g., 100
  passMarkPercentage: number; // e.g., 40
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface StudentResult {
  id: string;
  studentId: string;
  studentName: string;
  classLevel: ClassLevel;
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  term: Term;
  academicYear: string; // e.g., "2024-2025"
  marks: number;
  totalMarks: number;
  grade: Grade;
  percentage: number;
  isPassed: boolean;
  remarks?: string;
  teacherComment?: string;
  uploadedBy: string; // Admin/Teacher user ID
  uploadedByName: string;
  createdAt: string;
  updatedAt: string;
}

export interface ClassSubjectAssignment {
  id: string;
  classLevel: ClassLevel;
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  isActive: boolean;
  assignedAt: string;
  assignedBy: string;
}

export interface ResultUploadData {
  studentId: string;
  subjectId: string;
  term: Term;
  academicYear: string;
  marks: number;
  remarks?: string;
  teacherComment?: string;
}

export interface ResultSummary {
  studentId: string;
  studentName: string;
  classLevel: ClassLevel;
  term: Term;
  academicYear: string;
  totalSubjects: number;
  totalMarks: number;
  totalObtained: number;
  averagePercentage: number;
  overallGrade: Grade;
  position?: number; // Class position
  totalStudents?: number; // Total students in class
  isPassed: boolean;
  results: StudentResult[];
  generatedAt: string;
}

// Default subjects for each class level
export const DEFAULT_SUBJECTS_BY_CLASS: Record<ClassLevel, string[]> = {
  "Nursery 1": ["English", "Mathematics", "Creative Arts", "Physical Education"],
  "Nursery 2": ["English", "Mathematics", "Creative Arts", "Physical Education", "Environmental Studies"],
  "KG 1": ["English", "Mathematics", "Creative Arts", "Physical Education", "Environmental Studies"],
  "KG 2": ["English", "Mathematics", "Creative Arts", "Physical Education", "Environmental Studies", "ICT"],
  "Basic 1": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT"],
  "Basic 2": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT"],
  "Basic 3": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT"],
  "Basic 4": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French"],
  "Basic 5": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French"],
  "Basic 6": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French"],
  "Basic 7": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French", "Career Technology"],
  "Basic 8": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French", "Career Technology"],
  "Basic 9": ["English", "Mathematics", "Science", "Social Studies", "Creative Arts", "Physical Education", "ICT", "French", "Career Technology"]
};

// Default grade scales for different class levels
export const DEFAULT_GRADE_SCALES: Record<string, GradeScale['gradeRanges']> = {
  "Early Years": { // Nursery 1, 2, KG 1, 2
    A: { min: 85, max: 100 },
    B: { min: 75, max: 84 },
    C: { min: 65, max: 74 },
    D: { min: 55, max: 64 },
    E: { min: 45, max: 54 },
    F: { min: 0, max: 44 }
  },
  "Primary": { // Basic 1-6
    A: { min: 80, max: 100 },
    B: { min: 70, max: 79 },
    C: { min: 60, max: 69 },
    D: { min: 50, max: 59 },
    E: { min: 40, max: 49 },
    F: { min: 0, max: 39 }
  },
  "Junior High": { // Basic 7-9
    A: { min: 80, max: 100 },
    B: { min: 70, max: 79 },
    C: { min: 60, max: 69 },
    D: { min: 50, max: 59 },
    E: { min: 40, max: 49 },
    F: { min: 0, max: 39 }
  }
};

export const CORE_SUBJECTS = ["English", "Mathematics", "Science"];

export const GRADE_POINTS: Record<Grade, number> = {
  A: 4.0,
  B: 3.0,
  C: 2.0,
  D: 1.0,
  E: 0.5,
  F: 0.0
};
