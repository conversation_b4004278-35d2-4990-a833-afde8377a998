import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import { useDocumentTitle } from '../hooks/use-document-title'
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { StudentPayment, PaymentRecord, FeeStructure } from '../lib/academic-fees-types'
import { formatCurrency, getCurrentAcademicYear } from '../lib/academic-fees-utils'

export default function Analytics() {
  const { user } = useAuth()
  const [students, setStudents] = useState<any[]>([])
  const [selectedClass, setSelectedClass] = useState('all')
  const [selectedAcademicYear] = useState(getCurrentAcademicYear())

  // Financial data states
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [studentPayments, setStudentPayments] = useState<StudentPayment[]>([])
  const [feeStructures, setFeeStructures] = useState<FeeStructure[]>([])
  // Removed loading state for consistent UI behavior

  useDocumentTitle('Analytics Dashboard')

  // Fetch financial data directly like other working pages
  useEffect(() => {
    if (!user) return

    const setupAnalyticsListeners = () => {
      const unsubscribes: (() => void)[] = []

      try {
        // Students listener
        const studentsQuery = query(collection(db, 'students'), orderBy('dateAdded', 'desc'))
        const unsubscribeStudents = onSnapshot(studentsQuery, (snapshot) => {
          const studentsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          setStudents(studentsData)
        })
        unsubscribes.push(unsubscribeStudents)

        // Payment records listener
        const paymentsQuery = query(
          collection(db, 'payments'),
          orderBy('paymentDate', 'desc')
        )
        const unsubscribePayments = onSnapshot(paymentsQuery, (snapshot) => {
          const paymentsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as PaymentRecord[]
          setPayments(paymentsData)
        })
        unsubscribes.push(unsubscribePayments)

        // Student payments listener
        const studentPaymentsQuery = query(
          collection(db, 'studentPayments'),
          orderBy('studentName')
        )
        const unsubscribeStudentPayments = onSnapshot(studentPaymentsQuery, (snapshot) => {
          const studentPaymentsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as StudentPayment[]
          setStudentPayments(studentPaymentsData)
        })
        unsubscribes.push(unsubscribeStudentPayments)

        // Fee structures listener
        const feeStructuresQuery = query(
          collection(db, 'feeStructures'),
          where('academicYear', '==', selectedAcademicYear),
          orderBy('classLevel')
        )
        const unsubscribeFeeStructures = onSnapshot(feeStructuresQuery, (snapshot) => {
          const feeStructuresData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as FeeStructure[]
          setFeeStructures(feeStructuresData)
        })
        unsubscribes.push(unsubscribeFeeStructures)

        console.log('✅ Analytics real-time listeners set up successfully')
      } catch (error) {
        console.error('❌ Error setting up analytics listeners:', error)
      }

      return unsubscribes
    }

    const unsubscribes = setupAnalyticsListeners()

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe())
    }
  }, [user, selectedAcademicYear])

  // Calculate real financial metrics with class filtering
  const getFinancialMetrics = () => {
    // Filter students based on selected class and user role
    const filteredStudents = (() => {
      if (selectedClass === 'all') {
        // For teachers, "all" means all their assigned classes only
        if (user?.role === 'teacher' && user?.assignedClasses) {
          return students.filter(student => user.assignedClasses.includes(student.class))
        }
        // For admins, "all" means all students
        return students
      } else {
        // Specific class selected
        return students.filter(student => student.class === selectedClass)
      }
    })()

    // Filter payments based on academic year and selected class
    const filteredPayments = payments.filter(payment => {
      // Filter by academic year (handle different formats)
      const paymentYear = payment.academicYear
      const isCorrectYear = !paymentYear ||
                           paymentYear === selectedAcademicYear ||
                           paymentYear === selectedAcademicYear.split('-')[0] // Handle "2024" vs "2024-2025"

      if (!isCorrectYear) return false

      // Filter by class based on user role and selected class
      if (selectedClass === 'all') {
        // For teachers, "all" means only their assigned classes
        if (user?.role === 'teacher' && user?.assignedClasses) {
          const student = students.find(s => s.id === payment.studentId)
          return student && user.assignedClasses.includes(student.class)
        }
        // For admins, "all" means all students
        return true
      }

      // Find the student for this payment to check their class for specific class selection
      const student = students.find(s => s.id === payment.studentId)
      return student && student.class === selectedClass
    })

    // For more accurate calculations, use studentPayments data which has both expected and paid amounts
    const totalRevenue = studentPayments
      .filter(sp => {
        // Apply same filtering as filteredStudentPayments
        const spYear = sp.academicYear
        const isCorrectYear = !spYear || spYear === selectedAcademicYear || spYear === selectedAcademicYear.split('-')[0]
        if (!isCorrectYear) return false

        if (selectedClass === 'all') {
          if (user?.role === 'teacher' && user?.assignedClasses) {
            const student = students.find(s => s.id === sp.studentId)
            return student && user.assignedClasses.includes(student.class)
          }
          return true
        }

        const student = students.find(s => s.id === sp.studentId)
        return student && student.class === selectedClass
      })
      .reduce((sum, sp) => sum + (sp.totalAmountPaid || 0), 0)

    const expectedRevenue = studentPayments
      .filter(sp => {
        // Apply same filtering as filteredStudentPayments
        const spYear = sp.academicYear
        const isCorrectYear = !spYear || spYear === selectedAcademicYear || spYear === selectedAcademicYear.split('-')[0]
        if (!isCorrectYear) return false

        if (selectedClass === 'all') {
          if (user?.role === 'teacher' && user?.assignedClasses) {
            const student = students.find(s => s.id === sp.studentId)
            return student && user.assignedClasses.includes(student.class)
          }
          return true
        }

        const student = students.find(s => s.id === sp.studentId)
        return student && student.class === selectedClass
      })
      .reduce((sum, sp) => sum + (sp.totalAmountDue || 0), 0)

    // Calculate collection rate
    const collectionRate = expectedRevenue > 0 ? (totalRevenue / expectedRevenue) * 100 : 0

    // Calculate monthly revenue trend from filtered payments
    const monthlyRevenue = filteredPayments.reduce((acc, payment) => {
      const month = new Date(payment.paymentDate).toLocaleDateString('en-US', { month: 'short' })
      acc[month] = (acc[month] || 0) + payment.amount
      return acc
    }, {} as Record<string, number>)

    // Calculate previous month for comparison
    const currentMonth = new Date().toLocaleDateString('en-US', { month: 'short' })
    const lastMonth = new Date(new Date().setMonth(new Date().getMonth() - 1)).toLocaleDateString('en-US', { month: 'short' })
    const currentMonthRevenue = monthlyRevenue[currentMonth] || 0
    const lastMonthRevenue = monthlyRevenue[lastMonth] || 0
    const revenueGrowth = lastMonthRevenue > 0 ? ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0

    // Calculate additional metrics for the selected class(es)
    const studentsWithFeeStructures = filteredStudents.filter(student =>
      feeStructures.some(fs => fs.classLevel === student.class)
    ).length

    // Filter student payments by academic year and class
    const filteredStudentPayments = studentPayments.filter(sp => {
      // Filter by academic year (handle different formats)
      const spYear = sp.academicYear
      const isCorrectYear = !spYear ||
                           spYear === selectedAcademicYear ||
                           spYear === selectedAcademicYear.split('-')[0] // Handle "2024" vs "2024-2025"

      return isCorrectYear
    })

    const studentsWithPayments = filteredStudentPayments.filter(sp => {
      if (selectedClass === 'all') {
        return sp.payments && sp.payments.length > 0 && sp.totalAmountPaid > 0
      } else {
        return sp.classLevel === selectedClass && sp.payments && sp.payments.length > 0 && sp.totalAmountPaid > 0
      }
    }).length

    console.log('📊 Analytics Financial Metrics:', {
      selectedClass,
      filteredStudents: filteredStudents.length,
      studentsWithFeeStructures,
      studentsWithPayments,
      totalRevenue,
      expectedRevenue,
      collectionRate: collectionRate.toFixed(1) + '%'
    })

    return {
      totalRevenue,
      expectedRevenue,
      collectionRate,
      revenueGrowth,
      monthlyRevenue,
      studentsWithFeeStructures,
      studentsWithPayments,
      filteredStudentsCount: filteredStudents.length
    }
  }

  // Calculate academic performance metrics with class filtering
  const getAcademicMetrics = () => {
    // Filter students based on selected class and user role
    const filteredStudents = (() => {
      if (selectedClass === 'all') {
        // For teachers, "all" means all their assigned classes only
        if (user?.role === 'teacher' && user?.assignedClasses) {
          return students.filter(student => user.assignedClasses.includes(student.class))
        }
        // For admins, "all" means all students
        return students
      } else {
        // Specific class selected
        return students.filter(student => student.class === selectedClass)
      }
    })()

    const studentsWithResults = filteredStudents.filter(s => s.results && s.results.length > 0)
    const totalResults = studentsWithResults.reduce((sum, s) => sum + s.results.length, 0)

    // Calculate average grade (assuming grades are stored as percentages)
    let totalGradePoints = 0
    let gradeCount = 0

    studentsWithResults.forEach(student => {
      student.results.forEach(result => {
        // Convert grade to numeric if it's a letter grade
        const numericGrade = parseFloat(result.grade) || convertLetterGradeToNumeric(result.grade)
        if (!isNaN(numericGrade)) {
          totalGradePoints += numericGrade
          gradeCount++
        }
      })
    })

    const averageGrade = gradeCount > 0 ? totalGradePoints / gradeCount : 0
    const letterGrade = convertNumericToLetterGrade(averageGrade)

    return {
      studentsWithResults: studentsWithResults.length,
      totalResults,
      averageGrade,
      letterGrade,
      performanceRate: filteredStudents.length > 0 ? (studentsWithResults.length / filteredStudents.length) * 100 : 0,
      totalFilteredStudents: filteredStudents.length
    }
  }

  // Helper functions for grade conversion
  const convertLetterGradeToNumeric = (grade: string): number => {
    const gradeMap: Record<string, number> = {
      'A+': 95, 'A': 90, 'A-': 87,
      'B+': 83, 'B': 80, 'B-': 77,
      'C+': 73, 'C': 70, 'C-': 67,
      'D+': 63, 'D': 60, 'D-': 57,
      'F': 50
    }
    return gradeMap[grade.toUpperCase()] || 0
  }

  const convertNumericToLetterGrade = (numeric: number): string => {
    if (numeric >= 93) return 'A+'
    if (numeric >= 90) return 'A'
    if (numeric >= 87) return 'A-'
    if (numeric >= 83) return 'B+'
    if (numeric >= 80) return 'B'
    if (numeric >= 77) return 'B-'
    if (numeric >= 73) return 'C+'
    if (numeric >= 70) return 'C'
    if (numeric >= 67) return 'C-'
    if (numeric >= 60) return 'D'
    return 'F'
  }

  // Available classes based on user role
  const getAvailableClasses = () => {
    if (user?.role === 'admin') {
      return [
        'all', 'Nursery 1', 'Nursery 2', 'KG 1', 'KG 2',
        'Basic 1', 'Basic 2', 'Basic 3', 'Basic 4', 'Basic 5', 'Basic 6',
        'Basic 7', 'Basic 8', 'Basic 9'
      ]
    } else if (user?.role === 'teacher') {
      return ['all', ...(user?.assignedClasses || ['Basic 5', 'Basic 6'])]
    }
    return ['all'] // Parents don't need class filtering
  }

  const availableClasses = getAvailableClasses()

  // Filter students by selected class and user role
  const filteredStudents = (() => {
    if (selectedClass === 'all') {
      // For teachers, "all" means all their assigned classes only
      if (user?.role === 'teacher' && user?.assignedClasses) {
        return students.filter(student => user.assignedClasses.includes(student.class))
      }
      // For admins, "all" means all students
      return students
    } else {
      // Specific class selected
      return students.filter(student => student.class === selectedClass)
    }
  })()

  // Calculate real-time data for charts
  const getRealTimeData = () => {
    // Calculate enrollment by class
    const classCounts: { [key: string]: number } = {}
    students.forEach(student => {
      classCounts[student.class] = (classCounts[student.class] || 0) + 1
    })

    const gradeDistribution = Object.entries(classCounts).map(([className, count], index) => ({
      grade: className,
      count,
      color: [
        'from-blue-400 to-blue-600',
        'from-green-400 to-green-600',
        'from-purple-400 to-purple-600',
        'from-orange-400 to-orange-600',
        'from-pink-400 to-pink-600',
        'from-cyan-400 to-cyan-600',
        'from-red-400 to-red-600',
        'from-yellow-400 to-yellow-600',
        'from-indigo-400 to-indigo-600',
        'from-teal-400 to-teal-600'
      ][index % 10]
    }))

    // Calculate performance metrics from real results
    const subjectAverages: { [key: string]: { total: number; count: number } } = {}

    filteredStudents.forEach(student => {
      if (student.results && student.results.length > 0) {
        student.results.forEach((result: any) => {
          if (result.subject && typeof result.score === 'number') {
            if (!subjectAverages[result.subject]) {
              subjectAverages[result.subject] = { total: 0, count: 0 }
            }
            subjectAverages[result.subject].total += result.score
            subjectAverages[result.subject].count += 1
          }
        })
      }
    })

    const performanceMetrics = Object.entries(subjectAverages).map(([subject, data], index) => ({
      subject,
      average: Math.round(data.total / data.count),
      trend: 'stable' as const,
      color: [
        'text-blue-600',
        'text-green-600',
        'text-purple-600',
        'text-orange-600',
        'text-pink-600',
        'text-cyan-600'
      ][index % 6]
    }))

    // Mock enrollment trend data (could be enhanced with historical data)
    const enrollmentData = [
      { month: 'Jan', students: Math.max(students.length - 65, 0) },
      { month: 'Feb', students: Math.max(students.length - 50, 0) },
      { month: 'Mar', students: Math.max(students.length - 35, 0) },
      { month: 'Apr', students: Math.max(students.length - 20, 0) },
      { month: 'May', students: Math.max(students.length - 5, 0) },
      { month: 'Jun', students: students.length },
    ]

    return {
      enrollmentData,
      gradeDistribution,
      performanceMetrics
    }
  }

  // Get real-time data
  const currentData = getRealTimeData()
  const { enrollmentData, gradeDistribution, performanceMetrics } = currentData

  // Get financial and academic metrics
  const financialMetrics = getFinancialMetrics()
  const academicMetrics = getAcademicMetrics()

  const maxEnrollment = Math.max(...enrollmentData.map(d => d.students))
  const maxGradeCount = Math.max(...gradeDistribution.map(d => d.count))

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Analytics {selectedClass !== 'all' && `- ${selectedClass}`}
          </h1>
          <p className="text-gray-600">
            {selectedClass === 'all'
              ? 'Comprehensive insights and performance metrics across all classes'
              : `Detailed analytics and performance metrics for ${selectedClass}`
            }
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Class Filter Dropdown */}
          {(user?.role === 'admin' || user?.role === 'teacher') && (
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Class:</label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                aria-label="Select class for analytics"
                title="Select class for analytics"
                className="px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-700 hover:bg-white/70 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {availableClasses.map((className) => (
                  <option key={className} value={className}>
                    {className === 'all' ? 'All Classes' : className}
                  </option>
                ))}
              </select>
            </div>
          )}

          <button
            type="button"
            className="flex items-center space-x-2 px-4 py-2 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-700 hover:bg-white/70 transition-all duration-200"
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <span>Export Report</span>
          </button>
          <button
            type="button"
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <span>Download Data</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(financialMetrics.totalRevenue)}
              </p>
              <p className={`text-xs flex items-center mt-1 ${
                financialMetrics.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d={financialMetrics.revenueGrowth >= 0 ? "M7 17l9.2-9.2M17 17V7H7" : "M17 7l-9.2 9.2M7 7v10h10"} />
                </svg>
                {financialMetrics.revenueGrowth >= 0 ? '+' : ''}{financialMetrics.revenueGrowth.toFixed(1)}% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Fee Collection Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {`${financialMetrics.collectionRate.toFixed(1)}%`}
              </p>
              <p className={`text-xs flex items-center mt-1 ${
                financialMetrics.collectionRate >= 80 ? 'text-green-600' :
                financialMetrics.collectionRate >= 60 ? 'text-orange-600' : 'text-red-600'
              }`}>
                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d={financialMetrics.collectionRate >= 80 ? "M7 17l9.2-9.2M17 17V7H7" : "M20 12H4"} />
                </svg>
                {financialMetrics.collectionRate >= 80 ? 'Excellent' :
                 financialMetrics.collectionRate >= 60 ? 'Good' : 'Needs attention'}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Grade</p>
              <p className="text-2xl font-bold text-gray-900">
                {academicMetrics.letterGrade || 'N/A'}
              </p>
              <p className={`text-xs flex items-center mt-1 ${
                academicMetrics.averageGrade >= 80 ? 'text-green-600' :
                academicMetrics.averageGrade >= 70 ? 'text-orange-600' : 'text-red-600'
              }`}>
                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d={academicMetrics.averageGrade >= 80 ? "M7 17l9.2-9.2M17 17V7H7" : "M20 12H4"} />
                </svg>
                {academicMetrics.averageGrade >= 80 ? 'Excellent performance' :
                 academicMetrics.averageGrade >= 70 ? 'Good performance' : 'Needs improvement'}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Students with Results</p>
              <p className="text-2xl font-bold text-gray-900">
                {`${academicMetrics.studentsWithResults}/${academicMetrics.totalFilteredStudents}`}
              </p>
              <p className={`text-xs flex items-center mt-1 ${
                academicMetrics.performanceRate >= 80 ? 'text-green-600' :
                academicMetrics.performanceRate >= 60 ? 'text-orange-600' : 'text-red-600'
              }`}>
                <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d={academicMetrics.performanceRate >= 80 ? "M7 17l9.2-9.2M17 17V7H7" : "M20 12H4"} />
                </svg>
                {academicMetrics.performanceRate.toFixed(1)}% have results
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Financial Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expected Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(financialMetrics.expectedRevenue)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {selectedClass === 'all' ? 'All classes' : `${selectedClass} only`}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Students with Fee Structure</p>
              <p className="text-2xl font-bold text-gray-900">
                {`${financialMetrics.studentsWithFeeStructures}/${financialMetrics.filteredStudentsCount}`}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Eligible for payment
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Outstanding Amount</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(Math.max(0, financialMetrics.expectedRevenue - financialMetrics.totalRevenue))}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Amount yet to collect
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Enrollment Trend */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <span>Student Enrollment Trend</span>
          </h3>

          <div className="space-y-4">
            {enrollmentData.map((data) => (
              <div key={data.month} className="flex items-center space-x-4">
                <div className="w-12 text-sm font-medium text-gray-600">{data.month}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-3 relative overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-full rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${(data.students / maxEnrollment) * 100}%` }}
                  ></div>
                </div>
                <div className="w-12 text-sm font-semibold text-gray-900">{data.students}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Grade Distribution */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <span>Students by Grade</span>
          </h3>

          <div className="space-y-4">
            {gradeDistribution.map((grade) => (
              <div key={grade.grade} className="flex items-center space-x-4">
                <div className="w-16 text-sm font-medium text-gray-600">{grade.grade}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-3 relative overflow-hidden">
                  <div
                    className={`bg-gradient-to-r ${grade.color} h-full rounded-full transition-all duration-1000 ease-out`}
                    style={{ width: `${(grade.count / maxGradeCount) * 100}%` }}
                  ></div>
                </div>
                <div className="w-8 text-sm font-semibold text-gray-900">{grade.count}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <span>Subject Performance</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          {performanceMetrics.map((metric) => (
            <div key={metric.subject} className="text-center">
              <div className="relative w-24 h-24 mx-auto mb-4">
                <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="transparent"
                    className="text-gray-200"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="transparent"
                    strokeDasharray={`${(metric.average / 100) * 251.2} 251.2`}
                    className={metric.color}
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-900">{metric.average}%</span>
                </div>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">{metric.subject}</h4>
              <div className="flex items-center justify-center space-x-1">
                {metric.trend === 'up' && (
                  <svg className="w-3 h-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                )}
                {metric.trend === 'down' && (
                  <svg className="w-3 h-3 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                  </svg>
                )}
                {metric.trend === 'stable' && (
                  <svg className="w-3 h-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                )}
                <span className="text-xs text-gray-500 capitalize">{metric.trend}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Financial Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Revenue Trend */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <span>Monthly Revenue Collection</span>
          </h3>

          <div className="space-y-4">
            {Object.entries(financialMetrics.monthlyRevenue).map(([month, amount]) => {
              const maxAmount = Math.max(...Object.values(financialMetrics.monthlyRevenue))
              const percentage = maxAmount > 0 ? (amount / maxAmount) * 100 : 0

              return (
                <div key={month} className="flex items-center space-x-4">
                  <div className="w-12 text-sm font-medium text-gray-600">{month}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-3 relative overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-green-500 to-emerald-500 h-full rounded-full transition-all duration-1000 ease-out"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="w-20 text-sm font-semibold text-gray-900 text-right">
                    {formatCurrency(amount)}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Payment Status Distribution */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <span>Payment Status Overview</span>
          </h3>

          <div className="space-y-4">
            {(() => {
              // Filter student payments by academic year and selected class
              const filteredStudentPayments = studentPayments.filter(sp => {
                // Filter by academic year (handle different formats)
                const spYear = sp.academicYear
                const isCorrectYear = !spYear ||
                                     spYear === selectedAcademicYear ||
                                     spYear === selectedAcademicYear.split('-')[0] // Handle "2024" vs "2024-2025"

                if (!isCorrectYear) return false

                // Filter by class if specific class is selected
                return selectedClass === 'all' || sp.classLevel === selectedClass
              })

              const statusData = [
                { status: 'Fully Paid', count: filteredStudentPayments.filter(p => p.paymentStatus === 'completed').length, color: 'from-green-400 to-green-600' },
                { status: 'Partially Paid', count: filteredStudentPayments.filter(p => p.paymentStatus === 'partial').length, color: 'from-yellow-400 to-yellow-600' },
                { status: 'Pending', count: filteredStudentPayments.filter(p => p.paymentStatus === 'pending').length, color: 'from-orange-400 to-orange-600' },
                { status: 'Overdue', count: filteredStudentPayments.filter(p => p.paymentStatus === 'overdue').length, color: 'from-red-400 to-red-600' }
              ]

              const maxCount = Math.max(...statusData.map(item => item.count))

              return statusData.map((item) => {
                const percentage = maxCount > 0 ? (item.count / maxCount) * 100 : 0

              return (
                <div key={item.status} className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-gray-600">{item.status}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-3 relative overflow-hidden">
                    <div
                      className={`bg-gradient-to-r ${item.color} h-full rounded-full transition-all duration-1000 ease-out`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="w-8 text-sm font-semibold text-gray-900">{item.count}</div>
                </div>
              )
              })
            })()}
          </div>
        </div>
      </div>
    </div>
  )
}
