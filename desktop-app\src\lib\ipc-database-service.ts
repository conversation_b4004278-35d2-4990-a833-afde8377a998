// Renderer-side IPC client for communicating with main process database service

export interface DatabaseConfig {
  mongoUrl: string;
  mongoDatabase: string;
  syncEnabled: boolean;
}

export interface SyncMetadata {
  lastModified: Date;
  source: 'firebase' | 'mongodb';
  syncStatus: 'pending' | 'synced' | 'conflict';
  firebaseId?: string;
  mongoId?: string;
}

export interface DatabaseDocument {
  id?: string;
  syncMeta?: SyncMetadata;
  [key: string]: any;
}

export type ConnectionStatus = 'online' | 'offline' | 'syncing' | 'error';

// IPC Database Service for Renderer Process
export class IPCDatabaseService {
  private connectionStatus: ConnectionStatus = 'offline';
  private subscriptions: Map<string, () => void> = new Map();

  constructor() {
    // Check if we're in Electron environment
    if (!window.electronAPI) {
      console.warn('⚠️ Electron API not available - running in browser mode');
    }
  }

  // Connection Management
  async connect(): Promise<void> {
    try {
      console.log('🔄 Connecting to main process database...');
      
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('db:connect');
      
      if (!result.success) {
        throw new Error(result.error || 'Connection failed');
      }

      this.connectionStatus = 'offline'; // Will be updated by network monitor
      console.log('✅ Connected to main process database');
      
    } catch (error) {
      console.error('❌ Main process database connection failed:', error);
      this.connectionStatus = 'error';
      throw new Error(`Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (window.electronAPI) {
        await window.electronAPI.invoke('db:disconnect');
      }
      
      // Clear all subscriptions
      this.subscriptions.forEach(unsubscribe => unsubscribe());
      this.subscriptions.clear();
      
      this.connectionStatus = 'offline';
      console.log('🔌 Disconnected from main process database');
    } catch (error) {
      console.error('❌ Failed to disconnect from main process database:', error);
    }
  }

  // Connection Status
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
  }

  isOnline(): boolean {
    return this.connectionStatus === 'online' || this.connectionStatus === 'syncing';
  }

  // CRUD Operations
  async create(collectionName: string, data: any): Promise<string> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('db:create', collectionName, data);
      
      if (!result.success) {
        throw new Error(result.error || 'Create operation failed');
      }

      console.log(`✅ Created document in ${collectionName}:`, result.id);
      return result.id;

    } catch (error) {
      console.error(`❌ Failed to create document in ${collectionName}:`, error);
      throw error;
    }
  }

  async read(collectionName: string, query: any = {}): Promise<any[]> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('db:read', collectionName, query);
      
      if (!result.success) {
        throw new Error(result.error || 'Read operation failed');
      }

      return result.data || [];

    } catch (error) {
      console.error(`❌ Failed to read from ${collectionName}:`, error);
      throw error;
    }
  }

  async update(collectionName: string, id: string, data: any): Promise<void> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('db:update', collectionName, id, data);
      
      if (!result.success) {
        throw new Error(result.error || 'Update operation failed');
      }

      console.log(`✅ Updated document in ${collectionName}:`, id);

    } catch (error) {
      console.error(`❌ Failed to update document in ${collectionName}:`, error);
      throw error;
    }
  }

  async delete(collectionName: string, id: string): Promise<void> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('db:delete', collectionName, id);
      
      if (!result.success) {
        throw new Error(result.error || 'Delete operation failed');
      }

      console.log(`✅ Deleted document from ${collectionName}:`, id);

    } catch (error) {
      console.error(`❌ Failed to delete document from ${collectionName}:`, error);
      throw error;
    }
  }

  // Real-time Subscriptions (polling-based for now)
  subscribe(collectionName: string, callback: (data: any[]) => void): () => void {
    const subscriptionKey = `${collectionName}_${Date.now()}`;
    
    // Poll for changes every 2 seconds
    const pollInterval = setInterval(async () => {
      try {
        const data = await this.read(collectionName);
        callback(data);
      } catch (error) {
        console.error(`❌ Subscription error for ${collectionName}:`, error);
      }
    }, 2000);

    const unsubscribe = () => {
      clearInterval(pollInterval);
      this.subscriptions.delete(subscriptionKey);
    };

    this.subscriptions.set(subscriptionKey, unsubscribe);
    
    // Initial data load
    this.read(collectionName).then(callback).catch(console.error);

    return unsubscribe;
  }

  // Sync Queue Management
  async getSyncQueue(): Promise<Array<{ collection: string; operation: string; data: any; timestamp: Date }>> {
    try {
      if (!window.electronAPI) {
        return [];
      }

      return await window.electronAPI.invoke('db:getSyncQueue');
    } catch (error) {
      console.error('❌ Failed to get sync queue:', error);
      return [];
    }
  }

  async clearSyncQueue(): Promise<void> {
    try {
      if (!window.electronAPI) {
        return;
      }

      await window.electronAPI.invoke('db:clearSyncQueue');
    } catch (error) {
      console.error('❌ Failed to clear sync queue:', error);
    }
  }

  // Fallback to IndexedDB when Electron API is not available
  private async fallbackToIndexedDB(): Promise<void> {
    console.log('🔄 Falling back to IndexedDB for browser compatibility...');
    // Import and use the browser database service as fallback
    const { BrowserDatabaseService } = await import('./browser-database-service');
    // This would require more implementation to properly proxy calls
  }
}

// Default configuration
export const defaultDatabaseConfig: DatabaseConfig = {
  mongoUrl: 'mongodb://localhost:27017',
  mongoDatabase: 'maggie_school_offline',
  syncEnabled: true
};

// Singleton instance
let databaseServiceInstance: IPCDatabaseService | null = null;

export function getDatabaseService(config?: DatabaseConfig): IPCDatabaseService {
  if (!databaseServiceInstance) {
    databaseServiceInstance = new IPCDatabaseService();
  }
  return databaseServiceInstance;
}

// Type declarations for Electron API
declare global {
  interface Window {
    electronAPI?: {
      invoke: (channel: string, ...args: any[]) => Promise<any>;
      on: (channel: string, callback: (...args: any[]) => void) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}

// Export types for use in other files
export type { ConnectionStatus, SyncMetadata, DatabaseDocument };
