"use client";

import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ProfileImageModal } from './profile-image-modal';

interface ClickableAvatarProps {
  src?: string;
  alt: string;
  fallback: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showModal?: boolean;
}

const sizeClasses = {
  sm: 'h-8 w-8',
  md: 'h-12 w-12',
  lg: 'h-16 w-16',
  xl: 'h-20 w-20',
};

export function ClickableAvatar({ 
  src, 
  alt, 
  fallback, 
  size = 'md', 
  className = '',
  showModal = true 
}: ClickableAvatarProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    if (showModal && src) {
      setIsModalOpen(true);
    }
  };

  return (
    <>
      <Avatar 
        className={`${sizeClasses[size]} ${className} ${showModal && src ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}`}
        onClick={handleClick}
      >
        <AvatarImage src={src} alt={alt} />
        <AvatarFallback className="bg-gradient-to-br from-primary to-primary/70 text-white font-semibold">
          {fallback}
        </AvatarFallback>
      </Avatar>

      {showModal && src && (
        <ProfileImageModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          imageUrl={src}
          studentName={alt}
        />
      )}
    </>
  );
} 