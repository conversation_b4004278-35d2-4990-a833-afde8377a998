import { collection, getDocs, doc, writeBatch, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Development function to reset payment records and clean up orphaned records
export async function resetAllPaymentRecords() {
  try {
    // Admin password confirmation for destructive operation
    const adminPassword = prompt('🔐 Admin Password Required\n\nThis operation will:\n• Reset payments to zero for existing students\n• Remove payment records for deleted students\n\nPlease enter admin password to continue:');

    if (!adminPassword) {
      console.log('❌ Payment reset cancelled - no password provided');
      return;
    }

    // Simple password check (you can enhance this with proper authentication)
    if (adminPassword !== 'admin123') {
      alert('❌ Incorrect admin password. Operation cancelled.');
      console.log('❌ Payment reset cancelled - incorrect password');
      return;
    }

    console.log('🔄 Starting smart payment records reset...');

    // Get all current students in the system
    const studentsCollection = collection(db, 'students');
    const studentsSnapshot = await getDocs(studentsCollection);
    const existingStudentIds = new Set(studentsSnapshot.docs.map(doc => doc.id));

    console.log(`👥 Found ${existingStudentIds.size} students currently in system`);

    // Get all student payment records
    const studentPaymentsCollection = collection(db, 'studentPayments');
    const studentPaymentsSnapshot = await getDocs(studentPaymentsCollection);

    // Get all payment transaction records
    const paymentsCollection = collection(db, 'payments');
    const paymentsSnapshot = await getDocs(paymentsCollection);

    console.log(`📊 Found ${studentPaymentsSnapshot.docs.length} student payment records`);
    console.log(`📊 Found ${paymentsSnapshot.docs.length} payment transaction records`);

    // Create a batch for efficient operations
    const batch = writeBatch(db);

    let resetCount = 0;
    let deletedOrphanedPayments = 0;
    let deletedOrphanedTransactions = 0;

    // Process student payment records
    studentPaymentsSnapshot.docs.forEach((docSnapshot) => {
      const paymentData = docSnapshot.data();
      const studentId = paymentData.studentId;
      const studentPaymentRef = doc(db, 'studentPayments', docSnapshot.id);

      if (existingStudentIds.has(studentId)) {
        // Student exists - reset their payments to zero
        batch.update(studentPaymentRef, {
          payments: [],
          totalAmountPaid: 0,
          amountRemaining: paymentData.totalAmountDue || 0,
          paymentStatus: 'pending',
          lastPaymentDate: null,
          updatedAt: new Date().toISOString()
        });
        resetCount++;
      } else {
        // Student doesn't exist - delete the orphaned payment record
        batch.delete(studentPaymentRef);
        deletedOrphanedPayments++;
        console.log(`🗑️ Deleting orphaned payment record for student: ${studentId} (${paymentData.studentName || 'Unknown'})`);
      }
    });

    // Process payment transaction records
    paymentsSnapshot.docs.forEach((docSnapshot) => {
      const transactionData = docSnapshot.data();
      const studentId = transactionData.studentId;
      const paymentRef = doc(db, 'payments', docSnapshot.id);

      if (existingStudentIds.has(studentId)) {
        // Student exists - delete their transaction (part of reset)
        batch.delete(paymentRef);
      } else {
        // Student doesn't exist - delete the orphaned transaction
        batch.delete(paymentRef);
        deletedOrphanedTransactions++;
        console.log(`🗑️ Deleting orphaned transaction for student: ${studentId} (${transactionData.studentName || 'Unknown'})`);
      }
    });

    // Commit all changes
    await batch.commit();

    console.log('🎉 Smart payment reset completed!');
    alert('✅ Payment records have been cleaned up!\n\n' +
          `📊 Summary:\n` +
          `• ${resetCount} existing students: Payments reset to zero\n` +
          `• ${deletedOrphanedPayments} orphaned payment records: Deleted\n` +
          `• ${deletedOrphanedTransactions} orphaned transactions: Deleted\n` +
          `• ${paymentsSnapshot.docs.length - deletedOrphanedTransactions} current student transactions: Deleted\n\n` +
          'Payment system is now clean and synchronized with student records!');

  } catch (error) {
    console.error('❌ Error in smart payment reset:', error);
    alert('❌ Error resetting payment records. Check console for details.');
  }
}

// Function to clean up all student-related records when deleting a student
export async function cleanupStudentRecords(studentId: string, studentName?: string) {
  try {
    console.log(`🧹 Starting cleanup for student: ${studentId} (${studentName || 'Unknown'})`);
    
    // Create a batch for efficient operations
    const batch = writeBatch(db);
    
    // 1. Find and delete student payment records
    const studentPaymentsQuery = query(
      collection(db, 'studentPayments'),
      where('studentId', '==', studentId)
    );
    const studentPaymentsSnapshot = await getDocs(studentPaymentsQuery);
    
    console.log(`📊 Found ${studentPaymentsSnapshot.docs.length} student payment records to delete`);
    
    studentPaymentsSnapshot.docs.forEach((docSnapshot) => {
      const studentPaymentRef = doc(db, 'studentPayments', docSnapshot.id);
      batch.delete(studentPaymentRef);
    });
    
    // 2. Find and delete payment transaction records
    const paymentsQuery = query(
      collection(db, 'payments'),
      where('studentId', '==', studentId)
    );
    const paymentsSnapshot = await getDocs(paymentsQuery);
    
    console.log(`📊 Found ${paymentsSnapshot.docs.length} payment transaction records to delete`);
    
    paymentsSnapshot.docs.forEach((docSnapshot) => {
      const paymentRef = doc(db, 'payments', docSnapshot.id);
      batch.delete(paymentRef);
    });
    
    // 3. Delete the main student document
    const studentRef = doc(db, 'students', studentId);
    batch.delete(studentRef);
    
    // Commit all deletions
    await batch.commit();
    
    console.log(`✅ Student cleanup completed for: ${studentId}`);
    console.log(`   • Deleted ${studentPaymentsSnapshot.docs.length} student payment records`);
    console.log(`   • Deleted ${paymentsSnapshot.docs.length} payment transaction records`);
    console.log(`   • Deleted main student record`);
    
    return {
      success: true,
      deletedStudentPayments: studentPaymentsSnapshot.docs.length,
      deletedPaymentTransactions: paymentsSnapshot.docs.length
    };
    
  } catch (error) {
    console.error(`❌ Error cleaning up student records for ${studentId}:`, error);
    throw error;
  }
}
