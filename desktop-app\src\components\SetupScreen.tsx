import { useState, useEffect } from 'react'
import { getLogoPath } from '../assets'

interface SetupScreenProps {
  onComplete: () => void
}

export default function SetupScreen({ onComplete }: SetupScreenProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [isInstalling, setIsInstalling] = useState(false)
  const [isComplete, setIsComplete] = useState(false)

  const setupSteps = [
    {
      title: 'Welcome to Maggie Preparatory School',
      subtitle: 'Management System Installation',
      description: 'Thank you for choosing our comprehensive school management solution.',
      duration: 2000,
      tasks: ['Initializing installer', 'Checking system compatibility']
    },
    {
      title: 'Preparing Installation',
      subtitle: 'Setting up your environment',
      description: 'Configuring system requirements and dependencies...',
      duration: 1500,
      tasks: ['Creating application directories', 'Setting up permissions', 'Checking disk space']
    },
    {
      title: 'Installing Core Components',
      subtitle: 'Installing application files',
      description: 'Copying essential files and resources to your system...',
      duration: 2500,
      tasks: ['Installing Electron framework', 'Copying application assets', 'Setting up shortcuts']
    },
    {
      title: 'Setting Up Database',
      subtitle: 'Initializing data storage',
      description: 'Creating secure database connections and tables...',
      duration: 2000,
      tasks: ['Creating database schema', 'Setting up user tables', 'Configuring indexes']
    },
    {
      title: 'Configuring Security',
      subtitle: 'Implementing security measures',
      description: 'Setting up encryption and authentication protocols...',
      duration: 1800,
      tasks: ['Generating security keys', 'Setting up SSL certificates', 'Configuring firewall rules']
    },
    {
      title: 'Finalizing Setup',
      subtitle: 'Completing installation',
      description: 'Applying final configurations and optimizations...',
      duration: 1200,
      tasks: ['Registering application', 'Creating desktop shortcuts', 'Optimizing performance']
    }
  ]

  useEffect(() => {
    if (isInstalling) {
      const totalDuration = setupSteps.reduce((sum, step) => sum + step.duration, 0)
      let elapsed = 0

      const progressInterval = setInterval(() => {
        elapsed += 50
        const newProgress = Math.min((elapsed / totalDuration) * 100, 100)
        setProgress(newProgress)

        if (newProgress >= 100) {
          clearInterval(progressInterval)
          setIsComplete(true)
          setTimeout(() => {
            onComplete()
          }, 2000)
        }
      }, 50)

      // Step progression
      let stepElapsed = 0
      let stepIndex = 0

      const stepInterval = setInterval(() => {
        stepElapsed += 50
        
        if (stepIndex < setupSteps.length - 1 && stepElapsed >= setupSteps[stepIndex].duration) {
          stepIndex++
          setCurrentStep(stepIndex)
          stepElapsed = 0
        }

        if (stepIndex >= setupSteps.length - 1) {
          clearInterval(stepInterval)
        }
      }, 50)

      return () => {
        clearInterval(progressInterval)
        clearInterval(stepInterval)
      }
    }
  }, [isInstalling, onComplete])

  const startInstallation = () => {
    setIsInstalling(true)
  }

  if (!isInstalling) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        {/* Background Animation */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-ping"></div>
        </div>

        {/* Welcome Content */}
        <div className="relative z-10 text-center space-y-8 max-w-2xl mx-auto px-6">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="w-32 h-32 rounded-3xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-300">
                <img
                  src={getLogoPath()}
                  alt="Maggie Preparatory School Logo"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute inset-0 w-32 h-32 border-4 border-white/30 rounded-3xl animate-pulse"></div>
            </div>
          </div>

          {/* Welcome Text */}
          <div className="space-y-4">
            <h1 className="text-5xl font-bold text-white mb-4 animate-fade-in">
              Welcome to Setup
            </h1>
            <h2 className="text-2xl text-blue-200 animate-fade-in-delay">
              Maggie Preparatory School Management System
            </h2>
            <p className="text-lg text-white/80 max-w-xl mx-auto leading-relaxed animate-fade-in-delay">
              This setup wizard will guide you through the installation process. 
              The application will be configured with all necessary components for 
              managing students, teachers, and administrative tasks.
            </p>
          </div>

          {/* Features List */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            {[
              { icon: '👥', title: 'Student Management', desc: 'Comprehensive student records' },
              { icon: '📚', title: 'Academic Tracking', desc: 'Grades and performance monitoring' },
              { icon: '🔒', title: 'Secure Access', desc: 'Role-based authentication' }
            ].map((feature, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 animate-fade-in-delay">
                <div className="text-4xl mb-3">{feature.icon}</div>
                <h3 className="text-white font-semibold mb-2">{feature.title}</h3>
                <p className="text-white/70 text-sm">{feature.desc}</p>
              </div>
            ))}
          </div>

          {/* Install Button */}
          <div className="mt-12">
            <button
              type="button"
              onClick={startInstallation}
              className="px-12 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-2xl shadow-2xl hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 text-lg"
            >
              Begin Installation
            </button>
          </div>

          {/* Footer */}
          <div className="mt-16 text-white/50 text-sm">
            <p>© 2024 Maggie Preparatory School. All rights reserved.</p>
            <p className="mt-1">Version 1.0.0</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-ping"></div>
      </div>

      {/* Installation Content */}
      <div className="relative z-10 text-center space-y-8 max-w-2xl mx-auto px-6">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="w-24 h-24 rounded-3xl overflow-hidden shadow-2xl">
              <img
                src={getLogoPath()}
                alt="Maggie Preparatory School Logo"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute inset-0 w-24 h-24 border-4 border-white/30 rounded-3xl animate-spin"></div>
          </div>
        </div>

        {/* Current Step */}
        <div className="space-y-4">
          <h1 className="text-4xl font-bold text-white animate-fade-in">
            {setupSteps[currentStep].title}
          </h1>
          <h2 className="text-xl text-blue-200 animate-fade-in-delay">
            {setupSteps[currentStep].subtitle}
          </h2>
          <p className="text-white/80 max-w-lg mx-auto">
            {setupSteps[currentStep].description}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-4">
          <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full progress-bar-animated"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <p className="text-white/70 text-sm font-mono">
            {Math.round(progress)}% Complete
          </p>
        </div>

        {/* Step Indicators */}
        <div className="flex justify-center space-x-2 mt-8">
          {setupSteps.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index <= currentStep 
                  ? 'bg-gradient-to-r from-blue-400 to-purple-500 scale-110' 
                  : 'bg-white/30'
              }`}
            ></div>
          ))}
        </div>

        {/* Completion Message */}
        {isComplete && (
          <div className="mt-8 animate-fade-in">
            <div className="bg-green-500/20 border border-green-400/30 rounded-2xl p-6 backdrop-blur-xl">
              <div className="text-green-400 text-4xl mb-3">✓</div>
              <h3 className="text-white font-semibold text-xl mb-2">Installation Complete!</h3>
              <p className="text-white/80">
                Maggie Preparatory School Management System has been successfully installed.
                The application will launch shortly.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
