"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Student, User } from "@/lib/types";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { ClickableAvatar } from "../ui/clickable-avatar";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { User as UserIcon } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Textarea } from "../ui/textarea";

interface StudentDetailsDialogProps {
  children: React.ReactNode;
  student: Student;
}

export function StudentDetailsDialog({ children, student }: StudentDetailsDialogProps) {
  const [open, setOpen] = useState(false);
  const [parent, setParent] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchParent = async () => {
      if (open && student.parentId) {
        setLoading(true);
        try {
          const parentRef = doc(db, "users", student.parentId);
          const parentSnap = await getDoc(parentRef);
          if (parentSnap.exists()) {
            setParent({ id: parentSnap.id, ...parentSnap.data() } as User);
          }
        } catch (error) {
          console.error("Error fetching parent:", error);
          setParent(null);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchParent();
  }, [open, student.parentId]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <ClickableAvatar
              src={student.avatarUrl}
              alt={student.name}
              fallback={student.name.charAt(0)}
              size="lg"
              className="border"
            />
            <div>
              <DialogTitle className="font-headline text-2xl">{student.name}</DialogTitle>
              <DialogDescription>
                Student Information
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>
        <div className="max-h-[calc(90vh-200px)] overflow-y-auto pr-2">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Full Name
              </Label>
              <Input id="name" value={student.name} readOnly className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="dob" className="text-right">
                D.O.B
              </Label>
              <Input id="dob" value={student.dob} readOnly className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="class" className="text-right">
                Class
              </Label>
              <Input id="class" value={student.class} readOnly className="col-span-3" />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                  <UserIcon className="h-5 w-5"/>
                  Parent/Guardian Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading && <p>Loading parent details...</p>}
              {!loading && parent && (
                   <div className="grid gap-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="parent-name" className="text-right">
                          Parent Name
                          </Label>
                          <Input id="parent-name" value={parent.name} readOnly className="col-span-3" />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="parent-email" className="text-right">
                          Email
                          </Label>
                          <Input id="parent-email" value={parent.email} readOnly className="col-span-3" />
                      </div>
                      
                      {/* Father's Information */}
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="father-name" className="text-right">
                          Father's Name
                          </Label>
                          <Input id="father-name" value={parent.fatherName || 'N/A'} readOnly className="col-span-3" />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="father-phone" className="text-right">
                          Father's Phone
                          </Label>
                          <Input id="father-phone" value={parent.fatherPhone || 'N/A'} readOnly className="col-span-3" />
                      </div>
                      
                      {/* Mother's Information */}
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="mother-name" className="text-right">
                          Mother's Name
                          </Label>
                          <Input id="mother-name" value={parent.motherName || 'N/A'} readOnly className="col-span-3" />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="mother-phone" className="text-right">
                          Mother's Phone
                          </Label>
                          <Input id="mother-phone" value={parent.motherPhone || 'N/A'} readOnly className="col-span-3" />
                      </div>
                      
                      {/* General Contact Information */}
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="parent-phone" className="text-right">
                          General Phone
                          </Label>
                          <Input id="parent-phone" value={parent.phone || 'N/A'} readOnly className="col-span-3" />
                      </div>
                       <div className="grid grid-cols-4 items-start gap-4">
                          <Label htmlFor="parent-address" className="text-right pt-2">
                          Address
                          </Label>
                          <Textarea id="parent-address" value={parent.address || 'N/A'} readOnly className="col-span-3" />
                      </div>
                   </div>
              )}
               {!loading && !parent && (
                  <p className="text-sm text-muted-foreground">No parent information found.</p>
               )}
            </CardContent>
          </Card>

        </div>
      </DialogContent>
    </Dialog>
  );
}
