import React, { useEffect, useRef } from 'react';
import { useAuth } from '../hooks/use-auth';
import { useOfflineManager } from '../hooks/use-offline-manager';
import { useDynamicIsland } from '../hooks/use-dynamic-island';

export const NetworkStatusManager: React.FC = () => {
  const { isOnline, authMode } = useAuth();
  const { status, performSync } = useOfflineManager();
  const { showToast } = useDynamicIsland();
  
  const prevOnlineRef = useRef<boolean | null>(null);
  const prevStatusRef = useRef<string | null>(null);
  const syncInProgressRef = useRef(false);

  // Handle network status changes
  useEffect(() => {
    // Skip initial render
    if (prevOnlineRef.current === null) {
      prevOnlineRef.current = isOnline;
      return;
    }

    // Network status changed
    if (prevOnlineRef.current !== isOnline) {
      if (isOnline) {
        console.log('🌐 Network came online');
        showToast({
          title: '🌐 Back Online',
          description: 'Internet connection restored. Syncing data...',
          variant: 'success',
          duration: 3000
        });

        // Auto-sync when coming online
        if (!syncInProgressRef.current) {
          syncInProgressRef.current = true;
          performSync()
            .then(() => {
              showToast({
                title: '✅ Sync Complete',
                description: 'All data synchronized successfully',
                variant: 'success',
                duration: 2000
              });
            })
            .catch((error) => {
              console.error('Auto-sync failed:', error);
              showToast({
                title: '⚠️ Sync Failed',
                description: 'Failed to sync data. You can try again manually.',
                variant: 'error',
                duration: 4000
              });
            })
            .finally(() => {
              syncInProgressRef.current = false;
            });
        }
      } else {
        console.log('📱 Network went offline');
        showToast({
          title: '📱 Offline Mode',
          description: 'Working offline. Changes will sync when online.',
          variant: 'warning',
          duration: 4000
        });
      }
      
      prevOnlineRef.current = isOnline;
    }
  }, [isOnline, showToast, performSync]);

  // Handle offline manager status changes
  useEffect(() => {
    // Skip initial render
    if (prevStatusRef.current === null) {
      prevStatusRef.current = status;
      return;
    }

    // Status changed
    if (prevStatusRef.current !== status) {
      switch (status) {
        case 'syncing':
          if (!syncInProgressRef.current) {
            showToast({
              title: '🔄 Syncing...',
              description: 'Synchronizing data with server',
              variant: 'info',
              duration: 2000
            });
          }
          break;
        
        case 'error':
          showToast({
            title: '❌ Sync Error',
            description: 'Failed to sync data. Check your connection.',
            variant: 'error',
            duration: 4000
          });
          break;
        
        case 'ready':
          if (prevStatusRef.current === 'syncing') {
            showToast({
              title: '✅ Sync Complete',
              description: 'All data is up to date',
              variant: 'success',
              duration: 2000
            });
          }
          break;
      }
      
      prevStatusRef.current = status;
    }
  }, [status, showToast]);

  // Show auth mode status
  useEffect(() => {
    if (authMode === 'offline') {
      showToast({
        title: '📱 Offline Mode Active',
        description: 'Using cached data. Connect to internet to sync.',
        variant: 'info',
        duration: 3000
      });
    }
  }, [authMode, showToast]);

  return null; // This component doesn't render anything
};

export default NetworkStatusManager;
