import React, { useEffect, useRef } from 'react';
import { useAuth } from '../hooks/use-auth';
import { useOfflineManager } from '../hooks/use-offline-manager';
import { useNotifications } from './providers/notification-provider';

export const NetworkStatusManager: React.FC = () => {
  const { isOnline, authMode } = useAuth();
  const { status, performSync } = useOfflineManager();
  const { notify } = useNotifications();
  
  const prevOnlineRef = useRef<boolean | null>(null);
  const prevStatusRef = useRef<string | null>(null);
  const syncInProgressRef = useRef(false);

  // Handle network status changes
  useEffect(() => {
    // Skip initial render
    if (prevOnlineRef.current === null) {
      prevOnlineRef.current = isOnline;
      return;
    }

    // Network status changed
    if (prevOnlineRef.current !== isOnline) {
      if (isOnline) {
        console.log('🌐 Network came online');
        notify.success('🌐 Back Online', 'Internet connection restored. Syncing data...', 3000);

        // Auto-sync when coming online
        if (!syncInProgressRef.current) {
          syncInProgressRef.current = true;
          performSync()
            .then(() => {
              notify.success('✅ Sync Complete', 'All data synchronized successfully', 2000);
            })
            .catch((error) => {
              console.error('Auto-sync failed:', error);
              notify.error('⚠️ Sync Failed', 'Failed to sync data. You can try again manually.', 4000);
            })
            .finally(() => {
              syncInProgressRef.current = false;
            });
        }
      } else {
        console.log('📱 Network went offline');
        notify.warning('📱 Offline Mode', 'Working offline. Changes will sync when online.', 4000);
      }
      
      prevOnlineRef.current = isOnline;
    }
  }, [isOnline, notify, performSync]);

  // Handle offline manager status changes
  useEffect(() => {
    // Skip initial render
    if (prevStatusRef.current === null) {
      prevStatusRef.current = status;
      return;
    }

    // Status changed
    if (prevStatusRef.current !== status) {
      switch (status) {
        case 'syncing':
          if (!syncInProgressRef.current) {
            notify.info('🔄 Syncing...', 'Synchronizing data with server', 2000);
          }
          break;

        case 'error':
          notify.error('❌ Sync Error', 'Failed to sync data. Check your connection.', 4000);
          break;

        case 'ready':
          if (prevStatusRef.current === 'syncing') {
            notify.success('✅ Sync Complete', 'All data is up to date', 2000);
          }
          break;
      }
      
      prevStatusRef.current = status;
    }
  }, [status, notify]);

  // Show auth mode status
  useEffect(() => {
    if (authMode === 'offline') {
      notify.info('📱 Offline Mode Active', 'Using cached data. Connect to internet to sync.', 3000);
    }
  }, [authMode, notify]);

  return null; // This component doesn't render anything
};

export default NetworkStatusManager;
