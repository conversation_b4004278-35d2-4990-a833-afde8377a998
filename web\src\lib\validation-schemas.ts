import * as z from "zod";
import { Gender, EnrollmentStatus } from "./types";

// Define UserRole enum for validation
enum UserRole {
  admin = "admin",
  teacher = "teacher",
  parent = "parent"
}

// User validation schema
export const userFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Please enter a valid email address."),
  role: z.nativeEnum(UserRole).refine((val) => val !== undefined, { message: "Role is required." }),
  phone: z.string().optional(),
  address: z.string().optional(),
  assignedClasses: z.array(z.string()).optional(),
  password: z.string().min(6, "Password must be at least 6 characters.").optional(),
});

// Student registration validation schema
export const studentRegistrationSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  dob: z.string().refine((val) => !isNaN(Date.parse(val)), "Invalid date format."),
  gender: z.nativeEnum(Gender).refine((val) => val !== undefined, { message: "Gender is required." }),
  class: z.string().min(1, "Class is required."),
  parentId: z.string().min(1, "Parent is required."),
  enrollmentStatus: z.nativeEnum(EnrollmentStatus).refine((val) => val !== undefined, { message: "Enrollment status is required." }),
  enrollmentDate: z.string().refine((val) => !isNaN(Date.parse(val)), "Invalid enrollment date format."),
  academicYear: z.string().min(1, "Academic year is required."),
  previousSchool: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.object({
    name: z.string().optional(),
    relationship: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  medicalInfo: z.string().optional(),
  // Parent information for new registrations
  parentName: z.string().optional(),
  parentEmail: z.string().email().optional(),
  parentPhone: z.string().optional(),
  parentAddress: z.string().optional(),
  fatherName: z.string().optional(),
  motherName: z.string().optional(),
  fatherPhone: z.string().optional(),
  motherPhone: z.string().optional(),
});

// Login validation schema
export const loginFormSchema = z.object({
  email: z.string().email("Please enter a valid email address."),
  password: z.string().min(6, "Password must be at least 6 characters."),
});

// Admin setup validation schema
export const adminSetupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Please enter a valid email address."),
  password: z.string().min(6, "Password must be at least 6 characters."),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Academic year validation
export const academicYearSchema = z.string().refine((val) => {
  const yearPattern = /^\d{4}-\d{4}$/;
  if (!yearPattern.test(val)) return false;

  const [startYear, endYear] = val.split('-').map(Number);
  // Ensure it's a valid academic year format and not before 2025
  return endYear === startYear + 1 && startYear >= 2025;
}, "Academic year must be in format YYYY-YYYY (e.g., 2025-2026) and not before 2025-2026");

// Payment validation schema
export const paymentSchema = z.object({
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  paymentMethod: z.enum(['cash', 'bank_transfer', 'mobile_money', 'cheque', 'card']),
  description: z.string().min(1, "Description is required"),
});

export type UserFormData = z.infer<typeof userFormSchema>;
export type StudentRegistrationData = z.infer<typeof studentRegistrationSchema>;
export type LoginFormData = z.infer<typeof loginFormSchema>;
export type AdminSetupData = z.infer<typeof adminSetupSchema>;
export type PaymentData = z.infer<typeof paymentSchema>;
