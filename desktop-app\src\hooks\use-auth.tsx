import { useState, useEffect, useCallback, createContext, useContext, type ReactNode } from "react";
import type { User as FirebaseUser } from "firebase/auth";
import { onAuthStateChanged, signInWithEmailAndPassword, signOut } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { type User } from "../lib/types";
import { auth, db } from "../lib/firebase";
import { getOfflineAuthService } from "../lib/offline-auth";
import { getNetworkMonitor } from "../lib/network-monitor";

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  isLoading: boolean;
  isTransitioning: boolean;
  login: (email: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
  // Offline capabilities
  isOnline: boolean;
  authMode: 'online' | 'offline' | 'checking';
  canWorkOffline: boolean;
  offlineTimeRemaining: number;
  requiresOnlineAuth: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Offline state
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [authMode, setAuthMode] = useState<'online' | 'offline' | 'checking'>('checking');
  const [canWorkOffline, setCanWorkOffline] = useState(false);
  const [offlineTimeRemaining, setOfflineTimeRemaining] = useState(0);
  const [requiresOnlineAuth, setRequiresOnlineAuth] = useState(false);

  // Services
  const offlineAuth = getOfflineAuthService();
  const networkMonitor = getNetworkMonitor();

  const isAuthenticated = !isLoading && !!user && !!firebaseUser;

  const fetchUserData = useCallback(async (fbUser: FirebaseUser | null) => {
    console.log('🔄 fetchUserData called with user:', fbUser?.email || 'null');

    if (fbUser && fbUser.email) {
      // First, set loading to false immediately for fast splash completion
      setIsLoading(false);
      setFirebaseUser(fbUser);

      // Don't set user immediately - wait for Firestore data
      console.log('⚡ Loading user data from Firestore...');

      // Fetch full user data from Firestore
      try {
        const userDocRef = doc(db, "users", fbUser.email);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('📄 Raw Firestore user data:', userData);

          const fullUserData = { id: userDoc.id, ...userData } as User;

          // Validate that the user has a proper role
          if (!fullUserData.role || !['admin', 'teacher', 'parent'].includes(fullUserData.role)) {
            console.error('⚠️ Invalid or missing role in user data:', fullUserData.role);
            fullUserData.role = 'admin'; // Default to admin for safety
          }

          setUser(fullUserData);
          console.log('✅ Full user data loaded:', fullUserData.email, 'Role:', fullUserData.role);

          // Cache session for offline use
          if (fbUser) {
            try {
              await offlineAuth.cacheUserSession(fullUserData, fbUser);
              await offlineAuth.updateLastOnlineValidation();
              console.log('✅ User session cached for offline use');
            } catch (error) {
              console.warn('⚠️ Failed to cache user session:', error);
            }
          }
        } else {
          console.error(`User profile not found in Firestore for ${fbUser.email}`);
          // Create a basic user if no Firestore document exists
          const basicUser: User = {
            id: fbUser.email,
            email: fbUser.email,
            name: fbUser.displayName || fbUser.email.split('@')[0],
            role: 'admin' // Fallback role - should be updated by admin
          };
          setUser(basicUser);
          console.log('⚠️ Using basic user data as fallback - role needs to be set by admin');
        }
      } catch (error) {
        console.error("Error fetching full user data:", error);
        // Create a basic user without role if Firestore fails
        const basicUser: User = {
          id: fbUser.email,
          email: fbUser.email,
          name: fbUser.displayName || fbUser.email.split('@')[0],
          role: 'admin' // Fallback role - should be updated by admin
        };
        setUser(basicUser);
        console.log('⚠️ Using basic user data due to Firestore error - role needs to be set by admin');
      }
    } else {
      console.log('🔄 No Firebase user, clearing state');
      setUser(null);
      setFirebaseUser(null);
      setIsLoading(false);
    }
    console.log('✅ fetchUserData completed');
  }, []);

  const refreshUser = useCallback(async () => {
    const fbUser = auth.currentUser;
    if (fbUser) {
        await fetchUserData(fbUser);
    }
  }, [fetchUserData]);

  // Update offline status periodically
  useEffect(() => {
    const updateOfflineStatus = async () => {
      try {
        const canWork = await offlineAuth.canWorkOffline();
        const timeRemaining = await offlineAuth.getOfflineTimeRemaining();
        const requiresAuth = await offlineAuth.requiresOnlineAuth();

        setCanWorkOffline(canWork);
        setOfflineTimeRemaining(timeRemaining);
        setRequiresOnlineAuth(requiresAuth);
      } catch (error) {
        console.error('Failed to update offline status:', error);
      }
    };

    updateOfflineStatus();
    const interval = setInterval(updateOfflineStatus, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [offlineAuth]);

  // Network status monitoring
  useEffect(() => {
    const updateNetworkStatus = (status: 'online' | 'offline') => {
      setIsOnline(status === 'online');
    };

    const unsubscribeOnline = networkMonitor.onOnline(() => updateNetworkStatus('online'));
    const unsubscribeOffline = networkMonitor.onOffline(() => updateNetworkStatus('offline'));

    // Set initial status
    setIsOnline(networkMonitor.isOnline());

    return () => {
      unsubscribeOnline();
      unsubscribeOffline();
    };
  }, [networkMonitor]);

  useEffect(() => {
    console.log('🔄 Setting up auth state listener');
    setIsLoading(true);

    const unsubscribe = onAuthStateChanged(auth, async (fbUser) => {
      console.log('🔄 Auth state changed:', fbUser?.email || 'null');
      setIsTransitioning(true);

      try {
        if (isOnline) {
          // Online mode: Use Firebase Auth
          await fetchUserData(fbUser);
          setAuthMode('online');
        } else {
          // Offline mode: Try cached session first
          const cachedUser = await offlineAuth.authenticateOffline();
          if (cachedUser) {
            setUser(cachedUser);
            setFirebaseUser(null); // No Firebase user in offline mode
            setAuthMode('offline');
          } else {
            // No cached session, but we have a Firebase user - try to fetch data
            if (fbUser) {
              await fetchUserData(fbUser);
              setAuthMode('online'); // Treat as online if we can fetch data
            } else {
              setUser(null);
              setFirebaseUser(null);
              setAuthMode('checking');
            }
          }
        }
      } catch (error) {
        console.error("Error in auth state change:", error);

        // Try offline authentication as fallback
        if (!isOnline) {
          try {
            const cachedUser = await offlineAuth.authenticateOffline();
            if (cachedUser) {
              setUser(cachedUser);
              setFirebaseUser(null);
              setAuthMode('offline');
            } else {
              setUser(null);
              setFirebaseUser(null);
            }
          } catch (offlineError) {
            console.error("Offline authentication also failed:", offlineError);
            setUser(null);
            setFirebaseUser(null);
          }
        } else {
          setUser(null);
          setFirebaseUser(null);
        }
      } finally {
        setIsTransitioning(false);
      }
    });

    return () => {
      console.log('🔄 Cleaning up auth state listener');
      unsubscribe();
    };
  }, [fetchUserData, isOnline, offlineAuth]);
  
  // Remove automatic navigation - let the routing handle it

  const login = useCallback(
    async (email: string, password?: string) => {
      if (!password) {
        throw new Error("Password is required.");
      }
      try {
        await signInWithEmailAndPassword(auth, email, password);
        // The onAuthStateChanged listener will handle fetching user data and setting state.
      } catch (error: any) {
        if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential' || error.code === 'auth/invalid-email') {
            throw new Error("Invalid email or password.");
        }
        console.error("Login Error:", error);
        throw new Error("An unexpected error occurred during login.");
      }
    },
    []
  );

  const logout = useCallback(async () => {
    console.log('🔄 Logout initiated');
    try {
      setIsTransitioning(true);
      console.log('🔄 Transition state set to true');

      // Add a small delay for smooth transition
      await new Promise(resolve => setTimeout(resolve, 500));

      // Sign out from Firebase
      console.log('🔄 Signing out from Firebase...');
      await signOut(auth);
      console.log('✅ Firebase sign out successful');

      // Clear local state immediately after Firebase signout
      setUser(null);
      setFirebaseUser(null);
      console.log('✅ Local state cleared');

      // Small delay before navigation
      await new Promise(resolve => setTimeout(resolve, 200));

      // Navigate to login using hash
      console.log('🔄 Navigating to login...');
      window.location.hash = '#/login';
      console.log('✅ Navigation completed');

    } catch (error) {
        console.error("❌ Error during logout:", error);
        // On error, still clear state and navigate
        setUser(null);
        setFirebaseUser(null);
        window.location.hash = '#/login';
    } finally {
      // Add a small delay before clearing transition state
      setTimeout(() => {
        setIsTransitioning(false);
        console.log('✅ Logout process completed');
      }, 100);
    }
  }, []);
  

  const value = {
    user,
    firebaseUser,
    isLoading,
    isTransitioning,
    login,
    logout,
    isAuthenticated,
    refreshUser,
    // Offline capabilities
    isOnline,
    authMode,
    canWorkOffline,
    offlineTimeRemaining,
    requiresOnlineAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
