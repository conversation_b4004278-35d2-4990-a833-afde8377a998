import { useState, useEffect, useCallback, createContext, useContext, type ReactNode } from "react";
import type { User as FirebaseUser } from "firebase/auth";
import { onAuthStateChanged, signInWithEmailAndPassword, signOut } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { type User } from "../lib/types";
import { auth, db } from "../lib/firebase";
import { getOfflineAuthService } from "../lib/offline-auth";
import { getNetworkMonitor } from "../lib/network-monitor";

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  isLoading: boolean;
  isTransitioning: boolean;
  login: (email: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
  // Offline capabilities
  isOnline: boolean;
  authMode: 'online' | 'offline' | 'checking';
  canWorkOffline: boolean;
  offlineTimeRemaining: number;
  requiresOnlineAuth: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Offline state - Start with offline assumption for faster startup
  const [isOnline, setIsOnline] = useState(false);
  const [authMode, setAuthMode] = useState<'online' | 'offline' | 'checking'>('checking');
  const [canWorkOffline, setCanWorkOffline] = useState(false);
  const [offlineTimeRemaining, setOfflineTimeRemaining] = useState(0);
  const [requiresOnlineAuth, setRequiresOnlineAuth] = useState(false);

  // Services
  const offlineAuth = getOfflineAuthService();
  const networkMonitor = getNetworkMonitor();

  // Store password temporarily during login for caching
  const [tempPassword, setTempPassword] = useState<string | null>(null);

  const isAuthenticated = !isLoading && !!user && !!firebaseUser;

  const fetchUserData = useCallback(async (fbUser: FirebaseUser | null) => {
    console.log('🔄 fetchUserData called with user:', fbUser?.email || 'null');

    if (fbUser && fbUser.email) {
      // First, set loading to false immediately for fast splash completion
      setIsLoading(false);
      setFirebaseUser(fbUser);

      // Don't set user immediately - wait for Firestore data
      console.log('⚡ Loading user data from Firestore...');

      // Fetch full user data from Firestore
      try {
        const userDocRef = doc(db, "users", fbUser.email);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('📄 Raw Firestore user data:', userData);

          const fullUserData = { id: userDoc.id, ...userData } as User;

          // Validate that the user has a proper role
          if (!fullUserData.role || !['admin', 'teacher', 'parent'].includes(fullUserData.role)) {
            console.error('⚠️ Invalid or missing role in user data:', fullUserData.role);
            fullUserData.role = 'admin'; // Default to admin for safety
          }

          setUser(fullUserData);
          console.log('✅ Full user data loaded:', fullUserData.email, 'Role:', fullUserData.role);

          // Cache session for offline use
          if (fbUser) {
            try {
              await offlineAuth.cacheUserSession(fullUserData, fbUser, tempPassword || undefined);
              await offlineAuth.updateLastOnlineValidation();
              console.log('✅ User session cached for offline use');
              // Clear temporary password
              setTempPassword(null);
            } catch (error) {
              console.warn('⚠️ Failed to cache user session:', error);
            }
          }
        } else {
          console.error(`User profile not found in Firestore for ${fbUser.email}`);
          // Create a basic user if no Firestore document exists
          const basicUser: User = {
            id: fbUser.email,
            email: fbUser.email,
            name: fbUser.displayName || fbUser.email.split('@')[0],
            role: 'admin' // Fallback role - should be updated by admin
          };
          setUser(basicUser);
          console.log('⚠️ Using basic user data as fallback - role needs to be set by admin');
        }
      } catch (error) {
        console.error("Error fetching full user data:", error);
        // Create a basic user without role if Firestore fails
        const basicUser: User = {
          id: fbUser.email,
          email: fbUser.email,
          name: fbUser.displayName || fbUser.email.split('@')[0],
          role: 'admin' // Fallback role - should be updated by admin
        };
        setUser(basicUser);
        console.log('⚠️ Using basic user data due to Firestore error - role needs to be set by admin');
      }
    } else {
      console.log('🔄 No Firebase user, clearing state');
      setUser(null);
      setFirebaseUser(null);
      setIsLoading(false);
    }
    console.log('✅ fetchUserData completed');
  }, []);

  const refreshUser = useCallback(async () => {
    const fbUser = auth.currentUser;
    if (fbUser) {
        await fetchUserData(fbUser);
    }
  }, [fetchUserData]);

  // Update offline status periodically
  useEffect(() => {
    const updateOfflineStatus = async () => {
      try {
        const canWork = await offlineAuth.canWorkOffline();
        const timeRemaining = await offlineAuth.getOfflineTimeRemaining();
        const requiresAuth = await offlineAuth.requiresOnlineAuth();

        setCanWorkOffline(canWork);
        setOfflineTimeRemaining(timeRemaining);
        setRequiresOnlineAuth(requiresAuth);
      } catch (error) {
        console.error('Failed to update offline status:', error);
      }
    };

    updateOfflineStatus();
    const interval = setInterval(updateOfflineStatus, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [offlineAuth]);

  // Network status monitoring - Initialize immediately
  useEffect(() => {
    const updateNetworkStatus = (status: 'online' | 'offline') => {
      console.log(`🌐 Network status changed: ${status}`);
      setIsOnline(status === 'online');
    };

    // Set initial status immediately
    const initialStatus = networkMonitor.isOnline();
    console.log(`🌐 Initial network status: ${initialStatus ? 'online' : 'offline'}`);
    setIsOnline(initialStatus);

    const unsubscribeOnline = networkMonitor.onOnline(() => updateNetworkStatus('online'));
    const unsubscribeOffline = networkMonitor.onOffline(() => updateNetworkStatus('offline'));

    return () => {
      unsubscribeOnline();
      unsubscribeOffline();
    };
  }, [networkMonitor]);

  useEffect(() => {
    console.log('🔄 Setting up auth initialization');
    setIsLoading(true);

    // Add overall timeout to prevent infinite loading
    const overallTimeout = setTimeout(() => {
      console.warn('⚠️ Auth initialization timeout - proceeding with no auth');
      setIsLoading(false);
      setIsTransitioning(false);
      setAuthMode('checking');
    }, 15000); // 15 second overall timeout

    const initializeAuth = async () => {
      console.log('🔄 Initializing authentication...');
      setIsTransitioning(true);

      try {
        // ALWAYS try offline authentication first to avoid hanging on Firebase
        console.log('🔄 Checking for cached offline session...');
        const cachedUser = await offlineAuth.authenticateOffline();

        if (cachedUser) {
          console.log('✅ Found valid offline session for:', cachedUser.email);
          clearTimeout(overallTimeout); // Clear timeout on success
          setUser(cachedUser);
          setFirebaseUser(null); // No Firebase user in offline mode
          setAuthMode('offline');
          setIsLoading(false);
          setIsTransitioning(false);
          return; // Exit early - we have offline auth
        }

        console.log('⚠️ No valid offline session found');

        // Only try Firebase Auth if we're online AND no offline session exists
        if (isOnline) {
          console.log('🔄 Trying Firebase authentication...');

          // Add timeout to prevent hanging
          const authTimeout = setTimeout(() => {
            console.warn('⚠️ Firebase auth timeout - falling back to offline mode');
            setUser(null);
            setFirebaseUser(null);
            setAuthMode('checking');
            setIsLoading(false);
            setIsTransitioning(false);
          }, 10000); // 10 second timeout

          // Set up Firebase auth listener
          const unsubscribe = onAuthStateChanged(auth, async (fbUser) => {
            clearTimeout(authTimeout); // Clear timeout on successful auth
            console.log('🔄 Firebase auth state changed:', fbUser?.email || 'null');

            try {
              if (fbUser) {
                await fetchUserData(fbUser);
                setAuthMode('online');
              } else {
                setUser(null);
                setFirebaseUser(null);
                setAuthMode('checking');
              }
            } catch (error) {
              console.error("Error fetching Firebase user data:", error);
              setUser(null);
              setFirebaseUser(null);
              setAuthMode('checking');
            } finally {
              setIsLoading(false);
              setIsTransitioning(false);
            }
          });

          // Store unsubscribe function for cleanup
          return () => {
            clearTimeout(authTimeout);
            unsubscribe();
          };
        } else {
          console.log('⚠️ Offline and no cached session - user needs to login online first');
          clearTimeout(overallTimeout);
          setUser(null);
          setFirebaseUser(null);
          setAuthMode('checking');
          setIsLoading(false);
          setIsTransitioning(false);
        }

      } catch (error) {
        console.error("Error in auth initialization:", error);
        clearTimeout(overallTimeout);
        setUser(null);
        setFirebaseUser(null);
        setAuthMode('checking');
        setIsLoading(false);
        setIsTransitioning(false);
      }
    };

    // Initialize auth and store cleanup function
    let cleanup: (() => void) | undefined;
    initializeAuth().then((unsubscribe) => {
      cleanup = unsubscribe;
    });

    return () => {
      console.log('🔄 Cleaning up auth initialization');
      clearTimeout(overallTimeout);
      if (cleanup) {
        cleanup();
      }
    };
  }, [fetchUserData, isOnline, offlineAuth]);
  
  // Remove automatic navigation - let the routing handle it

  const login = useCallback(
    async (email: string, password?: string) => {
      if (!password) {
        throw new Error("Password is required.");
      }

      console.log(`🔄 Login attempt for: ${email} (Mode: ${authMode}, Online: ${isOnline})`);

      try {
        // First, try offline authentication with password validation
        try {
          const cachedUser = await offlineAuth.authenticateWithPassword(email, password);
          if (cachedUser) {
            console.log('✅ Using cached offline session for login with password validation');
            setUser(cachedUser);
            setFirebaseUser(null);
            setAuthMode('offline');
            return; // Success - using offline auth
          }
        } catch (offlineError: any) {
          // If offline auth fails with password error, show it immediately
          if (offlineError.message === 'Invalid password') {
            throw new Error('Invalid email or password.');
          }
          // Otherwise, continue to try online auth
          console.log('⚠️ Offline auth failed, trying online:', offlineError.message);
        }

        // If no offline session or different user, try online authentication
        if (!isOnline) {
          throw new Error("No internet connection and no valid offline session found. Please connect to the internet to sign in.");
        }

        console.log('🔄 Attempting Firebase authentication...');
        // Store password temporarily for caching
        setTempPassword(password);
        await signInWithEmailAndPassword(auth, email, password);
        // The onAuthStateChanged listener will handle fetching user data and setting state.

      } catch (error: any) {
        console.error("Login Error:", error);

        // Clear temporary password on error
        setTempPassword(null);

        // Handle Firebase auth errors
        if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential' || error.code === 'auth/invalid-email') {
          throw new Error("Invalid email or password.");
        } else if (error.code === 'auth/network-request-failed') {
          throw new Error("Network error. Please check your internet connection and try again.");
        } else if (error.code === 'auth/too-many-requests') {
          throw new Error("Too many failed login attempts. Please try again later.");
        } else if (error.message.includes("No internet connection")) {
          // Re-throw our custom offline message
          throw error;
        }

        throw new Error("An unexpected error occurred during login. Please try again.");
      }
    },
    [isOnline, authMode, offlineAuth]
  );

  const logout = useCallback(async () => {
    console.log('🔄 Logout initiated');
    try {
      setIsTransitioning(true);
      console.log('🔄 Transition state set to true');

      // Add a small delay for smooth transition
      await new Promise(resolve => setTimeout(resolve, 500));

      // Sign out from Firebase
      console.log('🔄 Signing out from Firebase...');
      await signOut(auth);
      console.log('✅ Firebase sign out successful');

      // Clear local state immediately after Firebase signout
      setUser(null);
      setFirebaseUser(null);
      console.log('✅ Local state cleared');

      // Small delay before navigation
      await new Promise(resolve => setTimeout(resolve, 200));

      // Navigate to login using hash
      console.log('🔄 Navigating to login...');
      window.location.hash = '#/login';
      console.log('✅ Navigation completed');

    } catch (error) {
        console.error("❌ Error during logout:", error);
        // On error, still clear state and navigate
        setUser(null);
        setFirebaseUser(null);
        window.location.hash = '#/login';
    } finally {
      // Add a small delay before clearing transition state
      setTimeout(() => {
        setIsTransitioning(false);
        console.log('✅ Logout process completed');
      }, 100);
    }
  }, []);
  

  const value = {
    user,
    firebaseUser,
    isLoading,
    isTransitioning,
    login,
    logout,
    isAuthenticated,
    refreshUser,
    // Offline capabilities
    isOnline,
    authMode,
    canWorkOffline,
    offlineTimeRemaining,
    requiresOnlineAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
