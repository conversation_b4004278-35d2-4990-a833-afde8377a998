import { MongoClient, Db, Collection, ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import { 
  collection as firebaseCollection, 
  doc as firebaseDoc,
  addDoc as firebaseAddDoc,
  setDoc as firebaseSetDoc,
  updateDoc as firebaseUpdateDoc,
  deleteDoc as firebaseDeleteDoc,
  getDocs as firebaseGetDocs,
  onSnapshot as firebaseOnSnapshot,
  query as firebaseQuery,
  where as firebaseWhere,
  orderBy as firebaseOrderBy,
  Unsubscribe
} from 'firebase/firestore';
import { db as firebaseDb } from './firebase';

// Types
export interface DatabaseConfig {
  mongoUrl: string;
  mongoDatabase: string;
  syncEnabled: boolean;
}

export interface SyncMetadata {
  lastModified: Date;
  source: 'firebase' | 'mongodb';
  syncStatus: 'pending' | 'synced' | 'conflict';
  firebaseId?: string;
  mongoId?: string;
}

export interface DatabaseDocument {
  _id?: ObjectId | string;
  id?: string;
  syncMeta?: SyncMetadata;
  [key: string]: any;
}

export type ConnectionStatus = 'online' | 'offline' | 'syncing' | 'error';

// Database Service Class
export class DatabaseService {
  private mongoClient: MongoClient | null = null;
  private mongoDb: Db | null = null;
  private isConnected: boolean = false;
  private connectionStatus: ConnectionStatus = 'offline';
  private config: DatabaseConfig;
  private subscriptions: Map<string, () => void> = new Map();
  private syncQueue: Array<{ collection: string; operation: string; data: any; timestamp: Date }> = [];

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  // Connection Management
  async connect(): Promise<void> {
    try {
      console.log('🔄 Connecting to MongoDB...');
      
      // Connect to MongoDB
      this.mongoClient = new MongoClient(this.config.mongoUrl, {
        serverSelectionTimeoutMS: 5000,
        connectTimeoutMS: 10000,
        maxPoolSize: 10,
      });

      await this.mongoClient.connect();
      this.mongoDb = this.mongoClient.db(this.config.mongoDatabase);
      
      // Test connection
      await this.mongoDb.admin().ping();
      
      this.isConnected = true;
      this.connectionStatus = 'offline'; // Start offline, will check network later
      
      console.log('✅ Connected to MongoDB:', this.config.mongoDatabase);
      
      // Initialize collections if they don't exist
      await this.initializeCollections();
      
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      this.connectionStatus = 'error';
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  async disconnect(): Promise<void> {
    if (this.mongoClient) {
      // Clear all subscriptions
      this.subscriptions.forEach(unsubscribe => unsubscribe());
      this.subscriptions.clear();
      
      await this.mongoClient.close();
      this.mongoClient = null;
      this.mongoDb = null;
      this.isConnected = false;
      this.connectionStatus = 'offline';
      
      console.log('🔌 Disconnected from MongoDB');
    }
  }

  private async initializeCollections(): Promise<void> {
    if (!this.mongoDb) return;

    const collections = ['users', 'students', 'results', 'feeStructures', 'studentPayments', 'subjects'];
    
    for (const collectionName of collections) {
      try {
        // Create collection if it doesn't exist
        const existingCollections = await this.mongoDb.listCollections({ name: collectionName }).toArray();
        
        if (existingCollections.length === 0) {
          await this.mongoDb.createCollection(collectionName);
          console.log(`📁 Created collection: ${collectionName}`);
        }
        
        // Create indexes for better performance
        const collection = this.mongoDb.collection(collectionName);
        await collection.createIndex({ 'syncMeta.lastModified': 1 });
        await collection.createIndex({ 'syncMeta.firebaseId': 1 });
        
      } catch (error) {
        console.warn(`⚠️ Failed to initialize collection ${collectionName}:`, error);
      }
    }
  }

  // Connection Status
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
  }

  isOnline(): boolean {
    return this.connectionStatus === 'online' || this.connectionStatus === 'syncing';
  }

  // CRUD Operations
  async create(collectionName: string, data: any): Promise<string> {
    if (!this.mongoDb) throw new Error('Database not connected');

    try {
      // Add sync metadata
      const document: DatabaseDocument = {
        ...data,
        syncMeta: {
          lastModified: new Date(),
          source: 'mongodb',
          syncStatus: this.isOnline() ? 'pending' : 'pending',
          mongoId: undefined // Will be set after insert
        }
      };

      // Insert into MongoDB
      const collection = this.mongoDb.collection(collectionName);
      const result = await collection.insertOne(document);
      
      // Update sync metadata with MongoDB ID
      await collection.updateOne(
        { _id: result.insertedId },
        { $set: { 'syncMeta.mongoId': result.insertedId.toString() } }
      );

      const documentId = result.insertedId.toString();

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'create', { ...document, _id: result.insertedId });
      }

      console.log(`✅ Created document in ${collectionName}:`, documentId);
      return documentId;

    } catch (error) {
      console.error(`❌ Failed to create document in ${collectionName}:`, error);
      throw error;
    }
  }

  async read(collectionName: string, query: any = {}): Promise<any[]> {
    if (!this.mongoDb) throw new Error('Database not connected');

    try {
      const collection = this.mongoDb.collection(collectionName);
      const documents = await collection.find(query).toArray();
      
      // Convert MongoDB documents to app format
      return documents.map(doc => ({
        id: doc._id.toString(),
        ...doc,
        _id: undefined // Remove MongoDB _id from app data
      }));

    } catch (error) {
      console.error(`❌ Failed to read from ${collectionName}:`, error);
      throw error;
    }
  }

  async update(collectionName: string, id: string, data: any): Promise<void> {
    if (!this.mongoDb) throw new Error('Database not connected');

    try {
      const collection = this.mongoDb.collection(collectionName);
      
      // Update sync metadata
      const updateData = {
        ...data,
        'syncMeta.lastModified': new Date(),
        'syncMeta.source': 'mongodb',
        'syncMeta.syncStatus': this.isOnline() ? 'pending' : 'pending'
      };

      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        throw new Error(`Document with id ${id} not found`);
      }

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'update', { id, ...updateData });
      }

      console.log(`✅ Updated document in ${collectionName}:`, id);

    } catch (error) {
      console.error(`❌ Failed to update document in ${collectionName}:`, error);
      throw error;
    }
  }

  async delete(collectionName: string, id: string): Promise<void> {
    if (!this.mongoDb) throw new Error('Database not connected');

    try {
      const collection = this.mongoDb.collection(collectionName);
      
      // Get document before deletion for sync queue
      const document = await collection.findOne({ _id: new ObjectId(id) });
      
      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      if (result.deletedCount === 0) {
        throw new Error(`Document with id ${id} not found`);
      }

      // Add to sync queue if online
      if (this.isOnline() && document) {
        this.addToSyncQueue(collectionName, 'delete', { id, firebaseId: document.syncMeta?.firebaseId });
      }

      console.log(`✅ Deleted document from ${collectionName}:`, id);

    } catch (error) {
      console.error(`❌ Failed to delete document from ${collectionName}:`, error);
      throw error;
    }
  }

  // Real-time Subscriptions
  subscribe(collectionName: string, callback: (data: any[]) => void): () => void {
    if (!this.mongoDb) throw new Error('Database not connected');

    const subscriptionKey = `${collectionName}_${Date.now()}`;
    
    // For now, we'll use polling for MongoDB changes
    // In production, you might want to use MongoDB Change Streams
    const pollInterval = setInterval(async () => {
      try {
        const data = await this.read(collectionName);
        callback(data);
      } catch (error) {
        console.error(`❌ Subscription error for ${collectionName}:`, error);
      }
    }, 1000); // Poll every second

    const unsubscribe = () => {
      clearInterval(pollInterval);
      this.subscriptions.delete(subscriptionKey);
    };

    this.subscriptions.set(subscriptionKey, unsubscribe);
    
    // Initial data load
    this.read(collectionName).then(callback).catch(console.error);

    return unsubscribe;
  }

  // Sync Queue Management
  private addToSyncQueue(collection: string, operation: string, data: any): void {
    this.syncQueue.push({
      collection,
      operation,
      data,
      timestamp: new Date()
    });
  }

  getSyncQueue(): Array<{ collection: string; operation: string; data: any; timestamp: Date }> {
    return [...this.syncQueue];
  }

  clearSyncQueue(): void {
    this.syncQueue = [];
  }
}

// Default configuration
export const defaultDatabaseConfig: DatabaseConfig = {
  mongoUrl: 'mongodb://localhost:27017',
  mongoDatabase: 'maggie_school_offline',
  syncEnabled: true
};

// Singleton instance
let databaseServiceInstance: DatabaseService | null = null;

export function getDatabaseService(config?: DatabaseConfig): DatabaseService {
  if (!databaseServiceInstance) {
    databaseServiceInstance = new DatabaseService(config || defaultDatabaseConfig);
  }
  return databaseServiceInstance;
}
