import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import { Modal } from '../components/ui/modal'
import {
  StudentResult,
  ClassLevel,
  Term,
  ResultSummary
} from '../lib/results-types'
import {
  getGradeColor,
  formatResultSummary,
  calculateOverallGrade,
  sortResults
} from '../lib/results-utils'
import { getCurrentAcademicYear, getAvailableAcademicYears } from '../lib/business-logic'
import { Student } from '../lib/types'
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  onSnapshot
} from 'firebase/firestore'
import { db } from '../lib/firebase'

export default function ViewResults() {
  const { user } = useAuth()

  // Restrict access for parent users - redirect them to My Children page
  if (user?.role === 'parent') {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">View Results in My Children</h3>
          <p className="text-gray-600 mb-6">
            Results viewing has been moved to the My Children page for a better experience with filtering and detailed views.
          </p>
          <button
            type="button"
            onClick={() => window.location.href = '/my-children'}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to My Children
          </button>
        </div>
      </div>
    );
  }

  const [students, setStudents] = useState<Student[]>([])
  const [results, setResults] = useState<StudentResult[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedClass, setSelectedClass] = useState<string>('all')
  const [selectedTerm, setSelectedTerm] = useState<Term | 'all'>('all')
  const [selectedYear, setSelectedYear] = useState(getCurrentAcademicYear())
  const [searchTerm, setSearchTerm] = useState('')
  const [showResultsModal, setShowResultsModal] = useState(false)
  const [modalStudent, setModalStudent] = useState<Student | null>(null)

  // Load data based on user role
  useEffect(() => {
    const loadData = async () => {
      if (!user) return

      try {
        let studentsData: Student[] = []
        
        if (user.role === 'parent') {
          // Parents can only see their children
          if (user.childrenIds && user.childrenIds.length > 0) {
            const studentsPromises = user.childrenIds.map(async (childId) => {
              const studentQuery = query(
                collection(db, 'students'),
                where('__name__', '==', childId)
              )
              const snapshot = await getDocs(studentQuery)
              return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })) as Student[]
            })
            
            const studentsArrays = await Promise.all(studentsPromises)
            studentsData = studentsArrays.flat()
          }
        } else if (user.role === 'teacher') {
          // Teachers can see students from their assigned classes
          if (user.assignedClasses && user.assignedClasses.length > 0) {
            const studentQuery = query(
              collection(db, 'students'),
              where('class', 'in', user.assignedClasses),
              orderBy('name')
            )
            const snapshot = await getDocs(studentQuery)
            studentsData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as Student[]
          }
        } else if (user.role === 'admin') {
          // Admins can see all students
          const studentQuery = query(collection(db, 'students'), orderBy('name'))
          const snapshot = await getDocs(studentQuery)
          studentsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Student[]
        }

        setStudents(studentsData)

        // Load results for accessible students
        if (studentsData.length > 0) {
          const studentIds = studentsData.map(s => s.id)
          const resultsQuery = query(
            collection(db, 'results'),
            where('studentId', 'in', studentIds.slice(0, 10)), // Firestore 'in' limit
            orderBy('createdAt', 'desc')
          )
          
          const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
            try {
              const resultsData = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })) as StudentResult[]
              setResults(Array.isArray(resultsData) ? resultsData : [])
            } catch (error) {
              console.error('Error processing results data:', error)
              setResults([])
            }
          })

          setLoading(false)
          return () => unsubscribeResults()
        } else {
          setLoading(false)
        }
      } catch (error) {
        console.error('Error loading data:', error)
        setLoading(false)
      }
    }

    loadData()
  }, [user])

  const getFilteredResults = () => {
    // Ensure results is always an array
    if (!Array.isArray(results)) {
      console.warn('Results is not an array in getFilteredResults:', results)
      return []
    }

    let filtered = results

    if (selectedTerm !== 'all') {
      filtered = filtered.filter(r => r.term === selectedTerm)
    }

    if (selectedYear) {
      filtered = filtered.filter(r => r.academicYear === selectedYear)
    }

    if (searchTerm) {
      filtered = filtered.filter(r =>
        r.studentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.subjectName?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return sortResults(filtered, 'name', 'asc')
  }

  const generateResultSummary = (studentId: string, term: Term, year: string): ResultSummary | null => {
    // Ensure results is always an array
    if (!Array.isArray(results)) {
      console.warn('Results is not an array in generateResultSummary:', results)
      return null
    }

    const studentResults = results.filter(r =>
      r.studentId === studentId &&
      r.term === term &&
      r.academicYear === year
    )

    if (studentResults.length === 0) return null

    const student = Array.isArray(students) ? students.find(s => s.id === studentId) : null
    if (!student) return null

    try {
      const summary = formatResultSummary(studentResults)
      const overallGrade = calculateOverallGrade(studentResults)

      return {
        studentId,
        studentName: student.name,
        classLevel: student.class as ClassLevel,
        term,
        academicYear: year,
        totalSubjects: summary.totalSubjects,
        totalMarks: summary.totalMarks,
        totalObtained: summary.totalObtained,
        averagePercentage: summary.averagePercentage,
        overallGrade,
        isPassed: summary.averagePercentage >= 40, // Assuming 40% pass mark
        results: studentResults,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error generating result summary:', error)
      return null
    }
  }

  const filteredResults = getFilteredResults()
  const groupedResults = filteredResults.reduce((acc, result) => {
    const key = `${result.studentId}-${result.term}-${result.academicYear}`
    if (!acc[key]) {
      acc[key] = []
    }
    acc[key].push(result)
    return acc
  }, {} as Record<string, StudentResult[]>)

  // Filter students based on search and selected filters
  const filteredStudents = Array.isArray(students) ? students.filter(student => {
    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSelectedClass = selectedClass === 'all' || student.class === selectedClass
    return matchesSearch && matchesSelectedClass
  }) : []

  // Get unique classes for the dropdown
  const uniqueClasses = Array.isArray(students)
    ? [...new Set(students.map(s => s.class))].sort()
    : []

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (students.length === 0) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Access to Results</h3>
          <p className="text-gray-600">
            {user?.role === 'parent' 
              ? "No children found in your account. Please contact the school administration."
              : user?.role === 'teacher'
              ? "No classes assigned to you. Please contact the school administration."
              : "No students found in the system."}
          </p>
        </div>
      </div>
    )
  }

  // Helper functions
  const handleViewResults = (student: Student) => {
    setModalStudent(student)
    setShowResultsModal(true)
  }

  const closeModal = () => {
    setShowResultsModal(false)
    setModalStudent(null)
  }

  const getStudentResults = (studentId: string) => {
    // Ensure results is always an array
    if (!Array.isArray(results)) {
      console.warn('Results is not an array:', results)
      return []
    }
    return results.filter(r => r.studentId === studentId)
  }

  // Get all results for a student (ignoring current filters) to check if they have any results
  const getAllStudentResults = (studentId: string) => {
    if (!Array.isArray(results)) {
      return []
    }
    return results.filter(r => r.studentId === studentId)
  }

  const getStudentSummary = (studentId: string) => {
    const studentResults = getStudentResults(studentId)
    if (!Array.isArray(studentResults) || studentResults.length === 0) return null

    try {
      const totalMarks = studentResults.reduce((sum, r) => sum + (r.marks || 0), 0)
      const totalPossible = studentResults.reduce((sum, r) => sum + (r.totalMarks || 0), 0)
      const averagePercentage = totalPossible > 0 ? Math.round((totalMarks / totalPossible) * 100) : 0
      const passedSubjects = studentResults.filter(r => r.isPassed).length

      return {
        totalSubjects: studentResults.length,
        passedSubjects,
        averagePercentage,
        overallGrade: calculateOverallGrade(studentResults)
      }
    } catch (error) {
      console.error('Error calculating student summary:', error)
      return null
    }
  }

  // Calculate stats with error handling
  const totalStudents = Array.isArray(students) ? students.length : 0
  const studentsWithResults = Array.isArray(students)
    ? students.filter(s => getStudentResults(s.id).length > 0).length
    : 0
  const totalResults = Array.isArray(results) ? results.length : 0
  const averagePassRate = studentsWithResults > 0 && Array.isArray(students)
    ? Math.round((students.reduce((sum, s) => {
        const summary = getStudentSummary(s.id)
        return sum + (summary ? (summary.passedSubjects / summary.totalSubjects) : 0)
      }, 0) / studentsWithResults) * 100)
    : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">View Results</h1>
          <p className="text-gray-600 mt-2">
            {user?.role === 'parent' && "View your children's academic results"}
            {user?.role === 'teacher' && "View results for your assigned classes"}
            {user?.role === 'admin' && "View all student results"}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Students</p>
              <p className="text-2xl font-bold text-blue-600">{totalStudents}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">With Results</p>
              <p className="text-2xl font-bold text-green-600">{studentsWithResults}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Results</p>
              <p className="text-2xl font-bold text-purple-600">{totalResults}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Pass Rate</p>
              <p className="text-2xl font-bold text-orange-600">{averagePassRate}%</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex flex-wrap gap-4">
          <div className="relative flex-1 min-w-64">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search students by name or class..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>

          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            title="Filter by class"
          >
            <option value="all">All Classes</option>
            {uniqueClasses.map(className => (
              <option key={className} value={className}>
                {className}
              </option>
            ))}
          </select>

          <select
            value={selectedTerm}
            onChange={(e) => setSelectedTerm(e.target.value as Term | 'all')}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            title="Filter by term"
          >
            <option value="all">All Terms</option>
            <option value="First">First Term</option>
            <option value="Second">Second Term</option>
            <option value="Third">Third Term</option>
          </select>

          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            title="Filter by academic year"
          >
            {getAvailableAcademicYears().map((year) => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            Students ({filteredStudents.length})
          </h2>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {filteredStudents.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No students found matching your criteria.</p>
            </div>
          ) : (
            filteredStudents.map((student, index) => {
              // Check if student has ANY results (ignoring current filters)
              const allStudentResults = getAllStudentResults(student.id)
              const hasAnyResults = allStudentResults.length > 0

              // Get filtered results for current term/year selection
              const filteredStudentResults = getStudentResults(student.id)
              const summary = getStudentSummary(student.id)

              return (
                <div
                  key={student.id}
                  className={`flex items-center justify-between p-4 hover:bg-white/50 transition-all duration-200 ${
                    index !== filteredStudents.length - 1 ? 'border-b border-gray-100' : ''
                  }`}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                      {student.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-1">
                        <h3 className="font-semibold text-gray-900 truncate">{student.name}</h3>
                        <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full flex-shrink-0">
                          {student.class}
                        </span>
                      </div>

                      {hasAnyResults ? (
                        summary ? (
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              {summary.totalSubjects} subjects
                            </span>

                            <span className="flex items-center gap-1">
                              <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                              </svg>
                              {summary.averagePercentage}% avg
                            </span>

                            <span className="flex items-center gap-1">
                              <svg className="w-4 h-4 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {summary.passedSubjects}/{summary.totalSubjects} passed
                            </span>

                            <div className="flex items-center gap-1">
                              <span
                                className="px-2 py-1 text-xs font-semibold rounded text-white"
                                style={{ backgroundColor: getGradeColor(summary.overallGrade) }}
                              >
                                {summary.overallGrade}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span>Has results ({allStudentResults.length} total) - Select different term/year</span>
                          </div>
                        )
                      ) : (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <span>No results available</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => handleViewResults(student)}
                    disabled={!hasAnyResults}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 flex-shrink-0 ml-4 ${
                      hasAnyResults
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                        : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <span className="text-sm font-medium">
                      {hasAnyResults ? 'View Results' : 'No Results'}
                    </span>
                  </button>
                </div>
              )
            })
          )}
        </div>
      </div>

      {/* This empty state is now handled inside the students list */}

      {/* Results Modal */}
      <Modal
        isOpen={showResultsModal}
        onClose={closeModal}
        title={`Results for ${modalStudent?.name || ''}`}
      >
        {modalStudent && (
          <div className="max-h-[75vh] overflow-y-auto">
            <div className="space-y-4">
              {/* Student Info Header */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold">
                    {modalStudent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{modalStudent.name}</h3>
                    <p className="text-sm text-gray-600">Class: {modalStudent.class}</p>
                  </div>
                </div>
              </div>

              {/* Results Table */}
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-sm font-semibold text-gray-900">Academic Results</h4>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Marks</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">%</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getAllStudentResults(modalStudent.id).map((result, index) => (
                        <tr key={result.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-4 py-3 text-sm">
                            <div>
                              <div className="font-medium text-gray-900">{result.subjectName}</div>
                              <div className="text-gray-500 text-xs">{result.subjectCode}</div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-center text-gray-900">{result.term}</td>
                          <td className="px-4 py-3 text-sm text-center font-semibold text-gray-900">{result.marks}</td>
                          <td className="px-4 py-3 text-sm text-center text-gray-900">{result.totalMarks}</td>
                          <td className="px-4 py-3 text-sm text-center font-semibold text-gray-900">{result.percentage}%</td>
                          <td className="px-4 py-3 text-center">
                            <span
                              className="px-2 py-1 text-xs font-semibold rounded text-white"
                              style={{ backgroundColor: getGradeColor(result.grade) }}
                            >
                              {result.grade}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className={`px-2 py-1 text-xs font-semibold rounded ${
                              result.isPassed
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {result.isPassed ? 'Passed' : 'Failed'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Summary Stats */}
              {(() => {
                // Calculate summary from all student results for modal
                const allResults = getAllStudentResults(modalStudent.id)
                if (allResults.length === 0) return null

                const totalMarks = allResults.reduce((sum, r) => sum + (r.marks || 0), 0)
                const totalPossible = allResults.reduce((sum, r) => sum + (r.totalMarks || 0), 0)
                const averagePercentage = totalPossible > 0 ? Math.round((totalMarks / totalPossible) * 100) : 0
                const passedSubjects = allResults.filter(r => r.isPassed).length
                const overallGrade = calculateOverallGrade(allResults)

                const summary = {
                  totalSubjects: allResults.length,
                  passedSubjects,
                  averagePercentage,
                  overallGrade
                }

                return (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-2xl font-bold text-blue-600">{summary.totalSubjects}</div>
                      <div className="text-sm text-gray-600">Total Subjects</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="text-2xl font-bold text-green-600">{summary.passedSubjects}</div>
                      <div className="text-sm text-gray-600">Passed</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="text-2xl font-bold text-purple-600">{summary.averagePercentage}%</div>
                      <div className="text-sm text-gray-600">Average</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="text-2xl font-bold text-orange-600">{summary.overallGrade}</div>
                      <div className="text-sm text-gray-600">Overall Grade</div>
                    </div>
                  </div>
                )
              })()}
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}
