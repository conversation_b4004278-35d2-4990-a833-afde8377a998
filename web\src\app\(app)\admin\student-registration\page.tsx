"use client";

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/hooks/use-auth";
import { Student, EnrollmentStatus, Gender, StudentFormData } from "@/lib/types";
import { useState, useEffect } from "react";
import { collection, getDocs, query, where, orderBy, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore';
import { db } from "@/lib/firebase";
import { 
  Users, 
  Plus, 
  Search, 
  Filter,
  GraduationCap,
  Calendar,
  UserCheck,
  AlertCircle,
  CheckCircle,
  Pause,
  XCircle,
  Award,
  Eye,
  Edit,
  FileUp,
  MoreHorizontal
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import { AddStudentDialog } from "@/components/students/add-student-dialog";
import { EditStudentDialog } from "@/components/students/edit-student-dialog";
import { StudentDetailsDialog } from "@/components/students/student-details-dialog";
import { UploadResultDialog } from "@/components/students/upload-result-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";

export default function StudentRegistrationPage() {
  const { user } = useAuth();
  const { toast } = useFloatingToast();
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [classFilter, setClassFilter] = useState<string>("all");
  const [genderFilter, setGenderFilter] = useState<string>("all");

  useEffect(() => {
    const fetchStudents = async () => {
      if (!user || user.role !== 'admin') return;
      setLoading(true);

      const studentsCollection = collection(db, 'students');
      const studentsQuery = query(studentsCollection, orderBy('dateAdded', 'desc'));
      const studentSnapshot = await getDocs(studentsQuery);
      
      const studentsData = await Promise.all(studentSnapshot.docs.map(async (doc) => {
        const student = { id: doc.id, ...doc.data() } as Student;
        const resultsCollection = collection(db, `students/${student.id}/results`);
        const resultsSnapshot = await getDocs(resultsCollection);
        student.results = resultsSnapshot.docs.map(resDoc => ({ id: resDoc.id, ...resDoc.data() } as any));
        return student;
      }));
      
      setStudents(studentsData);
      setLoading(false);
    };

    fetchStudents();
  }, [user]);

  // Filter students based on search and filters
  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || student.enrollmentStatus === statusFilter;
    const matchesClass = classFilter === "all" || student.class === classFilter;
    const matchesGender = genderFilter === "all" || student.gender === genderFilter;
    
    return matchesSearch && matchesStatus && matchesClass && matchesGender;
  });

  // Calculate statistics
  const stats = {
    total: students.length,
    active: students.filter(s => s.enrollmentStatus === EnrollmentStatus.Active).length,
    onHold: students.filter(s => s.enrollmentStatus === EnrollmentStatus.OnHold).length,
    inactive: students.filter(s => s.enrollmentStatus === EnrollmentStatus.Inactive).length,
    completed: students.filter(s => s.enrollmentStatus === EnrollmentStatus.Completed).length,
  };

  const getStatusBadge = (status: EnrollmentStatus) => {
    const statusConfig = {
      [EnrollmentStatus.Active]: { 
        label: "Active", 
        variant: "default" as const, 
        icon: CheckCircle,
        className: "bg-green-100 text-green-800 border-green-200"
      },
      [EnrollmentStatus.OnHold]: { 
        label: "On Hold", 
        variant: "secondary" as const, 
        icon: Pause,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200"
      },
      [EnrollmentStatus.Inactive]: { 
        label: "Inactive", 
        variant: "error" as const, 
        icon: XCircle,
        className: "bg-red-100 text-red-800 border-red-200"
      },
      [EnrollmentStatus.Completed]: { 
        label: "Completed", 
        variant: "default" as const, 
        icon: Award,
        className: "bg-blue-100 text-blue-800 border-blue-200"
      },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className={config.className}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getGenderBadge = (gender: Gender) => {
    const genderConfig = {
      [Gender.Male]: { label: "Male", className: "bg-blue-100 text-blue-800 border-blue-200" },
      [Gender.Female]: { label: "Female", className: "bg-pink-100 text-pink-800 border-pink-200" },
      [Gender.Other]: { label: "Other", className: "bg-purple-100 text-purple-800 border-purple-200" },
    };

    const config = genderConfig[gender];

    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <AlertCircle className="w-16 h-16 text-muted-foreground mx-auto" />
          <h3 className="text-lg font-semibold text-foreground">Access Denied</h3>
          <p className="text-sm text-muted-foreground">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 animate-fade-in">
        <div className="flex items-center justify-center h-64">
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              <div className="absolute inset-0 h-12 w-12 animate-ping rounded-full border-2 border-primary/30" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">Loading students...</h3>
              <p className="text-sm text-muted-foreground">Please wait while we fetch the data.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 animate-fade-in">
      {/* Header Section */}
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent truncate">
            Student Registration
          </h1>
          <p className="text-muted-foreground mt-1 truncate">
            Comprehensive student enrollment management and registration system.
          </p>
        </div>
        <div className="flex items-center gap-3 shrink-0">
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            <Users className="w-3 h-3 mr-1" />
            {filteredStudents.length} of {students.length} Students
          </Badge>
          <AddStudentDialog onAddStudent={(data) => {
            setStudents(prev => [data as Student, ...prev]);
            toast({
              title: "Student Registered",
              description: "New student has been successfully registered.",
              variant: "success"
            });
          }}>
            <Button className="btn-modern">
              <Plus className="mr-2 h-4 w-4" />
              Register Student
            </Button>
          </AddStudentDialog>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-5">
        <Card className="stats-card gradient-primary group hover:scale-105 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">
              Total Students
            </CardTitle>
            <div className="p-2 rounded-lg bg-background/50 text-primary">
              <Users className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.total}</div>
            <p className="text-xs text-muted-foreground mt-2">
              All registered students
            </p>
          </CardContent>
        </Card>

        <Card className="stats-card gradient-success group hover:scale-105 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">
              Active
            </CardTitle>
            <div className="p-2 rounded-lg bg-background/50 text-success">
              <CheckCircle className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.active}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Currently enrolled
            </p>
          </CardContent>
        </Card>

        <Card className="stats-card gradient-warning group hover:scale-105 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">
              On Hold
            </CardTitle>
            <div className="p-2 rounded-lg bg-background/50 text-warning">
              <Pause className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.onHold}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Temporarily suspended
            </p>
          </CardContent>
        </Card>

        <Card className="stats-card gradient-error group hover:scale-105 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">
              Inactive
            </CardTitle>
            <div className="p-2 rounded-lg bg-background/50 text-error">
              <XCircle className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.inactive}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Discontinued
            </p>
          </CardContent>
        </Card>

        <Card className="stats-card gradient-info group hover:scale-105 transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">
              Completed
            </CardTitle>
            <div className="p-2 rounded-lg bg-background/50 text-info">
              <Award className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.completed}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Graduated students
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Filters */}
      <Card className="card-modern">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Student Registry</CardTitle>
          <CardDescription>
            Search and filter students by various criteria.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6 flex-wrap">
            <div className="relative flex-1 min-w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search students by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40 min-w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={EnrollmentStatus.Active}>Active</SelectItem>
                <SelectItem value={EnrollmentStatus.OnHold}>On Hold</SelectItem>
                <SelectItem value={EnrollmentStatus.Inactive}>Inactive</SelectItem>
                <SelectItem value={EnrollmentStatus.Completed}>Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={classFilter} onValueChange={setClassFilter}>
              <SelectTrigger className="w-40 min-w-32">
                <SelectValue placeholder="Class" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                <SelectItem value="Nursery 1">Nursery 1</SelectItem>
                <SelectItem value="Nursery 2">Nursery 2</SelectItem>
                <SelectItem value="KG 1">KG 1</SelectItem>
                <SelectItem value="KG 2">KG 2</SelectItem>
                <SelectItem value="Basic 1">Basic 1</SelectItem>
                <SelectItem value="Basic 2">Basic 2</SelectItem>
                <SelectItem value="Basic 3">Basic 3</SelectItem>
                <SelectItem value="Basic 4">Basic 4</SelectItem>
                <SelectItem value="Basic 5">Basic 5</SelectItem>
                <SelectItem value="Basic 6">Basic 6</SelectItem>
                <SelectItem value="Basic 7">Basic 7</SelectItem>
                <SelectItem value="Basic 8">Basic 8</SelectItem>
                <SelectItem value="Basic 9">Basic 9</SelectItem>
              </SelectContent>
            </Select>
            <Select value={genderFilter} onValueChange={setGenderFilter}>
              <SelectTrigger className="w-40 min-w-32">
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Genders</SelectItem>
                <SelectItem value={Gender.Male}>Male</SelectItem>
                <SelectItem value={Gender.Female}>Female</SelectItem>
                <SelectItem value={Gender.Other}>Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Student Table */}
          <div className="space-y-3">
            {filteredStudents.map((student) => (
              <StudentRegistrationCard 
                key={student.id} 
                student={student}
                onUpdate={(updatedStudent) => {
                  setStudents(prev => prev.map(s => s.id === updatedStudent.id ? updatedStudent : s));
                }}
              />
            ))}
          </div>

          {filteredStudents.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
                <Users className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">No students found</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm || statusFilter !== "all" || classFilter !== "all" || genderFilter !== "all"
                  ? "Try adjusting your search or filter criteria."
                  : "No students have been registered yet."
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Enhanced Student Card Component
function StudentRegistrationCard({ student, onUpdate }: { 
  student: Student; 
  onUpdate: (student: Student) => void;
}) {
  const initials = student.name.split(' ').map(n => n[0]).join('');
  
  // Calculate unique terms
  const uniqueTerms = new Set();
  student.results?.forEach((result: any) => {
    uniqueTerms.add(`${result.term}-${result.year}`);
  });

  const getStatusBadge = (status: EnrollmentStatus) => {
    const statusConfig = {
      [EnrollmentStatus.Active]: { 
        label: "Active", 
        className: "bg-green-100 text-green-800 border-green-200"
      },
      [EnrollmentStatus.OnHold]: { 
        label: "On Hold", 
        className: "bg-yellow-100 text-yellow-800 border-yellow-200"
      },
      [EnrollmentStatus.Inactive]: { 
        label: "Inactive", 
        className: "bg-red-100 text-red-800 border-red-200"
      },
      [EnrollmentStatus.Completed]: { 
        label: "Completed", 
        className: "bg-blue-100 text-blue-800 border-blue-200"
      },
    };

    const config = statusConfig[status];

    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getGenderBadge = (gender: Gender) => {
    const genderConfig = {
      [Gender.Male]: { label: "Male", className: "bg-blue-100 text-blue-800 border-blue-200" },
      [Gender.Female]: { label: "Female", className: "bg-pink-100 text-pink-800 border-pink-200" },
      [Gender.Other]: { label: "Other", className: "bg-purple-100 text-purple-800 border-purple-200" },
    };

    const config = genderConfig[gender];

    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  return (
    <Card className="group hover:shadow-md transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-4 min-w-0 flex-1">
            <Avatar className="h-10 w-10 ring-2 ring-border/50 shrink-0">
              <AvatarImage src={student.avatarUrl} alt={student.name} />
              <AvatarFallback className="bg-gradient-to-br from-primary to-primary/70 text-white font-semibold">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1 flex-wrap">
                <h3 className="font-semibold text-foreground truncate">{student.name}</h3>
                {getGenderBadge(student.gender)}
                {getStatusBadge(student.enrollmentStatus)}
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground flex-wrap">
                <span className="truncate">ID: {student.id}</span>
                <span className="hidden sm:inline">•</span>
                <span className="truncate">{student.class}</span>
                <span className="hidden sm:inline">•</span>
                <span className="truncate">Age: {new Date().getFullYear() - new Date(student.dob).getFullYear()}</span>
                <span className="hidden lg:inline">•</span>
                <span className="hidden lg:inline truncate">Enrolled: {new Date(student.enrollmentDate).toLocaleDateString()}</span>
                <span className="hidden md:inline">•</span>
                <span className="truncate">{uniqueTerms.size} terms</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <Badge variant={uniqueTerms.size > 0 ? "default" : "secondary"}>
              {uniqueTerms.size} terms
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <StudentDetailsDialog student={student}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Eye className="mr-2 h-4 w-4" />View details
                  </DropdownMenuItem>
                </StudentDetailsDialog>
                <DropdownMenuSeparator />
                <EditStudentDialog student={student} onUpdateStudent={onUpdate}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Edit className="mr-2 h-4 w-4" />Edit student
                  </DropdownMenuItem>
                </EditStudentDialog>
                <UploadResultDialog student={student} onUploadResult={(result) => {
                  // Handle result upload
                }}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <FileUp className="mr-2 h-4 w-4" />Upload results
                  </DropdownMenuItem>
                </UploadResultDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 