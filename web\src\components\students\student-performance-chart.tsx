
"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, <PERSON>Axi<PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Student } from "@/lib/types"
import { useMemo } from "react"

interface StudentPerformanceChartProps {
    students: Student[];
}

const gradeOrder = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F'];
const gradeColors: { [key: string]: string } = {
    'A+': 'hsl(var(--chart-1))',
    'A': 'hsl(var(--chart-1))',
    'A-': 'hsl(var(--chart-1))',
    'B+': 'hsl(var(--chart-2))',
    'B': 'hsl(var(--chart-2))',
    'B-': 'hsl(var(--chart-2))',
    'C+': 'hsl(var(--chart-3))',
    'C': 'hsl(var(--chart-3))',
    'C-': 'hsl(var(--chart-3))',
    'D+': 'hsl(var(--chart-4))',
    'D': 'hsl(var(--chart-4))',
    'F': 'hsl(var(--chart-5))',
};


export function StudentPerformanceChart({ students }: StudentPerformanceChartProps) {
    const chartData = useMemo(() => {
        const gradeCounts: { [key: string]: number } = {};

        students.forEach(student => {
            student.results.forEach(result => {
                const grade = result.grade.toUpperCase();
                gradeCounts[grade] = (gradeCounts[grade] || 0) + 1;
            });
        });

        return gradeOrder.map(grade => ({
            grade,
            count: gradeCounts[grade] || 0,
            fill: gradeColors[grade] || 'hsl(var(--muted))'
        })).filter(item => item.count > 0);

    }, [students]);

    const chartConfig = {
        count: {
          label: "Number of Students",
        },
        ...Object.fromEntries(
            Object.entries(gradeColors).map(([grade, color]) => [grade, { label: grade, color }])
        )
      } satisfies ChartConfig


  if (chartData.length === 0) {
    return <p className="text-sm text-muted-foreground text-center py-8">No results available to display chart.</p>
  }


  return (
    <ChartContainer config={chartConfig} className="min-h-[200px] w-full">
      <BarChart accessibilityLayer data={chartData}>
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="grade"
          tickLine={false}
          tickMargin={10}
          axisLine={false}
        />
        <YAxis allowDecimals={false} />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent indicator="dot" />}
        />
        <Bar dataKey="count" radius={4} />
      </BarChart>
    </ChartContainer>
  )
}
