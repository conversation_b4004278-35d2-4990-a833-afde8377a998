'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useRealtimeStudents } from '@/hooks/use-realtime-data';
import {
  FeeStructure,
  StudentPayment,
  PaymentRecord,
  ClassLevel,
  PaymentStatus,
  PaymentMethod,
  CLASS_LEVEL_GROUPS,
  DEFAULT_FEE_STRUCTURE,
  FEE_TYPE_LABELS,
  PAYMENT_STATUS_COLORS
} from '@/lib/academic-fees-types';
import {
  calculateTotalFees,
  calculateAmountPaid,
  calculateAmountRemaining,
  determinePaymentStatus,
  generateReceiptNumber,
  formatCurrency,
  formatDate,
  getCurrentAcademicYear,
  validateFeeStructure,
  filterStudentPayments,
  sortStudentPayments
} from '@/lib/academic-fees-utils';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, DollarSign, Users, TrendingUp, Calendar } from "lucide-react";
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export default function AcademicFeesPage() {
  const { user } = useAuth();
  const { students } = useRealtimeStudents();
  const [activeTab, setActiveTab] = useState<'overview' | 'fee-structure' | 'payments' | 'reports'>('overview');
  const [feeStructures, setFeeStructures] = useState<FeeStructure[]>([]);
  const [studentPayments, setStudentPayments] = useState<StudentPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAcademicYear, setSelectedAcademicYear] = useState(getCurrentAcademicYear());

  // Real-time listeners for fee data
  useEffect(() => {
    if (!user || user.role !== 'admin') return;

    const unsubscribes: (() => void)[] = [];

    // Listen to fee structures
    const feeStructuresQuery = query(
      collection(db, 'feeStructures'),
      where('academicYear', '==', selectedAcademicYear),
      orderBy('classLevel')
    );

    const unsubscribeFeeStructures = onSnapshot(feeStructuresQuery, (snapshot) => {
      const structures = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as FeeStructure[];
      setFeeStructures(structures);
    });

    unsubscribes.push(unsubscribeFeeStructures);

    // Listen to student payments
    const paymentsQuery = query(
      collection(db, 'studentPayments'),
      where('academicYear', '==', selectedAcademicYear),
      orderBy('studentName')
    );

    const unsubscribePayments = onSnapshot(paymentsQuery, (snapshot) => {
      const payments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as StudentPayment[];
      setStudentPayments(payments);
      setLoading(false);
    });

    unsubscribes.push(unsubscribePayments);

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [user, selectedAcademicYear]);

  // Calculate overview statistics
  const overviewStats = {
    totalStudents: students.length,
    studentsWithPayments: studentPayments.filter(sp => sp.payments.length > 0).length,
    totalExpected: studentPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0),
    totalCollected: studentPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0),
    collectionRate: studentPayments.length > 0 ? 
      Math.round((studentPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0) / 
                  studentPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0)) * 100) : 0
  };

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only administrators can access the Academic Fees management.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Academic Fees Management</h1>
              <p className="text-gray-600 mt-2">Manage fee structures and track student payments</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedAcademicYear}
                onChange={(e) => setSelectedAcademicYear(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                aria-label="Select academic year"
                title="Select academic year for fee management"
              >
                <option value="2024-2025">2024-2025</option>
                <option value="2023-2024">2023-2024</option>
                <option value="2025-2026">2025-2026</option>
              </select>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-xl">
                <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{overviewStats.totalStudents}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-xl">
                <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expected Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.totalExpected)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-xl">
                <svg className="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Collected</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.totalCollected)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-orange-100 rounded-xl">
                <svg className="w-6 h-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Collection Rate</p>
                <p className="text-2xl font-bold text-gray-900">{overviewStats.collectionRate}%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: '📊' },
                { id: 'fee-structure', name: 'Fee Structure', icon: '💰' },
                { id: 'payments', name: 'Student Payments', icon: '💳' },
                { id: 'reports', name: 'Reports', icon: '📈' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-label={`Switch to ${tab.name} tab`}
                  title={`Switch to ${tab.name} tab`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && <OverviewTab studentPayments={studentPayments} />}
            {activeTab === 'fee-structure' && (
              <FeeStructureTab 
                feeStructures={feeStructures} 
                academicYear={selectedAcademicYear}
                onUpdate={() => {/* Refresh handled by real-time listener */}}
              />
            )}
            {activeTab === 'payments' && (
              <PaymentsTab 
                studentPayments={studentPayments}
                students={students}
                feeStructures={feeStructures}
                academicYear={selectedAcademicYear}
              />
            )}
            {activeTab === 'reports' && <ReportsTab studentPayments={studentPayments} />}
          </div>
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ studentPayments }: { studentPayments: StudentPayment[] }) {
  // Group payments by class level
  const paymentsByClass = Object.entries(CLASS_LEVEL_GROUPS).map(([groupName, classes]) => {
    const classPayments = studentPayments.filter(sp => classes.includes(sp.classLevel));
    const totalExpected = classPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0);
    const totalCollected = classPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0);
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    return {
      groupName,
      classes,
      studentCount: classPayments.length,
      totalExpected,
      totalCollected,
      collectionRate
    };
  });

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Fee Collection Overview</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {paymentsByClass.map((group) => (
          <div key={group.groupName} className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">{group.groupName}</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Students:</span>
                <span className="font-medium">{group.studentCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expected:</span>
                <span className="font-medium">{formatCurrency(group.totalExpected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collected:</span>
                <span className="font-medium text-green-600">{formatCurrency(group.totalCollected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collection Rate:</span>
                <span className={`font-medium ${group.collectionRate >= 80 ? 'text-green-600' : group.collectionRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {group.collectionRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${group.collectionRate >= 80 ? 'bg-green-500' : group.collectionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                  style={{ width: `${group.collectionRate}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Fee Structure Tab Component
function FeeStructureTab({
  feeStructures,
  academicYear,
  onUpdate
}: {
  feeStructures: FeeStructure[];
  academicYear: string;
  onUpdate: () => void;
}) {
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingStructure, setEditingStructure] = useState<FeeStructure | null>(null);
  const [formData, setFormData] = useState({
    classLevel: '' as ClassLevel,
    fees: { ...DEFAULT_FEE_STRUCTURE },
    currency: 'GHS',
    paymentFrequency: 'yearly' as 'yearly' | 'semester' | 'term',
    termNumber: 1 as 1 | 2 | 3, // For term payments
    semesterNumber: 1 as 1 | 2   // For semester payments
  });
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const totalAmount = calculateTotalFees({ fees: formData.fees } as FeeStructure);

      const feeStructureData = {
        classLevel: formData.classLevel,
        academicYear,
        fees: formData.fees,
        totalAmount,
        currency: formData.currency,
        paymentFrequency: formData.paymentFrequency,
        ...(formData.paymentFrequency === 'term' && { termNumber: formData.termNumber }),
        ...(formData.paymentFrequency === 'semester' && { semesterNumber: formData.semesterNumber }),
        updatedAt: new Date().toISOString(),
        isActive: true
      };

      if (editingStructure) {
        // Update existing
        await updateDoc(doc(db, 'feeStructures', editingStructure.id), feeStructureData);
      } else {
        // Create new
        await addDoc(collection(db, 'feeStructures'), {
          ...feeStructureData,
          createdAt: new Date().toISOString(),
          createdBy: user.id
        });
      }

      // Reset form
      setFormData({
        classLevel: '' as ClassLevel,
        fees: { ...DEFAULT_FEE_STRUCTURE },
        currency: 'GHS',
        paymentFrequency: 'yearly',
        termNumber: 1,
        semesterNumber: 1
      });
      setShowCreateForm(false);
      setEditingStructure(null);
      onUpdate();
    } catch (error) {
      console.error('Error saving fee structure:', error);
      alert('Failed to save fee structure');
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (structure: FeeStructure) => {
    setEditingStructure(structure);
    setFormData({
      classLevel: structure.classLevel,
      fees: { ...structure.fees },
      currency: structure.currency,
      paymentFrequency: (structure as any).paymentFrequency || 'yearly',
      termNumber: (structure as any).termNumber || 1,
      semesterNumber: (structure as any).semesterNumber || 1
    });
    setShowCreateForm(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Fee Structure Management</h3>
          <p className="text-sm text-muted-foreground">Create and manage fee structures for different class levels</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Fee Structure
        </Button>
      </div>

      {/* Fee Structures List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {feeStructures.map((structure) => (
          <Card key={structure.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{structure.classLevel}</CardTitle>
                  <CardDescription>
                    {(structure as any).paymentFrequency === 'yearly' ? 'Annual Payment' :
                     (structure as any).paymentFrequency === 'semester' ? `Semester ${(structure as any).semesterNumber || 1}` :
                     `Term ${(structure as any).termNumber || 1}`}
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(structure)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">

              <div className="space-y-2 text-sm">
                {Object.entries(structure.fees)
                  .filter(([key, value]) => value && value > 0) // Only show fees with amounts
                  .map(([key, value]) => (
                    <div key={key} className="flex justify-between items-center">
                      <span className="text-muted-foreground">{FEE_TYPE_LABELS[key as keyof typeof FEE_TYPE_LABELS]}</span>
                      <Badge variant="secondary" className="font-medium">
                        {formatCurrency(value || 0)}
                      </Badge>
                    </div>
                  ))}

                <div className="border-t pt-3 mt-3">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-foreground">Total Amount</span>
                    <Badge className="text-base font-bold">
                      {formatCurrency(structure.totalAmount)}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create/Edit Form Modal */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              {editingStructure ? 'Edit Fee Structure' : 'Create Fee Structure'}
            </DialogTitle>
            <DialogDescription>
              {editingStructure
                ? 'Update the fee structure details below.'
                : 'Create a new fee structure for a class level.'}
            </DialogDescription>
          </DialogHeader>

          <div className="max-h-[calc(90vh-200px)] overflow-y-auto pr-2">
            <div className="space-y-6">
              {/* Class Level Selection */}
              <div className="space-y-2">
                <Label htmlFor="classLevel">Class Level</Label>
                <Select
                  value={formData.classLevel}
                  onValueChange={(value) => setFormData({ ...formData, classLevel: value as ClassLevel })}
                  disabled={!!editingStructure}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Class Level" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(CLASS_LEVEL_GROUPS).flat().map((level) => (
                      <SelectItem key={level} value={level}>{level}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {editingStructure && (
                  <p className="text-xs text-muted-foreground">Class level cannot be changed when editing</p>
                )}
              </div>

              {/* Payment Frequency Selection */}
              <div className="space-y-2">
                <Label htmlFor="paymentFrequency">Payment Frequency</Label>
                <Select
                  value={formData.paymentFrequency}
                  onValueChange={(value) => setFormData({ ...formData, paymentFrequency: value as 'yearly' | 'semester' | 'term' })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Payment Frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yearly">Yearly (Annual Payment)</SelectItem>
                    <SelectItem value="semester">Semester (2 payments per year)</SelectItem>
                    <SelectItem value="term">Term (3 payments per year)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose how often students will pay these fees during the academic year
                </p>
              </div>

              {/* Term Selection - Only show for term frequency */}
              {formData.paymentFrequency === 'term' && (
                <div className="space-y-2">
                  <Label htmlFor="termNumber">Select Term</Label>
                  <Select
                    value={formData.termNumber.toString()}
                    onValueChange={(value) => setFormData({ ...formData, termNumber: parseInt(value) as 1 | 2 | 3 })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Term" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Term 1 (First Term)</SelectItem>
                      <SelectItem value="2">Term 2 (Second Term)</SelectItem>
                      <SelectItem value="3">Term 3 (Third Term)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    This fee structure will apply to the selected term only
                  </p>
                </div>
              )}

              {/* Semester Selection - Only show for semester frequency */}
              {formData.paymentFrequency === 'semester' && (
                <div className="space-y-2">
                  <Label htmlFor="semesterNumber">Select Semester</Label>
                  <Select
                    value={formData.semesterNumber.toString()}
                    onValueChange={(value) => setFormData({ ...formData, semesterNumber: parseInt(value) as 1 | 2 })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Semester" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Semester 1 (First Semester)</SelectItem>
                      <SelectItem value="2">Semester 2 (Second Semester)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    This fee structure will apply to the selected semester only
                  </p>
                </div>
              )}

              {/* Fee Structure Fields */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <DollarSign className="w-5 h-5 text-primary" />
                  <h4 className="text-lg font-semibold">Fee Breakdown</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(FEE_TYPE_LABELS).map(([key, label]) => (
                    <div key={key} className="space-y-2">
                      <Label htmlFor={key}>{label}</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                          GH₵
                        </span>
                        <Input
                          id={key}
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.fees[key as keyof typeof formData.fees] || 0}
                          onChange={(e) => setFormData({
                            ...formData,
                            fees: {
                              ...formData.fees,
                              [key]: parseFloat(e.target.value) || 0
                            }
                          })}
                          className="pl-12"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Total Amount Display */}
              <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <TrendingUp className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Total Fee Structure</p>
                        <p className="font-semibold">
                          {formData.paymentFrequency === 'yearly' ? 'Annual Amount' :
                           formData.paymentFrequency === 'semester' ? `Semester ${formData.semesterNumber} Amount` :
                           `Term ${formData.termNumber} Amount`}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        {formatCurrency(calculateTotalFees({ fees: formData.fees } as FeeStructure))}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formData.paymentFrequency === 'yearly' ? 'Per academic year' :
                         formData.paymentFrequency === 'semester' ? `For semester ${formData.semesterNumber} only` :
                         `For term ${formData.termNumber} only`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowCreateForm(false);
                setEditingStructure(null);
                setFormData({
                  classLevel: '' as ClassLevel,
                  fees: { ...DEFAULT_FEE_STRUCTURE },
                  currency: 'GHS',
                  paymentFrequency: 'yearly',
                  termNumber: 1,
                  semesterNumber: 1
                });
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              disabled={saving || !formData.classLevel}
              className="min-w-[120px]"
            >
              {saving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  {editingStructure ? <Edit className="w-4 h-4 mr-2" /> : <Plus className="w-4 h-4 mr-2" />}
                  {editingStructure ? 'Update Structure' : 'Create Structure'}
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Payments Tab Component (simplified for web)
function PaymentsTab({
  studentPayments,
  students,
  feeStructures,
  academicYear
}: {
  studentPayments: StudentPayment[];
  students: any[];
  feeStructures: FeeStructure[];
  academicYear: string;
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<PaymentStatus | ''>('');
  const [filterClass, setFilterClass] = useState<ClassLevel | ''>('');

  // Filter payments
  const filteredPayments = filterStudentPayments(studentPayments, {
    searchTerm,
    paymentStatus: filterStatus || undefined,
    classLevel: filterClass || undefined,
    academicYear
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Student Payments</h3>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <input
          type="text"
          placeholder="Search students..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as PaymentStatus | '')}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Filter by payment status"
          aria-label="Filter by payment status"
        >
          <option value="">All Payment Status</option>
          <option value="pending">Pending</option>
          <option value="partial">Partial</option>
          <option value="completed">Completed</option>
          <option value="overdue">Overdue</option>
        </select>
        <select
          value={filterClass}
          onChange={(e) => setFilterClass(e.target.value as ClassLevel | '')}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Filter by class level"
          aria-label="Filter by class level"
        >
          <option value="">All Classes</option>
          {Object.values(CLASS_LEVEL_GROUPS).flat().map((level) => (
            <option key={level} value={level}>{level}</option>
          ))}
        </select>
      </div>

      {/* Payments Table */}
      <div className="bg-white rounded-xl overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Due</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Paid</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{payment.studentName}</div>
                      <div className="text-sm text-gray-500">{payment.parentName}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.classLevel}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(payment.totalAmountDue)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{formatCurrency(payment.totalAmountPaid)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{formatCurrency(payment.amountRemaining)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${PAYMENT_STATUS_COLORS[payment.paymentStatus].bg} ${PAYMENT_STATUS_COLORS[payment.paymentStatus].text}`}>
                      {payment.paymentStatus}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Reports Tab Component
function ReportsTab({ studentPayments }: { studentPayments: StudentPayment[] }) {
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'outstanding'>('summary');

  const generateSummaryReport = () => {
    const totalStudents = studentPayments.length;
    const totalExpected = studentPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0);
    const totalCollected = studentPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0);
    const totalOutstanding = totalExpected - totalCollected;
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    const statusBreakdown = {
      completed: studentPayments.filter(sp => sp.paymentStatus === 'completed').length,
      partial: studentPayments.filter(sp => sp.paymentStatus === 'partial').length,
      pending: studentPayments.filter(sp => sp.paymentStatus === 'pending').length,
      overdue: studentPayments.filter(sp => sp.paymentStatus === 'overdue').length
    };

    return {
      totalStudents,
      totalExpected,
      totalCollected,
      totalOutstanding,
      collectionRate,
      statusBreakdown
    };
  };

  const summaryData = generateSummaryReport();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Fee Collection Reports</h3>
        <select
          value={reportType}
          onChange={(e) => setReportType(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Select report type"
          aria-label="Select report type"
        >
          <option value="summary">Summary Report</option>
          <option value="detailed">Detailed Report</option>
          <option value="outstanding">Outstanding Payments</option>
        </select>
      </div>

      {reportType === 'summary' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Collection Summary</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Students:</span>
                <span className="font-medium">{summaryData.totalStudents}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expected Revenue:</span>
                <span className="font-medium">{formatCurrency(summaryData.totalExpected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collected:</span>
                <span className="font-medium text-green-600">{formatCurrency(summaryData.totalCollected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Outstanding:</span>
                <span className="font-medium text-red-600">{formatCurrency(summaryData.totalOutstanding)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collection Rate:</span>
                <span className="font-medium text-blue-600">{summaryData.collectionRate}%</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Payment Status Breakdown</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Completed:</span>
                <span className="font-medium text-green-600">{summaryData.statusBreakdown.completed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Partial:</span>
                <span className="font-medium text-blue-600">{summaryData.statusBreakdown.partial}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pending:</span>
                <span className="font-medium text-yellow-600">{summaryData.statusBreakdown.pending}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Overdue:</span>
                <span className="font-medium text-red-600">{summaryData.statusBreakdown.overdue}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {reportType === 'outstanding' && (
        <div className="bg-white rounded-xl overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outstanding Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Contact</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {studentPayments
                  .filter(sp => sp.amountRemaining > 0)
                  .sort((a, b) => b.amountRemaining - a.amountRemaining)
                  .map((payment) => (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{payment.studentName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.classLevel}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">{formatCurrency(payment.amountRemaining)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${PAYMENT_STATUS_COLORS[payment.paymentStatus].bg} ${PAYMENT_STATUS_COLORS[payment.paymentStatus].text}`}>
                          {payment.paymentStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>{payment.parentName}</div>
                          {payment.parentPhone && <div className="text-gray-500">{payment.parentPhone}</div>}
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
