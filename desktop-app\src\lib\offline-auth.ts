import { User } from './types';
import { auth } from './firebase';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';

// Types
export interface CachedSession {
  user: User;
  firebaseUser: {
    uid: string;
    email: string;
    displayName?: string;
  };
  timestamp: number;
  checksum: string;
  lastOnlineValidation: number;
}

export interface OfflineAuthConfig {
  maxOfflineDays: number;
  encryptionKey?: string;
}

// Simple encrypted storage for renderer process
class SecureStorage {
  private encryptionKey: string;

  constructor(encryptionKey: string) {
    this.encryptionKey = encryptionKey;
  }

  private encrypt(data: any): string {
    // Simple XOR encryption (for demo - use proper encryption in production)
    const jsonStr = JSON.stringify(data);
    let encrypted = '';
    for (let i = 0; i < jsonStr.length; i++) {
      encrypted += String.fromCharCode(
        jsonStr.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
      );
    }
    return btoa(encrypted);
  }

  private decrypt(encryptedData: string): any {
    try {
      const encrypted = atob(encryptedData);
      let decrypted = '';
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(
          encrypted.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
        );
      }
      return JSON.parse(decrypted);
    } catch (error) {
      return null;
    }
  }

  set(key: string, value: any): void {
    const encrypted = this.encrypt(value);
    localStorage.setItem(key, encrypted);
  }

  get(key: string): any {
    const encrypted = localStorage.getItem(key);
    if (!encrypted) return null;
    return this.decrypt(encrypted);
  }

  delete(key: string): void {
    localStorage.removeItem(key);
  }
}

// Offline Authentication Service
export class OfflineAuthService {
  private storage: SecureStorage;
  private config: OfflineAuthConfig;
  private readonly SESSION_KEY = 'cached_user_session';
  private readonly CHECKSUM_SALT = 'maggie_school_auth_salt_2025';

  constructor(config: OfflineAuthConfig = { maxOfflineDays: 7 }) {
    this.config = config;

    // Initialize secure storage
    this.storage = new SecureStorage(
      config.encryptionKey || 'maggie-school-offline-auth-key-2025'
    );
  }

  // Session Management
  async cacheUserSession(user: User, firebaseUser: FirebaseUser, password?: string): Promise<void> {
    try {
      const sessionData: CachedSession = {
        user,
        firebaseUser: {
          uid: firebaseUser.uid,
          email: firebaseUser.email || '',
          displayName: firebaseUser.displayName || undefined
        },
        timestamp: Date.now(),
        checksum: this.generateChecksum(user),
        lastOnlineValidation: Date.now()
      };

      this.storage.set(this.SESSION_KEY, sessionData);

      // Cache password hash for offline validation (if provided)
      if (password) {
        const passwordHash = this.hashPassword(password, user.email);
        this.storage.set(`${this.SESSION_KEY}_pwd`, passwordHash);
      }

      console.log('✅ User session cached for offline use');

    } catch (error) {
      console.error('❌ Failed to cache user session:', error);
      throw new Error('Failed to cache authentication session');
    }
  }

  async getCachedSession(): Promise<CachedSession | null> {
    try {
      const session = this.storage.get(this.SESSION_KEY) as CachedSession | undefined;
      
      if (!session) {
        return null;
      }

      // Validate session integrity
      if (!this.validateSessionIntegrity(session)) {
        console.warn('⚠️ Cached session integrity check failed');
        this.clearCachedSession();
        return null;
      }

      return session;
      
    } catch (error) {
      console.error('❌ Failed to retrieve cached session:', error);
      return null;
    }
  }

  async validateCachedSession(): Promise<boolean> {
    const session = await this.getCachedSession();
    
    if (!session) {
      return false;
    }

    // Check session age
    const maxOfflineTime = this.config.maxOfflineDays * 24 * 60 * 60 * 1000; // Convert to milliseconds
    const sessionAge = Date.now() - session.lastOnlineValidation;
    
    if (sessionAge > maxOfflineTime) {
      console.warn('⚠️ Cached session expired (too old)');
      this.clearCachedSession();
      return false;
    }

    // Validate checksum
    const expectedChecksum = this.generateChecksum(session.user);
    if (session.checksum !== expectedChecksum) {
      console.warn('⚠️ Cached session checksum mismatch');
      this.clearCachedSession();
      return false;
    }

    return true;
  }

  async updateLastOnlineValidation(): Promise<void> {
    const session = await this.getCachedSession();
    
    if (session) {
      session.lastOnlineValidation = Date.now();
      this.storage.set(this.SESSION_KEY, session);
      console.log('✅ Updated last online validation timestamp');
    }
  }

  clearCachedSession(): void {
    this.storage.delete(this.SESSION_KEY);
    console.log('🗑️ Cleared cached session');
  }

  // Authentication Methods
  async authenticateOffline(): Promise<User | null> {
    try {
      const isValid = await this.validateCachedSession();
      
      if (!isValid) {
        return null;
      }

      const session = await this.getCachedSession();
      
      if (session) {
        console.log('✅ Offline authentication successful');
        return session.user;
      }

      return null;
      
    } catch (error) {
      console.error('❌ Offline authentication failed:', error);
      return null;
    }
  }

  async canWorkOffline(): Promise<boolean> {
    return await this.validateCachedSession();
  }

  async getOfflineTimeRemaining(): Promise<number> {
    const session = await this.getCachedSession();
    
    if (!session) {
      return 0;
    }

    const maxOfflineTime = this.config.maxOfflineDays * 24 * 60 * 60 * 1000;
    const sessionAge = Date.now() - session.lastOnlineValidation;
    const timeRemaining = maxOfflineTime - sessionAge;
    
    return Math.max(0, timeRemaining);
  }

  async requiresOnlineAuth(): Promise<boolean> {
    const canWork = await this.canWorkOffline();
    return !canWork;
  }

  // Utility Methods
  private generateChecksum(user: User): string {
    // Create a checksum based on user data to detect tampering
    const userData = JSON.stringify({
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name
    });
    
    // Simple checksum (in production, use a proper hash function)
    let hash = 0;
    const combined = userData + this.CHECKSUM_SALT;
    
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(36);
  }

  private validateSessionIntegrity(session: CachedSession): boolean {
    try {
      // Check required fields
      if (!session.user || !session.firebaseUser || !session.checksum) {
        return false;
      }

      // Validate user object structure
      if (!session.user.id || !session.user.email || !session.user.role) {
        return false;
      }

      // Validate timestamps
      if (!session.timestamp || !session.lastOnlineValidation) {
        return false;
      }

      // Check if timestamps are reasonable (not in the future)
      const now = Date.now();
      if (session.timestamp > now || session.lastOnlineValidation > now) {
        return false;
      }

      return true;
      
    } catch (error) {
      return false;
    }
  }

  // Session Info
  async getSessionInfo(): Promise<{
    hasSession: boolean;
    isValid: boolean;
    timeRemaining: number;
    lastValidation: Date | null;
    user: User | null;
  }> {
    const session = await this.getCachedSession();
    const isValid = await this.validateCachedSession();
    const timeRemaining = await this.getOfflineTimeRemaining();

    return {
      hasSession: !!session,
      isValid,
      timeRemaining,
      lastValidation: session ? new Date(session.lastOnlineValidation) : null,
      user: session?.user || null
    };
  }

  // Configuration
  updateConfig(newConfig: Partial<OfflineAuthConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): OfflineAuthConfig {
    return { ...this.config };
  }

  // Password validation methods
  private hashPassword(password: string, email: string): string {
    // Simple password hashing with email as salt
    let hash = 0;
    const saltedPassword = password + email + this.CHECKSUM_SALT;
    for (let i = 0; i < saltedPassword.length; i++) {
      const char = saltedPassword.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private validatePassword(password: string, email: string): boolean {
    try {
      const storedHash = this.storage.get(`${this.SESSION_KEY}_pwd`);
      if (!storedHash) {
        return false; // No password hash stored
      }

      const inputHash = this.hashPassword(password, email);
      return storedHash === inputHash;
    } catch (error) {
      console.error('❌ Password validation error:', error);
      return false;
    }
  }

  // Authenticate with password validation
  async authenticateWithPassword(email: string, password: string): Promise<User | null> {
    try {
      const session = this.storage.get(this.SESSION_KEY);
      if (!session) {
        return null;
      }

      // Check if email matches
      if (session.user.email !== email) {
        return null;
      }

      // Validate password
      if (!this.validatePassword(password, email)) {
        throw new Error('Invalid password');
      }

      // Check if session is still valid
      if (!this.isSessionValid(session)) {
        return null;
      }

      console.log('✅ Offline authentication successful with password validation');
      return session.user;

    } catch (error) {
      console.error('❌ Offline authentication with password failed:', error);
      throw error;
    }
  }
}

// Singleton instance
let offlineAuthInstance: OfflineAuthService | null = null;

export function getOfflineAuthService(config?: OfflineAuthConfig): OfflineAuthService {
  if (!offlineAuthInstance) {
    offlineAuthInstance = new OfflineAuthService(config);
  }
  return offlineAuthInstance;
}

// Enhanced Auth Hook for Offline Support
export function useOfflineCapableAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [authMode, setAuthMode] = useState<'online' | 'offline' | 'checking'>('checking');

  const offlineAuth = getOfflineAuthService();

  useEffect(() => {
    let unsubscribeFirebase: (() => void) | null = null;

    const initializeAuth = async () => {
      try {
        if (isOnline) {
          // Online mode: Use Firebase Auth
          unsubscribeFirebase = onAuthStateChanged(auth, async (firebaseUser) => {
            if (firebaseUser && firebaseUser.email) {
              // Fetch full user data and cache it
              try {
                // You'll need to implement getUserData function
                // const userData = await getUserData(firebaseUser.email);
                // await offlineAuth.cacheUserSession(userData, firebaseUser);
                // setUser(userData);
                setAuthMode('online');
              } catch (error) {
                console.error('Failed to fetch user data:', error);
              }
            } else {
              setUser(null);
            }
            setIsLoading(false);
          });
        } else {
          // Offline mode: Use cached session
          const cachedUser = await offlineAuth.authenticateOffline();
          setUser(cachedUser);
          setAuthMode(cachedUser ? 'offline' : 'checking');
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Network status listeners
    const handleOnline = () => {
      setIsOnline(true);
      initializeAuth();
    };

    const handleOffline = () => {
      setIsOnline(false);
      initializeAuth();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      if (unsubscribeFirebase) {
        unsubscribeFirebase();
      }
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isOnline]);

  return {
    user,
    isLoading,
    isOnline,
    authMode,
    canWorkOffline: () => offlineAuth.canWorkOffline(),
    getOfflineTimeRemaining: () => offlineAuth.getOfflineTimeRemaining(),
    requiresOnlineAuth: () => offlineAuth.requiresOnlineAuth(),
    clearSession: () => offlineAuth.clearCachedSession()
  };
}

// Import React hooks
import { useState, useEffect } from 'react';
