import { User, Student, UserRole } from "./types";

// Standardized business logic functions

// Academic Year Management
const SCHOOL_START_YEAR = 2025; // The year the school management system starts

/**
 * Generate standardized academic year format starting from 2025-2026
 * Academic year runs from September to August of the following year
 */
export function getCurrentAcademicYear(): string {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed

  // Ensure we never go below the school start year
  let academicStartYear: number;

  if (currentMonth >= 9) {
    // September or later - current academic year starts this year
    academicStartYear = Math.max(currentYear, SCHOOL_START_YEAR);
  } else {
    // Before September - current academic year started last year
    academicStartYear = Math.max(currentYear - 1, SCHOOL_START_YEAR);
  }

  return `${academicStartYear}-${academicStartYear + 1}`;
}

/**
 * Generate a list of available academic years for dropdowns
 * Includes past, current, and future years
 */
export function getAvailableAcademicYears(): string[] {
  const currentAcademicYear = getCurrentAcademicYear();
  const [currentStartYear] = currentAcademicYear.split('-').map(Number);

  const years: string[] = [];

  // Add past years (from school start year to current)
  for (let year = SCHOOL_START_YEAR; year <= currentStartYear; year++) {
    years.push(`${year}-${year + 1}`);
  }

  // Add future years (next 2 years for planning)
  for (let year = currentStartYear + 1; year <= currentStartYear + 2; year++) {
    years.push(`${year}-${year + 1}`);
  }

  return years.reverse(); // Most recent first
}

/**
 * Get the next academic year
 */
export function getNextAcademicYear(): string {
  const currentAcademicYear = getCurrentAcademicYear();
  const [currentStartYear] = currentAcademicYear.split('-').map(Number);
  const nextStartYear = currentStartYear + 1;
  return `${nextStartYear}-${nextStartYear + 1}`;
}

/**
 * Get the previous academic year
 */
export function getPreviousAcademicYear(): string {
  const currentAcademicYear = getCurrentAcademicYear();
  const [currentStartYear] = currentAcademicYear.split('-').map(Number);
  const prevStartYear = Math.max(currentStartYear - 1, SCHOOL_START_YEAR);
  return `${prevStartYear}-${prevStartYear + 1}`;
}

/**
 * Check if a given academic year is the current academic year
 */
export function isCurrentAcademicYear(academicYear: string): boolean {
  return academicYear === getCurrentAcademicYear();
}

/**
 * Parse academic year string to get start and end years
 */
export function parseAcademicYear(academicYear: string): { startYear: number; endYear: number } {
  const [startYear, endYear] = academicYear.split('-').map(Number);
  return { startYear, endYear };
}

/**
 * Format academic year for display
 */
export function formatAcademicYear(academicYear: string): string {
  return academicYear; // Already in the correct format
}

/**
 * Get academic year for a specific date
 */
export function getAcademicYearForDate(date: Date): string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;

  let academicStartYear: number;

  if (month >= 9) {
    academicStartYear = Math.max(year, SCHOOL_START_YEAR);
  } else {
    academicStartYear = Math.max(year - 1, SCHOOL_START_YEAR);
  }

  return `${academicStartYear}-${academicStartYear + 1}`;
}

/**
 * Automatically detect and return the appropriate academic year for new registrations
 * This ensures that when registering students or teachers, they are assigned to the correct year
 */
export function getRegistrationAcademicYear(): string {
  return getCurrentAcademicYear();
}

/**
 * Check if an academic year exists in a list and add it if it doesn't
 * Useful for ensuring dropdown lists always include the current year
 */
export function ensureAcademicYearInList(years: string[], targetYear?: string): string[] {
  const yearToEnsure = targetYear || getCurrentAcademicYear();
  if (!years.includes(yearToEnsure)) {
    return [yearToEnsure, ...years].sort().reverse(); // Most recent first
  }
  return years;
}

/**
 * Generate default email for users
 */
export function generateDefaultEmail(name: string, role: UserRole): string {
  const cleanName = name.toLowerCase().replace(/\s+/g, '.');
  const domain = role === 'parent' ? 'parent.school.com' : 'school.com';
  return `${cleanName}@${domain}`;
}

/**
 * Generate default password for new users
 */
export function getDefaultPassword(): string {
  return "password";
}

/**
 * Validate academic year format
 */
export function isValidAcademicYear(academicYear: string): boolean {
  const yearPattern = /^\d{4}-\d{4}$/;
  if (!yearPattern.test(academicYear)) return false;
  
  const [startYear, endYear] = academicYear.split('-').map(Number);
  return endYear === startYear + 1;
}

/**
 * Create standardized user data with defaults
 */
export function createUserWithDefaults(userData: Partial<User>): Omit<User, 'id'> {
  const now = new Date().toISOString();
  
  return {
    name: userData.name || '',
    email: userData.email || '',
    role: userData.role || 'teacher',
    password: userData.password || getDefaultPassword(),
    avatarUrl: userData.avatarUrl || 'https://placehold.co/100x100.png',
    childrenIds: userData.childrenIds || [],
    assignedClasses: userData.assignedClasses || [],
    phone: userData.phone || '',
    address: userData.address || '',
    lastNotificationView: userData.lastNotificationView || now,
    fatherName: userData.fatherName || '',
    motherName: userData.motherName || '',
    fatherPhone: userData.fatherPhone || '',
    motherPhone: userData.motherPhone || '',
    ...userData,
    // Ensure these are always set
    createdAt: now,
    updatedAt: now,
  } as Omit<User, 'id'>;
}

/**
 * Create standardized student data with defaults
 */
export function createStudentWithDefaults(studentData: Partial<Student>): Omit<Student, 'id'> {
  const now = new Date().toISOString();
  
  return {
    name: studentData.name || '',
    dob: studentData.dob || '',
    gender: studentData.gender || 'male',
    class: studentData.class || '',
    parentId: studentData.parentId || '',
    avatarUrl: studentData.avatarUrl || 'https://placehold.co/100x100.png',
    results: studentData.results || [],
    dateAdded: studentData.dateAdded || now.split('T')[0],
    enrollmentStatus: studentData.enrollmentStatus || 'active',
    enrollmentDate: studentData.enrollmentDate || now.split('T')[0],
    academicYear: studentData.academicYear || getCurrentAcademicYear(),
    emergencyContact: studentData.emergencyContact || {
      name: '',
      relationship: '',
      phone: ''
    },
    medicalInfo: studentData.medicalInfo || '',
    address: studentData.address || '',
    previousSchool: studentData.previousSchool || '',
    ...studentData,
  } as Omit<Student, 'id'>;
}

/**
 * Standardized error messages
 */
export const ERROR_MESSAGES = {
  REGISTRATION_FAILED: 'Registration failed. Please try again.',
  UPDATE_FAILED: 'Update failed. Please try again.',
  DELETE_FAILED: 'Delete failed. Please try again.',
  INVALID_DATA: 'Invalid data provided.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  PERMISSION_DENIED: 'Permission denied.',
  USER_NOT_FOUND: 'User not found.',
  STUDENT_NOT_FOUND: 'Student not found.',
  INVALID_CREDENTIALS: 'Invalid email or password.',
  WEAK_PASSWORD: 'Password must be at least 6 characters.',
  EMAIL_REQUIRED: 'Email is required.',
  NAME_REQUIRED: 'Name is required.',
} as const;

/**
 * Standardized success messages
 */
export const SUCCESS_MESSAGES = {
  REGISTRATION_SUCCESS: 'Registration successful!',
  UPDATE_SUCCESS: 'Update successful!',
  DELETE_SUCCESS: 'Delete successful!',
  LOGIN_SUCCESS: 'Welcome back!',
  LOGOUT_SUCCESS: 'Logged out successfully.',
  NOTIFICATION_SENT: 'Notification sent successfully!',
  PAYMENT_RECORDED: 'Payment recorded successfully!',
} as const;

/**
 * Format date consistently across applications
 */
export function formatDate(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

/**
 * Format datetime consistently across applications
 */
export function formatDateTime(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleString('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Validate required fields for student registration
 */
export function validateStudentRegistration(data: Partial<Student>): string[] {
  const errors: string[] = [];
  
  if (!data.name?.trim()) errors.push('Student name is required');
  if (!data.dob) errors.push('Date of birth is required');
  if (!data.gender) errors.push('Gender is required');
  if (!data.class?.trim()) errors.push('Class is required');
  if (!data.parentId?.trim()) errors.push('Parent is required');
  if (!data.enrollmentDate) errors.push('Enrollment date is required');
  if (!data.academicYear?.trim()) errors.push('Academic year is required');
  
  if (data.academicYear && !isValidAcademicYear(data.academicYear)) {
    errors.push('Academic year must be in format YYYY-YYYY');
  }
  
  return errors;
}

/**
 * Validate required fields for user registration
 */
export function validateUserRegistration(data: Partial<User>): string[] {
  const errors: string[] = [];
  
  if (!data.name?.trim()) errors.push('Name is required');
  if (!data.email?.trim()) errors.push('Email is required');
  if (!data.role) errors.push('Role is required');
  
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Invalid email format');
  }
  
  return errors;
}
