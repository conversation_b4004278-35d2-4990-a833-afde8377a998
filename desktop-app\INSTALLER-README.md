# Maggie Preparatory School Management System - Installer

This document explains how to build and distribute the professional installer for the Maggie Preparatory School Management System.

## 🚀 Building the Installer

### Prerequisites
- Node.js 16+ installed
- Windows environment (for NSIS installer)
- All dependencies installed (`npm install`)

### Build Commands

1. **Build the installer:**
   ```bash
   npm run build:installer
   ```

2. **Alternative build method:**
   ```bash
   npm run build
   npm run electron:dist
   ```

### Output
The installer will be created in the `dist-installer/` directory:
- **File**: `Maggie-Preparatory-School-Setup-1.0.0.exe`
- **Size**: Approximately 150-200 MB
- **Type**: NSIS installer with custom setup screens

## 🎨 Installer Features

### Professional Setup Experience
- **Welcome Screen**: Branded introduction with school logo
- **License Agreement**: Educational software license terms
- **Component Selection**: Choose installation components
- **Installation Directory**: Customizable install location
- **Progress Screen**: Real-time installation progress with animations
- **Completion Screen**: Success confirmation with launch option

### Installation Components
1. **Core Application** (Required)
   - Main application files
   - Electron runtime
   - Application resources

2. **Desktop Shortcut** (Optional)
   - Creates desktop shortcut for easy access

3. **Start Menu Shortcuts** (Optional)
   - Application shortcut in Start Menu
   - Uninstaller shortcut

### Advanced Features
- **System Requirements Check**: Validates Windows version
- **Mutex Protection**: Prevents multiple installer instances
- **Registry Integration**: Proper Windows integration
- **Clean Uninstallation**: Complete removal of all components
- **Professional Branding**: School logo throughout installation
- **Multi-language Support**: English (extensible)

## 📁 Installer Structure

```
installer/
├── installer.nsh          # Custom NSIS script
├── license.txt           # End User License Agreement
└── README.txt           # Post-installation readme

dist-installer/
└── Maggie-Preparatory-School-Setup-1.0.0.exe
```

## 🔧 Customization

### Modifying Installation Screens
Edit `installer/installer.nsh` to customize:
- Welcome message text
- License agreement content
- Component descriptions
- Installation directory defaults
- Finish screen options

### Updating Branding
- Replace `public/logo.jpg` with new school logo
- Update school name in `package.json`
- Modify installer text in `installer.nsh`

### Adding Components
Add new installation components in the NSIS script:
```nsis
Section "New Component" SecNewComponent
  # Installation logic here
SectionEnd
```

## 📋 Installation Process

### User Experience Flow
1. **Download**: User downloads the .exe installer
2. **Launch**: Double-click to start installation
3. **Welcome**: Professional welcome screen with school branding
4. **License**: Review and accept license terms
5. **Components**: Select installation components
6. **Directory**: Choose installation location
7. **Install**: Watch progress with animations
8. **Complete**: Launch application or view readme

### System Integration
- **Registry Entries**: Proper Windows registration
- **File Associations**: Application file handling
- **Shortcuts**: Desktop and Start Menu integration
- **Uninstaller**: Clean removal capability

## 🛠️ Troubleshooting

### Common Build Issues

1. **NSIS Not Found**
   ```bash
   # Install NSIS manually or use electron-builder's bundled version
   npm install --save-dev electron-builder
   ```

2. **Icon Issues**
   ```bash
   # Ensure logo.jpg exists in public/ directory
   # Convert to .ico format if needed
   ```

3. **Build Failures**
   ```bash
   # Clean build directories
   rm -rf dist dist-electron dist-installer
   npm run build:installer
   ```

### Installer Issues

1. **Installation Fails**
   - Check Windows compatibility (Windows 7+)
   - Run as administrator if needed
   - Verify disk space availability

2. **Application Won't Launch**
   - Check antivirus software
   - Verify all files were installed
   - Run compatibility troubleshooter

## 📦 Distribution

### Recommended Distribution Methods
1. **School Website**: Host installer for download
2. **USB Drives**: For offline installation
3. **Network Share**: For IT department deployment
4. **Email**: For individual users (if size permits)

### Security Considerations
- **Code Signing**: Consider signing the installer for trust
- **Antivirus**: Test with major antivirus software
- **Checksums**: Provide SHA256 hashes for verification

## 🔄 Updates

### Version Management
- Update version in `package.json`
- Rebuild installer with new version
- Test upgrade process from previous versions

### Automatic Updates
The application includes update checking functionality:
- Checks for updates on startup
- Downloads and installs updates automatically
- Notifies users of available updates

## 📞 Support

For installer-related issues:
1. Check this documentation
2. Review build logs for errors
3. Test installation on clean Windows system
4. Contact IT administrator for deployment issues

---

**© 2024 Maggie Preparatory School. All rights reserved.**

This installer provides a professional installation experience that reflects the quality and professionalism of the Maggie Preparatory School Management System.
