export type UserRole = "admin" | "teacher" | "parent";

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  password?: string;
  avatarUrl?: string;
  childrenIds?: string[];
  assignedClasses?: string[];
  phone?: string;
  address?: string;
  lastNotificationView?: string;
  // Standardized parent fields
  fatherName?: string;
  motherName?: string;
  fatherPhone?: string;
  motherPhone?: string;
}

export enum Term {
    First = "First",
    Second = "Second",
    Third = "Third",
}
  
export interface Result {
  id: string;
  studentId: string;
  subject: string;
  grade: string;
  term: Term;
  year: string;
  fileUrl: string;
  date: string;
}

export enum EnrollmentStatus {
  Active = "active",
  OnHold = "on_hold",
  Inactive = "inactive",
  Completed = "completed"
}

export enum Gender {
  Male = "male",
  Female = "female",
  Other = "other"
}

export interface Student {
  id: string;
  name: string;
  dob: string;
  gender: Gender;
  class: string;
  parentId: string;
  avatarUrl: string;
  results: Result[];
  dateAdded: string;
  // Enhanced enrollment information
  enrollmentStatus: EnrollmentStatus;
  enrollmentDate: string;
  // Additional contact information
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  medicalInfo?: string;
  address?: string;
  // Academic information
  previousSchool?: string;
  academicYear?: string;
}

export type StudentFormData = Omit<Student, 'id' | 'avatarUrl' | 'results' | 'dateAdded'> & {
  photo?: FileList;
};


export interface Notification {
  id: string;
  message: string;
  createdAt: string;
  createdBy: string;
}
