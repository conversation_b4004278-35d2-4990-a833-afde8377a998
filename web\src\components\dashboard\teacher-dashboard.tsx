import Link from "next/link";
import { ArrowUpRight, Users, Bell, GraduationCap, BookOpen, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
  } from "@/components/ui/dialog";
import { useAuth } from "@/hooks/use-auth";
import { useEffect, useState } from "react";
import { collection, getDocs, query, where, orderBy, doc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Student, Notification } from "@/lib/types";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import { DollarSign, TrendingUp, Users, Award } from "lucide-react";

export default function TeacherDashboard() {
  const { user, refreshUser } = useAuth();
  const [assignedStudents, setAssignedStudents] = useState<Student[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [results, setResults] = useState<any[]>([]);
  const [studentPayments, setStudentPayments] = useState<any[]>([]);
  
  useEffect(() => {
    const fetchTeacherData = async () => {
        if (!user || user.role !== 'teacher') return;

        // Fetch assigned students
        if (user.assignedClasses && user.assignedClasses.length > 0) {
            const studentsCollection = collection(db, 'students');
            const studentsQuery = query(studentsCollection, where('class', 'in', user.assignedClasses));
            const studentSnapshot = await getDocs(studentsQuery);

            const studentsData = studentSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Student));
            setAssignedStudents(studentsData);

            // Fetch results for analytics
            const studentIds = studentsData.map(s => s.id);
            if (studentIds.length > 0) {
                const resultsQuery = query(
                    collection(db, 'results'),
                    where('studentId', 'in', studentIds.slice(0, 10)) // Firestore limit
                );
                const resultsSnapshot = await getDocs(resultsQuery);
                const resultsData = resultsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                setResults(resultsData);

                // Fetch payment data for fee analytics
                const paymentsQuery = query(
                    collection(db, 'studentPayments'),
                    where('studentId', 'in', studentIds.slice(0, 10))
                );
                const paymentsSnapshot = await getDocs(paymentsQuery);
                const paymentsData = paymentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                setStudentPayments(paymentsData);
            }
        }

        // Fetch notifications
        const notificationsQuery = query(collection(db, 'notifications'), orderBy('createdAt', 'desc'));
        const notificationsSnapshot = await getDocs(notificationsQuery);
        const allNotifications = notificationsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Notification));
        setNotifications(allNotifications);
        
        // Calculate unread count
        const lastView = user.lastNotificationView ? new Date(user.lastNotificationView) : new Date(0);
        const newUnreadCount = allNotifications.filter(n => new Date(n.createdAt) > lastView).length;
        setUnreadCount(newUnreadCount);
    };

    fetchTeacherData();
  }, [user]);

  const handleOpenNotifications = async () => {
    if (unreadCount > 0 && user) {
        const userRef = doc(db, 'users', user.id);
        await updateDoc(userRef, {
            lastNotificationView: new Date().toISOString()
        });
        setUnreadCount(0); // Immediately update UI
        await refreshUser(); // Fetch updated user data in the background
    }
  };

  const totalAssignedStudents = assignedStudents.length;
  const myClassesCount = user?.assignedClasses?.length || 0;

  // Calculate analytics metrics
  const getAnalyticsMetrics = () => {
    // Academic performance metrics
    const studentIds = assignedStudents.map(s => s.id);
    const classResults = results.filter(r => studentIds.includes(r.studentId));
    const averagePerformance = classResults.length > 0
      ? Math.round(classResults.reduce((sum, r) => sum + (r.percentage || 0), 0) / classResults.length)
      : 0;

    // Fee collection metrics
    const classPayments = studentPayments.filter(p => studentIds.includes(p.studentId));
    const totalExpected = classPayments.reduce((sum, p) => sum + (p.totalAmountDue || 0), 0);
    const totalCollected = classPayments.reduce((sum, p) => sum + (p.totalAmountPaid || 0), 0);
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    return {
      averagePerformance,
      totalResults: classResults.length,
      totalExpected,
      totalCollected,
      collectionRate
    };
  };

  const analytics = getAnalyticsMetrics();

  const statsCards = [
    {
      title: "My Students",
      value: totalAssignedStudents,
      description: "Students in my classes",
      icon: GraduationCap,
      gradient: "gradient-primary",
      color: "text-primary"
    },
    {
      title: "Class Performance",
      value: `${analytics.averagePerformance}%`,
      description: `Based on ${analytics.totalResults} results`,
      icon: Award,
      gradient: "gradient-success",
      color: "text-success"
    },
    {
      title: "Fee Collection",
      value: `${analytics.collectionRate}%`,
      description: `₵${analytics.totalCollected.toLocaleString()} collected`,
      icon: DollarSign,
      gradient: "gradient-warning",
      color: "text-warning"
    },
    {
      title: "My Classes",
      value: `${myClassesCount} Classes`,
      description: user?.assignedClasses?.join(', ') || "No classes assigned",
      icon: BookOpen,
      gradient: "gradient-info",
      color: "text-info"
    },
    {
      title: "Notifications",
      value: unreadCount,
      description: "Click to view messages from admin",
      icon: Bell,
      gradient: "gradient-secondary",
      color: "text-secondary",
      isNotification: true
    }
  ];
  
  return (
    <div className="flex flex-1 flex-col gap-6 animate-fade-in">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-muted-foreground mt-1">
            Here's an overview of your teaching dashboard.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-success/10 text-success border-success/20">
            <TrendingUp className="w-3 h-3 mr-1" />
            Active Teacher
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        {statsCards.map((card, index) => (
          <Dialog key={index}>
            <DialogTrigger asChild>
              <Card className={`stats-card ${card.gradient} group hover:scale-105 transition-all duration-300 cursor-pointer`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground">
                    {card.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg bg-background/50 ${card.color}`}>
                    <card.icon className="h-4 w-4" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground flex items-center gap-2">
                    {card.value}
                    {card.isNotification && unreadCount > 0 && (
                      <Badge variant="destructive" className="text-xs">New</Badge>
                    )}
                    </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    {card.description}
                    </p>
                </CardContent>
                </Card>
            </DialogTrigger>
            {card.isNotification && (
              <DialogContent className="bg-card/95 backdrop-blur-xl border-border/50">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold">Notifications from Admin</DialogTitle>
                    <DialogDescription>Here are the latest messages for all teachers.</DialogDescription>
                </DialogHeader>
                <ScrollArea className="h-72 w-full">
                    {notifications.length > 0 ? (
                        <div className="space-y-4 pr-4">
                            {notifications.map(n => (
                        <div key={n.id} className="p-4 rounded-lg border border-border/30 bg-muted/30 hover:bg-muted/50 transition-colors">
                          <p className="text-sm text-foreground">{n.message}</p>
                                    <p className="text-xs text-muted-foreground mt-2">
                                        Sent by {n.createdBy} on {new Date(n.createdAt).toLocaleDateString()}
                                    </p>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-sm text-muted-foreground text-center py-10">No new notifications.</p>
                    )}
                </ScrollArea>
            </DialogContent>
            )}
        </Dialog>
        ))}
      </div>

      {/* Analytics Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Academic Performance Analytics */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Award className="w-5 h-5 text-success" />
              Academic Performance
            </CardTitle>
            <CardDescription>
              Performance overview for your classes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-foreground">Average Performance</p>
                  <p className="text-xs text-muted-foreground">Across all subjects</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-success">{analytics.averagePerformance}%</p>
                  <p className="text-xs text-muted-foreground">{analytics.totalResults} results</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-primary/10 rounded-lg">
                  <p className="text-lg font-bold text-primary">{assignedStudents.filter(s => s.enrollmentStatus === 'active').length}</p>
                  <p className="text-xs text-muted-foreground">Active Students</p>
                </div>
                <div className="text-center p-3 bg-success/10 rounded-lg">
                  <p className="text-lg font-bold text-success">{user?.assignedClasses?.length || 0}</p>
                  <p className="text-xs text-muted-foreground">Classes Teaching</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fee Collection Analytics */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-warning" />
              Fee Collection Overview
            </CardTitle>
            <CardDescription>
              Payment status for your students
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-foreground">Collection Rate</p>
                  <p className="text-xs text-muted-foreground">Overall payment progress</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-warning">{analytics.collectionRate}%</p>
                  <p className="text-xs text-muted-foreground">of expected amount</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-success/10 rounded-lg">
                  <p className="text-lg font-bold text-success">₵{analytics.totalCollected.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">Collected</p>
                </div>
                <div className="text-center p-3 bg-muted/30 rounded-lg">
                  <p className="text-lg font-bold text-muted-foreground">₵{analytics.totalExpected.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">Expected</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Academic Performance Analytics */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Award className="w-5 h-5 text-success" />
              Academic Performance
            </CardTitle>
            <CardDescription>
              Performance overview for your classes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-foreground">Average Performance</p>
                  <p className="text-xs text-muted-foreground">Across all subjects</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-success">{analytics.averagePerformance}%</p>
                  <p className="text-xs text-muted-foreground">{analytics.totalResults} results</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-primary/10 rounded-lg">
                  <p className="text-lg font-bold text-primary">{assignedStudents.filter(s => s.enrollmentStatus === 'active').length}</p>
                  <p className="text-xs text-muted-foreground">Active Students</p>
                </div>
                <div className="text-center p-3 bg-success/10 rounded-lg">
                  <p className="text-lg font-bold text-success">{user?.assignedClasses?.length || 0}</p>
                  <p className="text-xs text-muted-foreground">Classes Teaching</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fee Collection Analytics */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-warning" />
              Fee Collection Overview
            </CardTitle>
            <CardDescription>
              Payment status for your students
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-foreground">Collection Rate</p>
                  <p className="text-xs text-muted-foreground">Overall payment progress</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-warning">{analytics.collectionRate}%</p>
                  <p className="text-xs text-muted-foreground">of expected amount</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-success/10 rounded-lg">
                  <p className="text-lg font-bold text-success">₵{analytics.totalCollected.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">Collected</p>
                </div>
                <div className="text-center p-3 bg-muted/30 rounded-lg">
                  <p className="text-lg font-bold text-muted-foreground">₵{analytics.totalExpected.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">Expected</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Analytics & Reports</CardTitle>
            <CardDescription>
              View detailed analytics and performance reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full btn-modern">
              <Link href="/analytics">
                <TrendingUp className="w-4 h-4 mr-2" />
                View Full Analytics
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Academic Fees</CardTitle>
            <CardDescription>
              Manage and track student fee payments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full btn-modern" variant="outline">
              <Link href="/teacher-academic-fees">
                <DollarSign className="w-4 h-4 mr-2" />
                Manage Fees
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Student Roster */}
      <Card className="card-modern">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-xl font-semibold">Student Roster</CardTitle>
                    <CardDescription>
                    Quick access to your student records.
                    </CardDescription>
                </div>
          <Button asChild size="sm" className="btn-modern" variant="outline">
                    <Link href="/students">
                    View All My Students
              <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Link>
                </Button>
                </CardHeader>
                <CardContent>
          <div className="overflow-hidden rounded-lg border border-border/50">
                    <Table>
                        <TableHeader>
                <TableRow className="bg-muted/30">
                  <TableHead className="font-semibold">Student</TableHead>
                  <TableHead className="font-semibold">Class</TableHead>
                        </TableRow>
                        </TableHeader>
                        <TableBody>
                        {assignedStudents.slice(0, 5).map((student) => (
                  <TableRow key={student.id} className="hover:bg-muted/30 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center text-white text-xs font-semibold">
                          {student.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="font-medium text-foreground">{student.name}</div>
                      </div>
                    </TableCell>
                            <TableCell>
                      <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                        {student.class}
                      </Badge>
                            </TableCell>
                            </TableRow>
                        ))}
                        {assignedStudents.length === 0 && (
                            <TableRow>
                    <TableCell colSpan={2} className="text-center h-24 text-muted-foreground">
                                    No students assigned to your classes.
                                </TableCell>
                            </TableRow>
                        )}
                        </TableBody>
                    </Table>
                </div>
                </CardContent>
            </Card>
    </div>
  );
}
