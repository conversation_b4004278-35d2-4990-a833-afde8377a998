"use client";
import React, { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { MainNav } from "@/components/main-nav";
import { UserNav } from "@/components/user-nav";
import { Button } from "@/components/ui/button";
import { Menu, Search, ChevronLeft, ChevronRight } from "lucide-react";
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from "@/lib/firebase";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearch = async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      let searchQuery;
      switch (user?.role) {
        case 'admin':
          // Search across students, teachers, and users
          searchQuery = query(
            collection(db, 'students'), 
            where('name', '>=', term.toLowerCase()),
            where('name', '<=', term.toLowerCase() + '\uf8ff')
          );
          break;
        case 'teacher':
          // Search only in assigned classes
          searchQuery = query(
            collection(db, 'students'), 
            where('class', 'in', user.assignedClasses || []),
            where('name', '>=', term.toLowerCase()),
            where('name', '<=', term.toLowerCase() + '\uf8ff')
          );
          break;
        case 'parent':
          // Search only their children
          searchQuery = query(
            collection(db, 'students'), 
            where('parentId', '==', user.id)
          );
          break;
        default:
          setSearchResults([]);
          return;
      }

      const snapshot = await getDocs(searchQuery);
      const results = snapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      }));
      setSearchResults(results);
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
    }
  };

  React.useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isLoading, isAuthenticated, router]);

  // Close mobile menu when pathname changes (navigation occurs)
  React.useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  // Load sidebar state from localStorage on mount
  React.useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed');
    if (savedState !== null) {
      setIsSidebarCollapsed(JSON.parse(savedState));
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  React.useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  if (isLoading || !isAuthenticated || !user) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gradient-to-br from-primary/5 via-background to-primary/5">
        <div className="relative">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <div className="absolute inset-0 h-12 w-12 animate-ping rounded-full border-2 border-primary/30" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full max-w-full overflow-x-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Modern Sidebar - Fixed */}
      <div className={`hidden md:block fixed left-0 top-0 h-screen border-r border-border/50 bg-card/30 backdrop-blur-xl z-40 transition-all duration-300 ease-in-out ${
        isSidebarCollapsed ? 'w-[80px] sidebar-collapsed' : 'w-[280px] lg:w-[320px]'
      }`}>
        <div className="flex h-full flex-col overflow-hidden">
          {/* Logo Section - Fixed at top */}
          <div className={`flex h-16 shrink-0 items-center border-b border-border/50 bg-card/50 ${
            isSidebarCollapsed ? 'justify-center px-2' : 'justify-between px-6'
          }`}>
            <a href="/dashboard" className="flex items-center gap-3 group">
              <div className="relative">
                <Image src="/logo.jpg" alt="Maggie Prep Logo" width={32} height={32} className="w-8 h-8 rounded-full" />
                <div className="absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-xl group-hover:bg-primary/30 transition-all duration-200" />
              </div>
              {!isSidebarCollapsed && (
                <div className="flex flex-col min-w-0">
                  <span className="font-headline text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent truncate">
                    Maggie Prep
                  </span>
                  <span className="text-xs text-muted-foreground truncate">School Management</span>
                </div>
              )}
            </a>
            
            {/* Toggle Button - Only show when expanded */}
            {!isSidebarCollapsed && (
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsSidebarCollapsed(!isSidebarCollapsed);
                }}
                className="h-8 w-8 hover:bg-muted/50 transition-all duration-200"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          {/* Toggle Button for collapsed state - positioned separately */}
          {isSidebarCollapsed && (
            <div className="flex justify-center py-2 border-b border-border/50 bg-card/30">
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsSidebarCollapsed(!isSidebarCollapsed);
                }}
                className="h-8 w-8 hover:bg-muted/50 transition-all duration-200"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
          
          {/* Navigation - Scrollable area */}
          <div className="flex-1 overflow-y-auto px-4 py-6 scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
            <MainNav userRole={user.role} isCollapsed={isSidebarCollapsed} />
          </div>
          
          {/* User Info - Fixed at bottom */}
          <div className="shrink-0 border-t border-border/50 p-4 bg-card/30">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center text-white font-semibold shrink-0">
                {user.name.split(' ').map(n => n[0]).join('')}
              </div>
              {!isSidebarCollapsed && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">{user.name}</p>
                  <p className="text-xs text-muted-foreground capitalize truncate">{user.role}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Main Content - Full width with offset */}
      <div className={`flex flex-col min-w-0 w-full max-w-full overflow-x-hidden transition-all duration-300 ease-in-out ${
        isSidebarCollapsed ? 'md:pl-[80px]' : 'md:pl-[280px] lg:pl-[320px]'
      }`}>
        {/* Modern Header */}
        <header className="sticky top-0 z-50 flex h-14 lg:h-16 items-center justify-between border-b border-border/50 bg-card/50 backdrop-blur-xl px-3 sm:px-4 lg:px-6 min-w-0">
          {/* Left Section - Mobile Menu */}
          <div className="flex items-center gap-3 lg:gap-4 min-w-0">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                  variant="ghost"
                size="icon"
                  className="shrink-0 md:hidden hover:bg-muted/50 h-9 w-9 lg:h-10 lg:w-10"
              >
                <Menu className="h-4 w-4 lg:h-5 lg:w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
              <SheetContent side="left" className="flex flex-col bg-card/95 backdrop-blur-xl w-[280px] max-w-[90vw]">
                <SheetTitle className="flex items-center gap-3 mb-6">
                  <Image src="/logo.jpg" alt="Maggie Prep Logo" width={32} height={32} className="w-8 h-8 rounded-full" />
                  <span className="font-headline text-xl font-bold">Maggie Prep</span>
                </SheetTitle>
              <MainNav userRole={user.role} />
                <div className="sr-only">Navigation Menu</div>
            </SheetContent>
          </Sheet>
          </div>
          
          {/* Center Section - Search Bar */}
          <div className="hidden md:flex flex-1 justify-center max-w-md min-w-0">
            {user.role !== 'parent' && (
              <div className="relative w-full">
                <Input
                  type="search"
                  placeholder="Search students, teachers..."
                  value={searchTerm}
                  onChange={(e) => {
                    const term = e.target.value;
                    setSearchTerm(term);
                    handleSearch(term);
                  }}
                  className="w-full pl-10 bg-background/50 border-border/50 focus:bg-background transition-all duration-200 text-sm"
                />
                {searchResults.length > 0 && (
                  <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {searchResults.map((result) => (
                      <div 
                        key={result.id} 
                        className="px-4 py-2 hover:bg-muted/50 cursor-pointer"
                        onClick={() => {
                          // Navigate to student details or perform action based on role
                          switch(user?.role) {
                            case 'admin':
                              router.push(`/students/${result.id}`);
                              break;
                            case 'teacher':
                              router.push(`/students/${result.id}`);
                              break;
                            case 'parent':
                              router.push(`/my-children/${result.id}`);
                              break;
                          }
                          setSearchResults([]);
                          setSearchTerm("");
                        }}
                      >
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                            {result.name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <p className="text-sm font-medium">{result.name}</p>
                            <p className="text-xs text-muted-foreground">{result.class || 'No Class'}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Right Section - User Menu */}
          <div className="flex items-center gap-3 lg:gap-4 min-w-0">
            {/* Mobile Search (shown only on mobile) */}
            <div className="md:hidden flex-1 max-w-xs min-w-0">
              {user.role !== 'parent' && (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => {
                      const term = e.target.value;
                      setSearchTerm(term);
                      handleSearch(term);
                    }}
                    className="w-full pl-10 bg-background/50 border-border/50 focus:bg-background transition-all duration-200 text-sm"
                  />
                  {searchResults.length > 0 && (
                    <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {searchResults.map((result) => (
                        <div 
                          key={result.id} 
                          className="px-4 py-2 hover:bg-muted/50 cursor-pointer"
                          onClick={() => {
                            // Navigate to student details or perform action based on role
                            switch(user?.role) {
                              case 'admin':
                                router.push(`/students/${result.id}`);
                                break;
                              case 'teacher':
                                router.push(`/students/${result.id}`);
                                break;
                              case 'parent':
                                router.push(`/my-children/${result.id}`);
                                break;
                            }
                            setSearchResults([]);
                            setSearchTerm("");
                          }}
                        >
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                              {result.name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <p className="text-sm font-medium">{result.name}</p>
                              <p className="text-xs text-muted-foreground">{result.class || 'No Class'}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
             )}
          </div>
          <UserNav user={user} />
          </div>
        </header>
        
        {/* Main Content Area */}
        <main className="flex flex-1 flex-col gap-3 sm:gap-4 lg:gap-6 p-3 sm:p-4 lg:p-6 min-w-0 max-w-full overflow-x-hidden">
          <div className="animate-fade-in w-full max-w-full">
          {children}
          </div>
        </main>
      </div>
    </div>
  );
}
