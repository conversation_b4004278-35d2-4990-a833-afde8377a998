import { useState, useEffect } from 'react';
import { useAuth } from '../hooks/use-auth';
import { useDocumentTitle } from '../hooks/use-document-title';
import {
  FeeStructure,
  StudentPayment,
  ClassLevel,
  PaymentMethod,
  PaymentStatus,
  CLASS_LEVEL_GROUPS,
  DEFAULT_FEE_STRUCTURE,
  FEE_TYPE_LABELS,
  PAYMENT_STATUS_COLORS
} from '../lib/academic-fees-types';
import {
  calculateTotalFees,
  determinePaymentStatus,
  generateReceiptNumber,
  formatCurrency,
  getCurrentAcademicYear,
  getAvailableAcademicYears,
  filterStudentPayments
} from '../lib/academic-fees-utils';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  getDocs
} from 'firebase/firestore';
import { db } from '../lib/firebase';

export default function AcademicFees() {
  const { user } = useAuth();
  const [students, setStudents] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'fee-structure' | 'payments' | 'reports'>('overview');
  const [feeStructures, setFeeStructures] = useState<FeeStructure[]>([]);
  const [studentPayments, setStudentPayments] = useState<StudentPayment[]>([]);
  const [paymentRecords, setPaymentRecords] = useState<any[]>([]);

  const [selectedAcademicYear, setSelectedAcademicYear] = useState(getCurrentAcademicYear());

  // Modal states
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [editingStructure, setEditingStructure] = useState<FeeStructure | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<StudentPayment | null>(null);
  const [saving, setSaving] = useState(false);

  // Form data states
  const [formData, setFormData] = useState({
    classLevel: '' as ClassLevel,
    fees: { ...DEFAULT_FEE_STRUCTURE },
    currency: 'GHS' as const,
    paymentFrequency: 'yearly' as 'yearly' | 'semester' | 'term',
    termNumber: 1 as 1 | 2 | 3, // For term payments
    semesterNumber: 1 as 1 | 2   // For semester payments
  });

  const [paymentData, setPaymentData] = useState({
    amount: 0,
    paymentMethod: 'cash' as PaymentMethod,
    description: ''
  });

  useDocumentTitle('Academic Fees Management');

  // Real-time listener for students (for instant UI updates)
  useEffect(() => {
    if (!user) return;

    let studentsQuery;
    const studentsCollection = collection(db, 'students');

    if (user.role === 'admin') {
      studentsQuery = query(studentsCollection, orderBy('dateAdded', 'desc'));
    } else if (user.role === 'teacher' && user.assignedClasses && user.assignedClasses.length > 0) {
      studentsQuery = query(
        studentsCollection,
        where('class', 'in', user.assignedClasses),
        orderBy('name')
      );
    } else {
      setStudents([]);
      return;
    }

    // Use real-time listener for instant updates
    const unsubscribe = onSnapshot(studentsQuery, (snapshot) => {
      const studentsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setStudents(studentsData);
    }, (error) => {
      console.error('❌ Error in students listener:', error);
    });

    return unsubscribe;
  }, [user]);

  // Real-time listeners for fee data (for instant UI updates)
  useEffect(() => {
    if (!user || user.role !== 'admin') return;

    const unsubscribes: (() => void)[] = [];

    try {
      // Fee structures listener
      const feeStructuresQuery = query(
        collection(db, 'feeStructures'),
        where('academicYear', '==', selectedAcademicYear),
        orderBy('classLevel')
      );
      const unsubscribeFeeStructures = onSnapshot(feeStructuresQuery, (snapshot) => {
        const structures = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as FeeStructure[];
        setFeeStructures(structures);
      });
      unsubscribes.push(unsubscribeFeeStructures);

      // Student payments listener
      const paymentsQuery = query(
        collection(db, 'studentPayments'),
        where('academicYear', '==', selectedAcademicYear),
        orderBy('studentName')
      );
      const unsubscribePayments = onSnapshot(paymentsQuery, (snapshot) => {
        const payments = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as StudentPayment[];
        setStudentPayments(payments);
      });
      unsubscribes.push(unsubscribePayments);

      // Payment records listener
      const paymentRecordsQuery = query(
        collection(db, 'payments'),
        where('academicYear', '==', selectedAcademicYear),
        orderBy('paymentDate', 'desc')
      );
      const unsubscribePaymentRecords = onSnapshot(paymentRecordsQuery, (snapshot) => {
        const records = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setPaymentRecords(records);
      });
      unsubscribes.push(unsubscribePaymentRecords);

      console.log('✅ Real-time fee data listeners set up successfully');
    } catch (error) {
      console.error('❌ Error setting up fee data listeners:', error);
    }

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [user, selectedAcademicYear]);

  // Removed loading timeout since we don't use loading states anymore

  // Auto-create student payment records when data is ready
  useEffect(() => {
    // Only run if we have students and fee structures
    if (students.length > 0 && feeStructures.length > 0) {
      // Check if we need to create payment records
      const studentsWithFeeStructures = students.filter(student =>
        feeStructures.some(fs => fs.classLevel === student.class)
      );

      // If we have eligible students but no payment records, create them
      if (studentsWithFeeStructures.length > 0 && studentPayments.length === 0) {
        setTimeout(() => createStudentPaymentRecords(), 1000); // Brief delay to ensure data is settled
      }
    }
  }, [students.length, feeStructures.length, studentPayments.length]);

  // Handler functions
  const handleSave = async () => {
    if (!formData.classLevel) return;

    setSaving(true);
    try {
      const feeStructureData = {
        classLevel: formData.classLevel,
        fees: formData.fees,
        currency: formData.currency,
        paymentFrequency: formData.paymentFrequency,
        ...(formData.paymentFrequency === 'term' && { termNumber: formData.termNumber }),
        ...(formData.paymentFrequency === 'semester' && { semesterNumber: formData.semesterNumber }),
        academicYear: selectedAcademicYear,
        totalAmount: calculateTotalFees({ fees: formData.fees } as FeeStructure),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (editingStructure) {
        await updateDoc(doc(db, 'feeStructures', editingStructure.id), {
          ...feeStructureData,
          updatedAt: new Date().toISOString()
        });
      } else {
        await addDoc(collection(db, 'feeStructures'), feeStructureData);
      }

      setShowCreateForm(false);
      setEditingStructure(null);
      setFormData({
        classLevel: '' as ClassLevel,
        fees: { ...DEFAULT_FEE_STRUCTURE },
        currency: 'GHS',
        paymentFrequency: 'yearly',
        termNumber: 1,
        semesterNumber: 1
      });
    } catch (error) {
      console.error('Error saving fee structure:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleRecordPayment = async () => {
    if (!selectedStudent || paymentData.amount <= 0) return;

    try {
      setSaving(true);

      // Create the payment record for the payments collection
      const paymentRecord = {
        studentId: selectedStudent.studentId,
        studentName: selectedStudent.studentName,
        classLevel: selectedStudent.classLevel,
        amount: paymentData.amount,
        paymentMethod: paymentData.paymentMethod,
        description: paymentData.description,
        academicYear: selectedAcademicYear,
        receiptNumber: generateReceiptNumber(),
        paymentDate: new Date().toISOString(),
        recordedBy: user?.name || 'Admin'
      };

      // Add to payments collection
      await addDoc(collection(db, 'payments'), paymentRecord);

      // Update the StudentPayment record to reflect the new payment
      const studentPaymentDoc = studentPayments.find(sp => sp.studentId === selectedStudent.studentId);

      if (studentPaymentDoc) {
        // Create the individual payment record
        const individualPayment = {
          id: paymentRecord.receiptNumber,
          amount: paymentData.amount,
          paymentMethod: paymentData.paymentMethod,
          description: paymentData.description,
          paymentDate: paymentRecord.paymentDate,
          receiptNumber: paymentRecord.receiptNumber,
          recordedBy: paymentRecord.recordedBy
        };

        // Calculate new totals
        const newTotalPaid = studentPaymentDoc.totalAmountPaid + paymentData.amount;
        const newAmountRemaining = Math.max(0, studentPaymentDoc.totalAmountDue - newTotalPaid);
        const newPaymentStatus = determinePaymentStatus(studentPaymentDoc.totalAmountDue, newTotalPaid);

        // Update the StudentPayment document
        await updateDoc(doc(db, 'studentPayments', studentPaymentDoc.id), {
          payments: [...(studentPaymentDoc.payments || []), individualPayment],
          totalAmountPaid: newTotalPaid,
          amountRemaining: newAmountRemaining,
          paymentStatus: newPaymentStatus,
          updatedAt: new Date().toISOString()
        });

        console.log(`✅ Payment recorded: ₵${paymentData.amount} for ${selectedStudent.studentName}`);
        console.log(`💰 New totals - Paid: ₵${newTotalPaid}, Remaining: ₵${newAmountRemaining}, Status: ${newPaymentStatus}`);
      } else {
        console.warn('⚠️ StudentPayment record not found for student:', selectedStudent.studentName);
      }

      // Reset form and close modal
      setShowPaymentForm(false);
      setSelectedStudent(null);
      setPaymentData({
        amount: 0,
        paymentMethod: 'cash',
        description: ''
      });

    } catch (error) {
      console.error('❌ Error recording payment:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (structure: FeeStructure) => {
    setEditingStructure(structure);
    setFormData({
      classLevel: structure.classLevel,
      fees: structure.fees,
      currency: (structure.currency || 'GHS') as 'GHS',
      paymentFrequency: (structure as any).paymentFrequency || 'yearly',
      termNumber: (structure as any).termNumber || 1,
      semesterNumber: (structure as any).semesterNumber || 1
    });
    setShowCreateForm(true);
  };

  // Calculate real expected revenue based on students and fee structures
  const calculateExpectedRevenue = () => {
    let totalExpected = 0;
    let studentsWithFeeStructures = 0;
    let studentsWithoutFeeStructures = 0;

    // Group students by class
    const studentsByClass = students.reduce((acc, student) => {
      const className = student.class;
      if (!acc[className]) {
        acc[className] = 0;
      }
      acc[className]++;
      return acc;
    }, {} as Record<string, number>);

    // Calculate expected revenue: students per class × fee structure amount
    Object.entries(studentsByClass).forEach(([className, studentCount]) => {
      const feeStructure = feeStructures.find(fs => fs.classLevel === className);

      if (feeStructure) {
        // Only count students who have a fee structure
        const feePerStudent = calculateTotalFees(feeStructure);
        totalExpected += feePerStudent * studentCount;
        studentsWithFeeStructures += studentCount;
      } else {
        // Don't count students without fee structures
        studentsWithoutFeeStructures += studentCount;
      }
    });

    console.log('📊 Expected Revenue Calculation:', {
      totalExpected,
      studentsWithFeeStructures,
      studentsWithoutFeeStructures,
      totalStudents: students.length,
      breakdown: Object.entries(studentsByClass).map(([className, count]) => {
        const feeStructure = feeStructures.find(fs => fs.classLevel === className);
        return {
          class: className,
          studentCount: count,
          hasFeeStructure: !!feeStructure,
          feePerStudent: feeStructure ? calculateTotalFees(feeStructure) : 0,
          expectedFromClass: feeStructure ? calculateTotalFees(feeStructure) * count : 0
        };
      })
    });

    return totalExpected;
  };

  // Create StudentPayment records for all eligible students
  const createStudentPaymentRecords = async () => {
    console.log('🔄 Creating student payment records...');

    try {
      // Get existing student payment records
      const existingPaymentsQuery = query(
        collection(db, 'studentPayments'),
        where('academicYear', '==', selectedAcademicYear)
      );
      const existingPaymentsSnapshot = await getDocs(existingPaymentsQuery);
      const existingStudentIds = new Set(
        existingPaymentsSnapshot.docs.map(doc => doc.data().studentId)
      );

      let createdCount = 0;

      // Create payment records for students with fee structures
      for (const student of students) {
        // Skip if student already has a payment record
        if (existingStudentIds.has(student.id)) continue;

        // Find fee structure for student's class
        const feeStructure = feeStructures.find(fs => fs.classLevel === student.class);

        // Only create record if fee structure exists
        if (!feeStructure) {
          console.log(`⚠️ Skipping ${student.name} - no fee structure for class ${student.class}`);
          continue;
        }

        // Get parent information
        let parentData = null;
        try {
          const parentDoc = await getDocs(query(
            collection(db, 'users'),
            where('__name__', '==', student.parentId)
          ));
          parentData = parentDoc.docs[0]?.data();
        } catch (error) {
          console.warn(`Could not fetch parent data for student ${student.name}`);
        }

        const totalAmountDue = calculateTotalFees(feeStructure);

        const studentPaymentRecord: Omit<StudentPayment, 'id'> = {
          studentId: student.id,
          studentName: student.name,
          classLevel: student.class as ClassLevel,
          academicYear: selectedAcademicYear,
          feeStructureId: feeStructure.id,
          payments: [],
          totalAmountDue,
          totalAmountPaid: 0,
          amountRemaining: totalAmountDue,
          paymentStatus: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          parentId: student.parentId,
          parentName: parentData?.name || 'Unknown Parent',
          parentPhone: parentData?.phone,
          parentEmail: parentData?.email
        };

        // Create the document
        await addDoc(collection(db, 'studentPayments'), studentPaymentRecord);
        createdCount++;

        console.log(`✅ Created payment record for ${student.name} (${student.class}) - ₵${totalAmountDue}`);
      }

      console.log(`🎉 Successfully created ${createdCount} student payment records`);

      if (createdCount === 0) {
        console.log('ℹ️ All eligible students already have payment records');
      }

    } catch (error) {
      console.error('❌ Error creating student payment records:', error);
    }
  };

  // Calculate real collected amount from actual payments
  const calculateCollectedRevenue = () => {
    // Method 1: Sum from studentPayments (aggregated data)
    const fromStudentPayments = studentPayments.reduce((sum, sp) => sum + (sp.totalAmountPaid || 0), 0);

    // Method 2: Sum from direct payment records (individual transactions)
    const fromPaymentRecords = paymentRecords.reduce((sum, payment) => sum + (payment.amount || 0), 0);

    // Use the higher of the two values to ensure we capture all payments
    // In a well-synced system, these should be equal
    const totalCollected = Math.max(fromStudentPayments, fromPaymentRecords);

    console.log('💰 Revenue Calculation:', {
      fromStudentPayments,
      fromPaymentRecords,
      totalCollected,
      studentPaymentsCount: studentPayments.length,
      paymentRecordsCount: paymentRecords.length
    });

    return totalCollected;
  };

  // Calculate overview statistics with correct logic
  const overviewStats = (() => {
    const totalExpected = calculateExpectedRevenue();
    const totalCollected = calculateCollectedRevenue();
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    // Count students who have fee structures (eligible for payment)
    const studentsWithFeeStructures = students.filter(student =>
      feeStructures.some(fs => fs.classLevel === student.class)
    ).length;

    // Count students who have actually made payments
    const studentsWithPayments = studentPayments.filter(sp =>
      sp.payments && sp.payments.length > 0 && sp.totalAmountPaid > 0
    ).length;

    // Calculate outstanding amount
    const outstandingAmount = Math.max(0, totalExpected - totalCollected);

    return {
      totalStudents: students.length,
      studentsWithFeeStructures, // Students who can pay (have fee structures)
      studentsWithoutFeeStructures: students.length - studentsWithFeeStructures,
      studentsWithPayments, // Students who have actually paid
      totalExpected,
      totalCollected,
      outstandingAmount,
      collectionRate,
      // Calculate averages based on students with fee structures (not all students)
      averagePaymentPerEligibleStudent: studentsWithFeeStructures > 0 ? totalCollected / studentsWithFeeStructures : 0,
      expectedPaymentPerEligibleStudent: studentsWithFeeStructures > 0 ? totalExpected / studentsWithFeeStructures : 0
    };
  })();

  // Simplified debug logging
  console.log('📊 Academic Fees Data:', {
    students: students.length,
    payments: studentPayments.length,
    feeStructures: feeStructures.length
  });

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please log in to access the Academic Fees management.</p>
        </div>
      </div>
    );
  }

  if (user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only administrators can access the Academic Fees management.</p>
          <p className="text-sm text-gray-500 mt-2">Current role: {user.role}</p>
        </div>
      </div>
    );
  }

  // Removed loading screen - page loads immediately and updates silently

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Academic Fees Management</h1>
              <p className="text-gray-600 mt-2">Manage fee structures and track student payments</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={createStudentPaymentRecords}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
                title="Create payment records for all eligible students"
              >
                Create Payment Records
              </button>
              <select
                value={selectedAcademicYear}
                onChange={(e) => setSelectedAcademicYear(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                aria-label="Select academic year"
                title="Select academic year for fee management"
              >
                {getAvailableAcademicYears().map((year) => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-xl">
                <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{overviewStats.totalStudents}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-xl">
                <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expected Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.totalExpected)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-xl">
                <svg className="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Collected</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.totalCollected)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-orange-100 rounded-xl">
                <svg className="w-6 h-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Collection Rate</p>
                <p className="text-2xl font-bold text-gray-900">{overviewStats.collectionRate}%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Stats Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-emerald-100 rounded-xl">
                <svg className="w-6 h-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Outstanding Amount</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.outstandingAmount)}</p>
                <p className="text-xs text-gray-500">Amount yet to be collected</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-cyan-100 rounded-xl">
                <svg className="w-6 h-6 text-cyan-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Students with Payments</p>
                <p className="text-2xl font-bold text-gray-900">{overviewStats.studentsWithPayments}</p>
                <p className="text-xs text-gray-500">Out of {overviewStats.studentsWithFeeStructures} eligible students</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center">
              <div className="p-3 bg-indigo-100 rounded-xl">
                <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Average per Eligible Student</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overviewStats.averagePaymentPerEligibleStudent)}</p>
                <p className="text-xs text-gray-500">Expected: {formatCurrency(overviewStats.expectedPaymentPerEligibleStudent)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: '📊' },
                { id: 'fee-structure', name: 'Fee Structure', icon: '💰' },
                { id: 'payments', name: 'Student Payments', icon: '💳' },
                { id: 'reports', name: 'Reports', icon: '📈' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-label={`Switch to ${tab.name} tab`}
                  title={`Switch to ${tab.name} tab`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && <OverviewTab studentPayments={studentPayments} />}
            {activeTab === 'fee-structure' && (
              <FeeStructureTab
                feeStructures={feeStructures}
                academicYear={selectedAcademicYear}
                onUpdate={() => {/* Refresh handled by real-time listener */}}
                setShowCreateForm={setShowCreateForm}
                setEditingStructure={setEditingStructure}
                setFormData={setFormData}
                handleEdit={handleEdit}
              />
            )}
            {activeTab === 'payments' && (
              <PaymentsTab
                studentPayments={studentPayments}
                academicYear={selectedAcademicYear}
                showPaymentForm={showPaymentForm}
                selectedStudent={selectedStudent}
                setShowPaymentForm={setShowPaymentForm}
                setSelectedStudent={setSelectedStudent}
                setPaymentData={setPaymentData}
                paymentData={paymentData}
                handlePayment={handleRecordPayment}
                saving={saving}
              />
            )}
            {activeTab === 'reports' && <ReportsTab studentPayments={studentPayments} />}
          </div>
        </div>
      </div>

      {/* Modals mounted at root level like other admin forms */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl w-full max-w-4xl border border-white/20 shadow-2xl h-[85vh] flex flex-col">
            {/* Fixed Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-xl font-semibold text-gray-900">
                {editingStructure ? 'Edit Fee Structure' : 'Create Fee Structure'}
              </h2>
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setEditingStructure(null);
                  setFormData({
                    classLevel: '' as ClassLevel,
                    fees: { ...DEFAULT_FEE_STRUCTURE },
                    currency: 'GHS',
                    paymentFrequency: 'yearly',
                    termNumber: 1,
                    semesterNumber: 1
                  });
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="Close form"
                aria-label="Close fee structure form"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-6">
                {/* Class Level Selection */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Class Level</label>
                  <select
                    value={formData.classLevel}
                    onChange={(e) => setFormData({ ...formData, classLevel: e.target.value as ClassLevel })}
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!!editingStructure}
                    title="Select class level for fee structure"
                    aria-label="Select class level for fee structure"
                  >
                    <option value="">Select Class Level</option>
                    {Object.values(CLASS_LEVEL_GROUPS).flat().map((level) => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                  {editingStructure && (
                    <p className="text-xs text-gray-500">Class level cannot be changed when editing</p>
                  )}
                </div>

                {/* Payment Frequency Selection */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Payment Frequency</label>
                  <select
                    value={formData.paymentFrequency}
                    onChange={(e) => setFormData({ ...formData, paymentFrequency: e.target.value as 'yearly' | 'semester' | 'term' })}
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                    title="Select payment frequency for fee structure"
                    aria-label="Select payment frequency for fee structure"
                  >
                    <option value="yearly">Yearly (Annual Payment)</option>
                    <option value="semester">Semester (2 payments per year)</option>
                    <option value="term">Term (3 payments per year)</option>
                  </select>
                  <p className="text-xs text-gray-500">
                    Choose how often students will pay these fees during the academic year
                  </p>
                </div>

                {/* Term Selection - Only show for term frequency */}
                {formData.paymentFrequency === 'term' && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Select Term</label>
                    <select
                      value={formData.termNumber}
                      onChange={(e) => setFormData({ ...formData, termNumber: parseInt(e.target.value) as 1 | 2 | 3 })}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                      title="Select which term this fee structure applies to"
                      aria-label="Select term number"
                    >
                      <option value={1}>Term 1 (First Term)</option>
                      <option value={2}>Term 2 (Second Term)</option>
                      <option value={3}>Term 3 (Third Term)</option>
                    </select>
                    <p className="text-xs text-gray-500">
                      This fee structure will apply to the selected term only
                    </p>
                  </div>
                )}

                {/* Semester Selection - Only show for semester frequency */}
                {formData.paymentFrequency === 'semester' && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Select Semester</label>
                    <select
                      value={formData.semesterNumber}
                      onChange={(e) => setFormData({ ...formData, semesterNumber: parseInt(e.target.value) as 1 | 2 })}
                      className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                      title="Select which semester this fee structure applies to"
                      aria-label="Select semester number"
                    >
                      <option value={1}>Semester 1 (First Semester)</option>
                      <option value={2}>Semester 2 (Second Semester)</option>
                    </select>
                    <p className="text-xs text-gray-500">
                      This fee structure will apply to the selected semester only
                    </p>
                  </div>
                )}

                {/* Fee Structure Fields */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">Fee Breakdown</h4>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(FEE_TYPE_LABELS).map(([key, label]) => (
                      <div key={key} className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">{label}</label>
                        <div className="relative">
                          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">GH₵</span>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={formData.fees[key as keyof typeof formData.fees] || 0}
                            onChange={(e) => setFormData({
                              ...formData,
                              fees: {
                                ...formData.fees,
                                [key]: parseFloat(e.target.value) || 0
                              }
                            })}
                            className="w-full pl-12 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                            placeholder="0.00"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Total Amount Display */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Fee Structure</p>
                        <p className="font-semibold text-gray-900">
                          {formData.paymentFrequency === 'yearly' ? 'Annual Amount' :
                           formData.paymentFrequency === 'semester' ? `Semester ${formData.semesterNumber} Amount` :
                           `Term ${formData.termNumber} Amount`}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        {formatCurrency(calculateTotalFees({ fees: formData.fees } as FeeStructure))}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formData.paymentFrequency === 'yearly' ? 'Per academic year' :
                         formData.paymentFrequency === 'semester' ? `For semester ${formData.semesterNumber} only` :
                         `For term ${formData.termNumber} only`}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateForm(false);
                      setEditingStructure(null);
                    }}
                    className="flex-1 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-xl transition-all duration-200 hover:shadow-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={saving || !formData.classLevel}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:shadow-lg flex items-center justify-center space-x-2"
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>{editingStructure ? 'Update Structure' : 'Create Structure'}</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {showPaymentForm && selectedStudent && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/90 backdrop-blur-xl rounded-2xl w-full max-w-2xl border border-white/20 shadow-2xl h-[85vh] flex flex-col">
            {/* Fixed Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-xl font-semibold text-gray-900">Record Payment</h2>
              <button
                type="button"
                onClick={() => setShowPaymentForm(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="Close payment form"
                aria-label="Close payment form"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-6">
                {/* Student Information Card */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">
                        {selectedStudent.studentName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-lg">{selectedStudent.studentName}</h4>
                      <p className="text-gray-600">Class: {selectedStudent.classLevel}</p>
                      <p className="text-sm">
                        <span className="text-gray-600">Amount Remaining: </span>
                        <span className="font-bold text-red-600 text-lg">{formatCurrency(selectedStudent.amountRemaining)}</span>
                      </p>
                    </div>
                  </div>
                </div>

                {/* Payment Amount */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Payment Amount</label>
                  <div className="relative">
                    <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">GH₵</span>
                    <input
                      type="number"
                      min="0"
                      max={selectedStudent.amountRemaining}
                      step="0.01"
                      value={paymentData.amount}
                      onChange={(e) => setPaymentData({ ...paymentData, amount: parseFloat(e.target.value) || 0 })}
                      className="w-full pl-12 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                      placeholder="0.00"
                    />
                  </div>
                  <p className="text-xs text-gray-500">Maximum: {formatCurrency(selectedStudent.amountRemaining)}</p>
                </div>

                {/* Payment Method */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                  <select
                    value={paymentData.paymentMethod}
                    onChange={(e) => setPaymentData({ ...paymentData, paymentMethod: e.target.value as PaymentMethod })}
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                    title="Select payment method"
                    aria-label="Select payment method"
                  >
                    <option value="cash">💵 Cash</option>
                    <option value="bank_transfer">🏦 Bank Transfer</option>
                    <option value="mobile_money">📱 Mobile Money</option>
                    <option value="cheque">📄 Cheque</option>
                    <option value="card">💳 Card</option>
                  </select>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <input
                    type="text"
                    value={paymentData.description}
                    onChange={(e) => setPaymentData({ ...paymentData, description: e.target.value })}
                    placeholder="e.g., Tuition fee payment, Books fee, etc."
                    className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>

                {/* Form Actions */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowPaymentForm(false)}
                    className="flex-1 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-xl transition-all duration-200 hover:shadow-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleRecordPayment}
                    disabled={saving || paymentData.amount <= 0 || paymentData.amount > selectedStudent.amountRemaining}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:shadow-lg flex items-center justify-center space-x-2"
                  >
                    {saving ? (
                      <>
                        <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <span>Record Payment</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ studentPayments }: { studentPayments: StudentPayment[] }) {
  // Group payments by class level
  const paymentsByClass = Object.entries(CLASS_LEVEL_GROUPS).map(([groupName, classes]) => {
    const classPayments = studentPayments.filter(sp => classes.includes(sp.classLevel));
    const totalExpected = classPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0);
    const totalCollected = classPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0);
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    return {
      groupName,
      classes,
      studentCount: classPayments.length,
      totalExpected,
      totalCollected,
      collectionRate
    };
  });

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Fee Collection Overview</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {paymentsByClass.map((group) => (
          <div key={group.groupName} className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">{group.groupName}</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Students:</span>
                <span className="font-medium">{group.studentCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expected:</span>
                <span className="font-medium">{formatCurrency(group.totalExpected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collected:</span>
                <span className="font-medium text-green-600">{formatCurrency(group.totalCollected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collection Rate:</span>
                <span className={`font-medium ${group.collectionRate >= 80 ? 'text-green-600' : group.collectionRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {group.collectionRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 relative overflow-hidden">
                <div
                  className={`absolute left-0 top-0 h-full rounded-full transition-all duration-300 ${group.collectionRate >= 80 ? 'bg-green-500' : group.collectionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                  style={{ width: `${Math.min(100, Math.max(0, group.collectionRate))}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Fee Structure Tab Component
function FeeStructureTab({
  feeStructures,
  academicYear,
  onUpdate,
  setShowCreateForm,
  setEditingStructure,
  setFormData,
  handleEdit
}: {
  feeStructures: FeeStructure[];
  academicYear: string;
  onUpdate: () => void;
  setShowCreateForm: (show: boolean) => void;
  setEditingStructure: (structure: FeeStructure | null) => void;
  setFormData: (data: any) => void;
  handleEdit: (structure: FeeStructure) => void;
}) {
  const { user } = useAuth();



  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Fee Structure Management</h3>
        <button
          type="button"
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          title="Add new fee structure"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>Add Fee Structure</span>
        </button>
      </div>

      {/* Fee Structures List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {feeStructures.map((structure) => (
          <div key={structure.id} className="bg-gray-50 rounded-xl p-6">
            <div className="flex justify-between items-start mb-4">
              <h4 className="font-semibold text-gray-900">{structure.classLevel}</h4>
              <button
                type="button"
                onClick={() => handleEdit(structure)}
                className="text-blue-600 hover:text-blue-800"
                title={`Edit fee structure for ${structure.classLevel}`}
                aria-label={`Edit fee structure for ${structure.classLevel}`}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            </div>

            <div className="space-y-2 text-sm">
              {Object.entries(structure.fees)
                .filter(([key, value]) => value && value > 0) // Only show fees with amounts
                .map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-gray-600">{FEE_TYPE_LABELS[key as keyof typeof FEE_TYPE_LABELS]}:</span>
                    <span className="font-medium">{formatCurrency(value || 0)}</span>
                  </div>
                ))}
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span className="text-blue-600">{formatCurrency(structure.totalAmount)}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>



    </div>
  );
}

// Payments Tab Component
function PaymentsTab({
  studentPayments,
  academicYear,
  showPaymentForm,
  selectedStudent,
  setShowPaymentForm,
  setSelectedStudent,
  setPaymentData,
  paymentData,
  handlePayment,
  saving
}: {
  studentPayments: StudentPayment[];
  academicYear: string;
  showPaymentForm: boolean;
  selectedStudent: StudentPayment | null;
  setShowPaymentForm: (show: boolean) => void;
  setSelectedStudent: (student: StudentPayment | null) => void;
  setPaymentData: (data: any) => void;
  paymentData: any;
  handlePayment: () => void;
  saving: boolean;
}) {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<PaymentStatus | ''>('');
  const [filterClass, setFilterClass] = useState<ClassLevel | ''>('');

  // Filter and sort payments
  const filteredPayments = filterStudentPayments(studentPayments, {
    searchTerm,
    paymentStatus: filterStatus || undefined,
    classLevel: filterClass || undefined,
    academicYear
  });



  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Student Payments</h3>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <input
          type="text"
          placeholder="Search students..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as PaymentStatus | '')}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Filter by payment status"
          aria-label="Filter by payment status"
        >
          <option value="">All Payment Status</option>
          <option value="pending">Pending</option>
          <option value="partial">Partial</option>
          <option value="completed">Completed</option>
          <option value="overdue">Overdue</option>
        </select>
        <select
          value={filterClass}
          onChange={(e) => setFilterClass(e.target.value as ClassLevel | '')}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Filter by class level"
          aria-label="Filter by class level"
        >
          <option value="">All Classes</option>
          {Object.values(CLASS_LEVEL_GROUPS).flat().map((level) => (
            <option key={level} value={level}>{level}</option>
          ))}
        </select>
      </div>

      {/* Payments Table */}
      <div className="bg-white rounded-xl overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Due</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Paid</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{payment.studentName}</div>
                      <div className="text-sm text-gray-500">{payment.parentName}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.classLevel}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(payment.totalAmountDue)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{formatCurrency(payment.totalAmountPaid)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{formatCurrency(payment.amountRemaining)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${PAYMENT_STATUS_COLORS[payment.paymentStatus].bg} ${PAYMENT_STATUS_COLORS[payment.paymentStatus].text}`}>
                      {payment.paymentStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {payment.amountRemaining > 0 ? (
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedStudent(payment);
                          setShowPaymentForm(true);
                        }}
                        className="inline-flex items-center space-x-1 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105"
                      >
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <span>Record</span>
                      </button>
                    ) : (
                      <span className="inline-flex items-center space-x-1 px-3 py-1.5 bg-green-100 text-green-700 text-xs font-medium rounded-lg">
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Paid</span>
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>


    </div>
  );
}

// Reports Tab Component
function ReportsTab({ studentPayments }: { studentPayments: StudentPayment[] }) {
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'outstanding'>('summary');

  const generateSummaryReport = () => {
    const totalStudents = studentPayments.length;
    const totalExpected = studentPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0);
    const totalCollected = studentPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0);
    const totalOutstanding = totalExpected - totalCollected;
    const collectionRate = totalExpected > 0 ? Math.round((totalCollected / totalExpected) * 100) : 0;

    const statusBreakdown = {
      completed: studentPayments.filter(sp => sp.paymentStatus === 'completed').length,
      partial: studentPayments.filter(sp => sp.paymentStatus === 'partial').length,
      pending: studentPayments.filter(sp => sp.paymentStatus === 'pending').length,
      overdue: studentPayments.filter(sp => sp.paymentStatus === 'overdue').length
    };

    return {
      totalStudents,
      totalExpected,
      totalCollected,
      totalOutstanding,
      collectionRate,
      statusBreakdown
    };
  };

  const summaryData = generateSummaryReport();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Fee Collection Reports</h3>
        <select
          value={reportType}
          onChange={(e) => setReportType(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          title="Select report type"
          aria-label="Select report type"
        >
          <option value="summary">Summary Report</option>
          <option value="detailed">Detailed Report</option>
          <option value="outstanding">Outstanding Payments</option>
        </select>
      </div>

      {reportType === 'summary' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Collection Summary</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Students:</span>
                <span className="font-medium">{summaryData.totalStudents}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expected Revenue:</span>
                <span className="font-medium">{formatCurrency(summaryData.totalExpected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collected:</span>
                <span className="font-medium text-green-600">{formatCurrency(summaryData.totalCollected)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Outstanding:</span>
                <span className="font-medium text-red-600">{formatCurrency(summaryData.totalOutstanding)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Collection Rate:</span>
                <span className="font-medium text-blue-600">{summaryData.collectionRate}%</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Payment Status Breakdown</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Completed:</span>
                <span className="font-medium text-green-600">{summaryData.statusBreakdown.completed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Partial:</span>
                <span className="font-medium text-blue-600">{summaryData.statusBreakdown.partial}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pending:</span>
                <span className="font-medium text-yellow-600">{summaryData.statusBreakdown.pending}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Overdue:</span>
                <span className="font-medium text-red-600">{summaryData.statusBreakdown.overdue}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {reportType === 'outstanding' && (
        <div className="bg-white rounded-xl overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outstanding Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Contact</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {studentPayments
                  .filter(sp => sp.amountRemaining > 0)
                  .sort((a, b) => b.amountRemaining - a.amountRemaining)
                  .map((payment) => (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{payment.studentName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.classLevel}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">{formatCurrency(payment.amountRemaining)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${PAYMENT_STATUS_COLORS[payment.paymentStatus].bg} ${PAYMENT_STATUS_COLORS[payment.paymentStatus].text}`}>
                          {payment.paymentStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>{payment.parentName}</div>
                          {payment.parentPhone && <div className="text-gray-500">{payment.parentPhone}</div>}
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
