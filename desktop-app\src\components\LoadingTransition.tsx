import { useState } from 'react'
import { getLogoPathWithFallback } from '../assets'

interface LoadingTransitionProps {
  isVisible: boolean
  message?: string
}

export default function LoadingTransition({ isVisible, message = 'Loading...' }: LoadingTransitionProps) {
  const [logoError, setLogoError] = useState(false)
  const [currentFallbackIndex, setCurrentFallbackIndex] = useState(0)

  if (!isVisible) return null

  const logoConfig = getLogoPathWithFallback()
  const currentLogoPath = logoError && currentFallbackIndex < logoConfig.fallbacks.length
    ? logoConfig.fallbacks[currentFallbackIndex]
    : logoConfig.primary

  const handleLogoError = () => {
    console.warn(`Logo failed to load: ${currentLogoPath}`)
    if (currentFallbackIndex < logoConfig.fallbacks.length - 1) {
      setCurrentFallbackIndex(prev => prev + 1)
    } else {
      setLogoError(true)
    }
  }

  const handleLogoLoad = () => {
    console.log(`Logo loaded successfully: ${currentLogoPath}`)
    setLogoError(false)
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 transition-all duration-300">
      <div className="bg-white/90 backdrop-blur-xl rounded-2xl p-8 shadow-2xl border border-white/20 max-w-sm mx-4">
        <div className="text-center space-y-4">
          {/* Loading Spinner */}
          <div className="flex justify-center">
            <div className="relative">
              <div className="w-12 h-12 rounded-xl overflow-hidden bg-gray-100 flex items-center justify-center">
                {!logoError ? (
                  <img
                    src={currentLogoPath}
                    alt="Maggie Preparatory School Logo"
                    className="w-full h-full object-cover"
                    onError={handleLogoError}
                    onLoad={handleLogoLoad}
                  />
                ) : (
                  // Fallback icon when all logo attempts fail
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xs font-bold">
                    MP
                  </div>
                )}
              </div>
              {/* Spinning Ring */}
              <div className="absolute inset-0 w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-xl animate-spin"></div>
            </div>
          </div>

          {/* Loading Message */}
          <div>
            <p className="text-gray-900 font-medium">{message}</p>
            <p className="text-gray-500 text-sm mt-1">Please wait...</p>
          </div>

          {/* Loading Dots */}
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce delay-100"></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce delay-200"></div>
          </div>
        </div>
      </div>
    </div>
  )
}
