"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Home, Users, BookOpen, Settings, GraduationCap, UserCheck, BarChart3, UserPlus, DollarSign, Upload, Award } from "lucide-react";
import { cn } from "@/lib/utils";
import type { UserRole } from "@/lib/types";

const commonLinks = [
  { href: "/dashboard", label: "Dashboard", icon: Home },
];

const roleLinks: Record<UserRole, { href: string; label: string; icon: React.ElementType }[]> = {
  admin: [
    { href: "/students", label: "Students", icon: GraduationCap },
    { href: "/subjects", label: "Subjects", icon: BookOpen },
    { href: "/results", label: "Upload Results", icon: Upload },
    { href: "/view-results", label: "View Results", icon: Award },
    { href: "/analytics", label: "Analytics", icon: Bar<PERSON>hart3 },
    { href: "/users", label: "Manage Users", icon: User<PERSON><PERSON><PERSON> },
    { href: "/academic-fees", label: "Academic Fees", icon: DollarSign },
    { href: "/settings", label: "Settings", icon: Settings },
    { href: "/student-registration", label: "Student Registration", icon: UserPlus },
  ],
  teacher: [
    { href: "/students", label: "My Students", icon: GraduationCap },
    { href: "/view-results", label: "View Results", icon: Award },
    { href: "/analytics", label: "Class Analytics", icon: BarChart3 },
    { href: "/teacher-academic-fees", label: "Academic Fees", icon: DollarSign },
    { href: "/settings", label: "Settings", icon: Settings },
  ],
  parent: [
    { href: "/my-children", label: "My Children", icon: Users },
    { href: "/settings", label: "Settings", icon: Settings },
  ],
};

export function MainNav({ userRole, isCollapsed = false }: { userRole: UserRole; isCollapsed?: boolean }) {
  const pathname = usePathname();
  const links = [...commonLinks, ...(roleLinks[userRole] || [])];

  return (
    <nav className="space-y-2">
      <div className="px-3 py-2">
        {!isCollapsed && (
          <h2 className="mb-2 px-2 text-xs font-semibold tracking-tight text-muted-foreground uppercase">
            Navigation
          </h2>
        )}
        <div className="space-y-1">
          {links.map(({ href, label, icon: Icon }) => {
            const isActive = pathname === href;
            return (
              <Link
                key={href}
                href={href}
                className={cn(
                  "group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 relative overflow-hidden nav-item",
                  isActive 
                    ? "bg-primary/10 text-primary border-r-2 border-primary shadow-sm" 
                    : "text-muted-foreground hover:text-foreground hover:bg-muted/50",
                  isCollapsed && "justify-center px-2"
                )}
                data-tooltip={isCollapsed ? label : undefined}
              >
                <div className={cn(
                  "absolute inset-0 rounded-lg transition-all duration-200",
                  isActive 
                    ? "bg-primary/5" 
                    : "group-hover:bg-muted/30"
                )} />
                
                <div className={cn(
                  "relative z-10 flex items-center justify-center w-5 h-5 transition-all duration-200 shrink-0",
                  isActive && "text-primary"
                )}>
                  <Icon className="w-4 h-4" />
                </div>
                
                {!isCollapsed && (
                  <span className="relative z-10 mobile-truncate flex-1">{label}</span>
                )}
                
                {isActive && !isCollapsed && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-primary rounded-full shrink-0" />
                )}
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
}
