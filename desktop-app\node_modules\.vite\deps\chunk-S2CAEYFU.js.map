{"version": 3, "sources": ["../../.vite-electron-renderer/events.mjs"], "sourcesContent": ["const avoid_parse_require = require; const _M_ = avoid_parse_require(\"events\");\nexport const length = _M_.length;\nexport const name = _M_.name;\nexport const prototype = _M_.prototype;\nexport const addAbortListener = _M_.addAbortListener;\nexport const once = _M_.once;\nexport const on = _M_.on;\nexport const getEventListeners = _M_.getEventListeners;\nexport const getMaxListeners = _M_.getMaxListeners;\nexport const EventEmitter = _M_.EventEmitter;\nexport const usingDomains = _M_.usingDomains;\nexport const captureRejectionSymbol = _M_.captureRejectionSymbol;\nexport const captureRejections = _M_.captureRejections;\nexport const EventEmitterAsyncResource = _M_.EventEmitterAsyncResource;\nexport const errorMonitor = _M_.errorMonitor;\nexport const defaultMaxListeners = _M_.defaultMaxListeners;\nexport const kMaxEventTargetListeners = _M_.kMaxEventTargetListeners;\nexport const kMaxEventTargetListenersWarned = _M_.kMaxEventTargetListenersWarned;\nexport const setMaxListeners = _M_.setMaxListeners;\nexport const init = _M_.init;\nexport const listenerCount = _M_.listenerCount;\nconst keyword_default = _M_.default || _M_;\nexport {\n  keyword_default as default,\n};"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAM,qBAAqC,KAC9B,QACA,MACA,WACA,kBACA,MACA,IACA,mBACA,iBACA,cACA,cACA,wBACA,mBACA,2BACA,cACA,qBACA,0BACA,gCACA,iBACA,MACA,eACP;AArBN;AAAA;AAAA,IAAM,sBAAsB;AAAS,IAAM,MAAM,oBAAoB,QAAQ;AACtE,IAAM,SAAS,IAAI;AACnB,IAAM,OAAO,IAAI;AACjB,IAAM,YAAY,IAAI;AACtB,IAAM,mBAAmB,IAAI;AAC7B,IAAM,OAAO,IAAI;AACjB,IAAM,KAAK,IAAI;AACf,IAAM,oBAAoB,IAAI;AAC9B,IAAM,kBAAkB,IAAI;AAC5B,IAAM,eAAe,IAAI;AACzB,IAAM,eAAe,IAAI;AACzB,IAAM,yBAAyB,IAAI;AACnC,IAAM,oBAAoB,IAAI;AAC9B,IAAM,4BAA4B,IAAI;AACtC,IAAM,eAAe,IAAI;AACzB,IAAM,sBAAsB,IAAI;AAChC,IAAM,2BAA2B,IAAI;AACrC,IAAM,iCAAiC,IAAI;AAC3C,IAAM,kBAAkB,IAAI;AAC5B,IAAM,OAAO,IAAI;AACjB,IAAM,gBAAgB,IAAI;AACjC,IAAM,kBAAkB,IAAI,WAAW;AAAA;AAAA;", "names": []}