import { useState, useEffect } from 'react'
import { collection, query, where, onSnapshot } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useAuth } from '../hooks/use-auth'
import { Student, Term } from '../lib/types'
import { StudentResult } from '../lib/results-types'

export default function MyChildren() {
  const { user } = useAuth()
  const [selectedChild, setSelectedChild] = useState<string | null>(null)
  const [children, setChildren] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [teachers, setTeachers] = useState<any[]>([])

  // Filter states for results
  const [selectedTerm, setSelectedTerm] = useState<Term | 'all'>('all')
  const [selectedYear, setSelectedYear] = useState<string>('all')

  // Contact modal states
  const [showContactModal, setShowContactModal] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState<any>(null)

  // Fetch children data from Firebase
  useEffect(() => {
    if (!user || !user.childrenIds || user.childrenIds.length === 0) {
      setChildren([])
      setLoading(false)
      return
    }

    const studentsCollection = collection(db, 'students')
    const studentsQuery = query(studentsCollection, where('__name__', 'in', user.childrenIds))

    // Use real-time listener for students
    const unsubscribeStudents = onSnapshot(studentsQuery, (snapshot) => {
      try {
        const childrenData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Student[]

        setChildren(childrenData)
        console.log('✅ Children data loaded:', childrenData.length, 'children')
      } catch (error) {
        console.error('❌ Error fetching children data:', error)
        setChildren([])
      }
    }, (error) => {
      console.error('❌ Error in children listener:', error)
      setChildren([])
      setLoading(false)
    })

    // Fetch results from the main results collection (like ViewResults page does)
    if (user.childrenIds.length > 0) {
      const resultsQuery = query(
        collection(db, 'results'),
        where('studentId', 'in', user.childrenIds.slice(0, 10)), // Firestore 'in' limit
        // orderBy('createdAt', 'desc') // Remove this to avoid index issues for now
      )

      // Fetch teachers to get contact information
      const teachersQuery = query(
        collection(db, 'users'),
        where('role', '==', 'teacher')
      )

      const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
        try {
          const resultsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }))

          console.log('✅ Results data loaded:', resultsData.length, 'results')

          // Attach results to children
          setChildren(prevChildren =>
            prevChildren.map(child => ({
              ...child,
              results: resultsData.filter((result: any) => result.studentId === child.id)
            }))
          )
          setLoading(false)
        } catch (error) {
          console.error('❌ Error processing results data:', error)
          setLoading(false)
        }
      }, (error) => {
        console.error('❌ Error in results listener:', error)
        setLoading(false)
      })

      // Set up real-time listeners for teachers
      const unsubscribeTeachers = onSnapshot(teachersQuery, (snapshot) => {
        try {
          const teachersData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          setTeachers(teachersData)
          console.log('✅ MyChildren - teachers loaded:', teachersData.length)
        } catch (error) {
          console.error('❌ Error in teachers listener:', error)
          setTeachers([])
        }
      }, (error) => {
        console.error('❌ Error in teachers listener:', error)
        setTeachers([])
      })

      return () => {
        unsubscribeStudents()
        unsubscribeResults()
        unsubscribeTeachers()
      }
    } else {
      setLoading(false)
      return unsubscribeStudents
    }
  }, [user])

  // Helper function to find teacher for a specific class
  const getClassTeacher = (className: string) => {
    return teachers.find((teacher: any) =>
      teacher.assignedClasses && teacher.assignedClasses.includes(className)
    );
  };

  // Handle teacher contact
  const handleContactTeacher = (child: any) => {
    if (child.teacherAvailable) {
      setSelectedTeacher({
        name: child.teacher,
        email: child.teacherEmail,
        phone: child.teacherPhone,
        class: child.class,
        childName: child.name
      });
      setShowContactModal(true);
    }
  };

  // Process children data to match the expected format
  const processedChildren = children.map(child => {
    console.log(`Processing child ${child.name}:`, {
      id: child.id,
      resultsCount: child.results?.length || 0,
      results: child.results
    })

    // Calculate age from date of birth
    const age = child.dob ? Math.floor((new Date().getTime() - new Date(child.dob).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 0

    // Calculate overall grade from results using the new StudentResult structure
    const percentages = child.results?.map((result: StudentResult) => result.percentage || 0) || []
    const avgPercentage = percentages.length > 0 ? percentages.reduce((sum: number, percentage: number) => sum + percentage, 0) / percentages.length : 0
    const overallGrade = avgPercentage >= 90 ? 'A' : avgPercentage >= 80 ? 'B' : avgPercentage >= 70 ? 'C' : 'D'

    // Process all results for display using the new StudentResult structure
    const allResults = child.results?.map((result: StudentResult) => ({
      id: result.id,
      subject: result.subjectName || result.subjectCode || 'Unknown',
      grade: result.grade || 'N/A',
      score: result.percentage || 0,
      term: result.term || Term.First,
      year: result.academicYear || new Date().getFullYear().toString(),
      date: result.createdAt || new Date().toISOString(),
      marks: result.marks || 0,
      totalMarks: result.totalMarks || 100
    })) || []

    // Get teacher information for this child's class
    const classTeacher = getClassTeacher(child.class);

    return {
      ...child,
      age,
      avatar: child.name?.split(' ').map((n: string) => n[0]).join('') || '👤',
      attendance: child.enrollmentStatus === 'active' ? 95 : 85, // Mock attendance
      overallGrade,
      allResults,
      // Real teacher information
      teacher: classTeacher ? classTeacher.name : 'Not Available',
      teacherEmail: classTeacher ? classTeacher.email : null,
      teacherPhone: classTeacher ? classTeacher.phone : null,
      teacherAvailable: !!classTeacher
    }
  })

  // Get available years from all children's results
  const getAvailableYears = () => {
    const years = new Set<string>()
    processedChildren.forEach(child => {
      child.allResults?.forEach((result: any) => {
        if (result.year) years.add(result.year)
      })
    })
    return Array.from(years).sort((a, b) => b.localeCompare(a)) // Sort descending (newest first)
  }

  // Filter results based on selected term and year
  const getFilteredResults = (childResults: any[]) => {
    if (!childResults) return []

    return childResults.filter((result: any) => {
      const termMatch = selectedTerm === 'all' || result.term === selectedTerm
      const yearMatch = selectedYear === 'all' || result.year === selectedYear
      return termMatch && yearMatch
    }).sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime()) // Sort by date, newest first
  }

  // Check if user is parent
  if (user?.role !== 'parent') {
    return (
      <div className="p-6 flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Access Denied</h3>
            <p className="text-sm text-gray-600">
              This page is only accessible to parents.
            </p>
          </div>
        </div>
      </div>
    )
  }

  const selectedChildData = selectedChild ? processedChildren.find(c => c.id === selectedChild) : processedChildren[0]

  // Loading state
  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Children</h1>
          <p className="text-gray-600">Loading your children's information...</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  // No children state
  if (processedChildren.length === 0) {
    return (
      <div className="p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Children</h1>
          <p className="text-gray-600">Track your children's academic progress and school activities</p>
        </div>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Children Found</h3>
          <p className="text-gray-600">No children are associated with your account.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Children</h1>
        <p className="text-gray-600">Track your children's academic progress and school activities</p>
      </div>

      {/* Children Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {processedChildren.map((child) => (
          <div
            key={child.id}
            onClick={() => setSelectedChild(child.id)}
            className={`bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl cursor-pointer transition-all duration-200 hover:shadow-2xl hover:scale-105 ${
              selectedChild === child.id || (!selectedChild && child.id === processedChildren[0].id)
                ? 'ring-2 ring-blue-500 bg-blue-50/50'
                : ''
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-2xl text-white font-semibold">
                {child.avatar}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">{child.name}</h3>
                <p className="text-sm text-gray-600">{child.class}</p>
                <p className="text-xs text-gray-500">{child.age} years old</p>
              </div>
            </div>

            <div className="mt-4 grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-xs text-gray-500">Attendance</p>
                <p className="text-lg font-semibold text-green-600">{child.attendance}%</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Overall Grade</p>
                <p className="text-lg font-semibold text-blue-600">{child.overallGrade}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedChildData && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Debug Info - Remove this after testing */}
          {process.env.NODE_ENV === 'development' && (
            <div className="lg:col-span-3 bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <h4 className="font-semibold text-yellow-800 mb-2">Debug Info:</h4>
              <p className="text-sm text-yellow-700">
                Child: {selectedChildData.name} |
                Total Results: {selectedChildData.allResults?.length || 0} |
                Available Years: {getAvailableYears().join(', ') || 'None'}
              </p>
            </div>
          )}

          {/* Academic Performance */}
          <div className="lg:col-span-2 space-y-6">
            {/* Academic Results */}
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span>Academic Results</span>
                </h3>

                {/* Filter Controls */}
                <div className="flex items-center space-x-3">
                  <select
                    value={selectedTerm}
                    onChange={(e) => setSelectedTerm(e.target.value as Term | 'all')}
                    aria-label="Filter by term"
                    className="px-3 py-1 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Terms</option>
                    <option value={Term.First}>First Term</option>
                    <option value={Term.Second}>Second Term</option>
                    <option value={Term.Third}>Third Term</option>
                  </select>

                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(e.target.value)}
                    aria-label="Filter by year"
                    className="px-3 py-1 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Years</option>
                    {getAvailableYears().map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="space-y-4">
                {(() => {
                  const filteredResults = getFilteredResults(selectedChildData.allResults || [])
                  console.log('Filtered results for display:', {
                    childName: selectedChildData.name,
                    totalResults: selectedChildData.allResults?.length || 0,
                    filteredCount: filteredResults.length,
                    selectedTerm,
                    selectedYear,
                    filteredResults
                  })
                  return filteredResults.length > 0 ? filteredResults.map((result, index) => (
                    <div key={result.id || index} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
                          {result.subject.charAt(0)}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{result.subject}</h4>
                          <p className="text-sm text-gray-600">
                            {result.marks && result.totalMarks ?
                              `${result.marks}/${result.totalMarks} (${result.score}%)` :
                              `Score: ${result.score}%`
                            } • {result.term} Term {result.year}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          result.grade.toString().startsWith('A') || result.score >= 90 ? 'bg-green-100 text-green-800' :
                          result.grade.toString().startsWith('B') || result.score >= 80 ? 'bg-blue-100 text-blue-800' :
                          result.grade.toString().startsWith('C') || result.score >= 70 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {result.grade}
                        </span>
                      </div>
                    </div>
                  )) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">
                        {selectedTerm === 'all' && selectedYear === 'all'
                          ? 'No results available'
                          : 'No results found for the selected filters'
                        }
                      </p>
                    </div>
                  )
                })()}
              </div>
            </div>

            {/* Teacher Information */}
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <span>Class Teacher</span>
              </h3>
              
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-semibold">
                  {selectedChildData.teacherAvailable ? selectedChildData.teacher.split(' ').map((n: string) => n[0]).join('').slice(0, 2) : '?'}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">
                    {selectedChildData.teacherAvailable ? selectedChildData.teacher : 'Teacher Not Available'}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {selectedChildData.teacherAvailable ? selectedChildData.teacherEmail || 'No email available' : 'Contact not available'}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => handleContactTeacher(selectedChildData)}
                  disabled={!selectedChildData.teacherAvailable}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedChildData.teacherAvailable
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {selectedChildData.teacherAvailable ? 'Contact' : 'Not Available'}
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Attendance Rate</span>
                  <span className="text-sm font-semibold text-green-600">{selectedChildData.attendance}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 relative overflow-hidden">
                  <div
                    className={`bg-green-500 h-2 rounded-full transition-all duration-300 ${
                      selectedChildData.attendance >= 95 ? 'w-full' :
                      selectedChildData.attendance >= 90 ? 'w-11/12' :
                      selectedChildData.attendance >= 85 ? 'w-5/6' :
                      selectedChildData.attendance >= 80 ? 'w-4/5' :
                      selectedChildData.attendance >= 75 ? 'w-3/4' :
                      selectedChildData.attendance >= 70 ? 'w-2/3' :
                      selectedChildData.attendance >= 60 ? 'w-3/5' :
                      'w-1/2'
                    }`}
                  ></div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Overall Grade</span>
                  <span className="text-sm font-semibold text-blue-600">{selectedChildData.overallGrade}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Class Rank</span>
                  <span className="text-sm font-semibold text-purple-600">3rd of 25</span>
                </div>
              </div>
            </div>


          </div>
        </div>
      )}

      {/* Teacher Contact Modal */}
      {showContactModal && selectedTeacher && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Teacher Contact</h3>
              <button
                type="button"
                onClick={() => setShowContactModal(false)}
                className="text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-semibold">
                  {selectedTeacher.name.split(' ').map((n: string) => n[0]).join('').slice(0, 2)}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{selectedTeacher.name}</h4>
                  <p className="text-sm text-gray-600">Class Teacher - {selectedTeacher.class}</p>
                  <p className="text-xs text-gray-500">Teaching {selectedTeacher.childName}</p>
                </div>
              </div>

              <div className="space-y-3">
                {selectedTeacher.email && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <p className="text-sm text-gray-600">{selectedTeacher.email}</p>
                    </div>
                  </div>
                )}

                {selectedTeacher.phone && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phone</p>
                      <p className="text-sm text-gray-600">{selectedTeacher.phone}</p>
                    </div>
                  </div>
                )}

                {!selectedTeacher.email && !selectedTeacher.phone && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No contact information available</p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3 pt-4">
                {selectedTeacher.email && (
                  <button
                    type="button"
                    onClick={() => window.location.href = `mailto:${selectedTeacher.email}`}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Send Email
                  </button>
                )}
                {selectedTeacher.phone && (
                  <button
                    type="button"
                    onClick={() => window.location.href = `tel:${selectedTeacher.phone}`}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Call
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
