import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Space_Grotesk } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { FloatingAlertProvider } from "@/components/ui/floating-alert-provider";
import { cn } from "@/lib/utils";
import { AuthProvider } from "@/hooks/use-auth";

const fontBody = Inter({
  subsets: ["latin"],
  variable: "--font-body",
});

const fontHeadline = Space_Grotesk({
  subsets: ["latin"],
  variable: "--font-headline",
});


export const metadata: Metadata = {
  title: "Maggie Preparatory School",
  description: "A modern school management system.",
  icons: {
    icon: [
      {
        url: "/favicon.ico",
        sizes: "any",
      },
    ],
    apple: [
      {
        url: "/favicon.ico",
        sizes: "any",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn("font-body antialiased", fontBody.variable, fontHeadline.variable)}>
        <AuthProvider>
          <FloatingAlertProvider>
            {children}
            <Toaster />
          </FloatingAlertProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
