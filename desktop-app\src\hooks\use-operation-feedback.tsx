import { useCallback, useRef } from 'react';
import { useDynamicIsland } from './use-dynamic-island';
import { useAuth } from './use-auth';

interface OperationFeedbackOptions {
  showProgress?: boolean;
  successMessage?: string;
  errorMessage?: string;
  duration?: number;
}

export const useOperationFeedback = () => {
  const { showToast } = useDynamicIsland();
  const { isOnline, authMode } = useAuth();
  const operationCountRef = useRef(0);

  const executeWithFeedback = useCallback(async <T>(
    operation: () => Promise<T>,
    options: OperationFeedbackOptions = {}
  ): Promise<T> => {
    const {
      showProgress = true,
      successMessage = 'Operation completed successfully',
      errorMessage = 'Operation failed',
      duration = 2000
    } = options;

    const operationId = ++operationCountRef.current;
    
    try {
      // Show progress notification
      if (showProgress) {
        const progressMessage = isOnline 
          ? 'Processing...' 
          : 'Saving offline... Will sync when online';
          
        showToast({
          title: '🔄 Working...',
          description: progressMessage,
          variant: 'info',
          duration: 1500
        });
      }

      // Execute the operation
      const result = await operation();

      // Show success notification
      const finalSuccessMessage = authMode === 'offline' 
        ? `${successMessage} (Offline - will sync later)`
        : successMessage;

      showToast({
        title: '✅ Success',
        description: finalSuccessMessage,
        variant: 'success',
        duration
      });

      return result;

    } catch (error) {
      console.error(`Operation ${operationId} failed:`, error);
      
      // Show error notification
      const finalErrorMessage = error instanceof Error 
        ? error.message 
        : errorMessage;

      showToast({
        title: '❌ Error',
        description: finalErrorMessage,
        variant: 'error',
        duration: 4000
      });

      throw error;
    }
  }, [showToast, isOnline, authMode]);

  const showSuccess = useCallback((message: string, duration = 2000) => {
    const finalMessage = authMode === 'offline' 
      ? `${message} (Offline - will sync later)`
      : message;

    showToast({
      title: '✅ Success',
      description: finalMessage,
      variant: 'success',
      duration
    });
  }, [showToast, authMode]);

  const showError = useCallback((message: string, duration = 4000) => {
    showToast({
      title: '❌ Error',
      description: message,
      variant: 'error',
      duration
    });
  }, [showToast]);

  const showInfo = useCallback((message: string, duration = 3000) => {
    showToast({
      title: 'ℹ️ Info',
      description: message,
      variant: 'info',
      duration
    });
  }, [showToast]);

  const showWarning = useCallback((message: string, duration = 3000) => {
    showToast({
      title: '⚠️ Warning',
      description: message,
      variant: 'warning',
      duration
    });
  }, [showToast]);

  const showOfflineNotice = useCallback(() => {
    showToast({
      title: '📱 Offline Mode',
      description: 'Changes saved locally. Will sync when online.',
      variant: 'info',
      duration: 3000
    });
  }, [showToast]);

  return {
    executeWithFeedback,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    showOfflineNotice
  };
};

export default useOperationFeedback;
