import { useCallback, useRef } from 'react';
import { useNotifications } from '../components/providers/notification-provider';
import { useAuth } from './use-auth';

interface OperationFeedbackOptions {
  showProgress?: boolean;
  successMessage?: string;
  errorMessage?: string;
  duration?: number;
}

export const useOperationFeedback = () => {
  const { notify } = useNotifications();
  const { isOnline, authMode } = useAuth();
  const operationCountRef = useRef(0);

  const executeWithFeedback = useCallback(async (
    operation: () => Promise<any>,
    options: OperationFeedbackOptions = {}
  ) => {
    const {
      showProgress = true,
      successMessage = 'Operation completed successfully',
      errorMessage = 'Operation failed',
      duration = 2000
    } = options;

    const operationId = ++operationCountRef.current;

    try {
      // Show progress notification
      if (showProgress) {
        const progressMessage = isOnline
          ? 'Processing...'
          : 'Saving offline... Will sync when online';

        notify.info('🔄 Working...', progressMessage, 1500);
      }

      // Execute the operation
      const result = await operation();

      // Show success notification
      const finalSuccessMessage = authMode === 'offline'
        ? `${successMessage} (Offline - will sync later)`
        : successMessage;

      notify.success('✅ Success', finalSuccessMessage, duration);

      return result;

    } catch (error) {
      console.error(`Operation ${operationId} failed:`, error);

      // Show error notification
      const finalErrorMessage = error instanceof Error
        ? error.message
        : errorMessage;

      notify.error('❌ Error', finalErrorMessage, 4000);

      throw error;
    }
  }, [notify, isOnline, authMode]);

  const showSuccess = useCallback((message: string, duration = 2000) => {
    const finalMessage = authMode === 'offline'
      ? `${message} (Offline - will sync later)`
      : message;

    notify.success('✅ Success', finalMessage, duration);
  }, [notify, authMode]);

  const showError = useCallback((message: string, duration = 4000) => {
    notify.error('❌ Error', message, duration);
  }, [notify]);

  const showInfo = useCallback((message: string, duration = 3000) => {
    notify.info('ℹ️ Info', message, duration);
  }, [notify]);

  const showWarning = useCallback((message: string, duration = 3000) => {
    notify.warning('⚠️ Warning', message, duration);
  }, [notify]);

  const showOfflineNotice = useCallback(() => {
    notify.info('📱 Offline Mode', 'Changes saved locally. Will sync when online.', 3000);
  }, [notify]);

  return {
    executeWithFeedback,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    showOfflineNotice
  };
};

export default useOperationFeedback;
