"use client";

import { useFloatingAlert } from "@/components/ui/floating-alert-provider";

export interface FloatingToastProps {
  title?: string;
  description?: string;
  variant?: "default" | "success" | "error" | "warning" | "info";
  duration?: number;
}

export function useFloatingToast() {
  const { addAlert } = useFloatingAlert();

  const toast = ({ title, description, variant = "default", duration = 5000 }: FloatingToastProps) => {
    const id = addAlert({
      title,
      description,
      variant,
      duration,
      open: true,
    });

    return {
      id,
      dismiss: () => {
        // The alert will auto-dismiss based on duration
      },
    };
  };

  return {
    toast,
  };
} 