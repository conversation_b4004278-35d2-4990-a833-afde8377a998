"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import { 
  Subject, 
  StudentResult, 
  ClassLevel, 
  Term,
  GradeScale,
  DEFAULT_GRADE_SCALES
} from "@/lib/results-types";
import { 
  calculateGrade, 
  calculatePercentage, 
  isPassed,
  getClassCategory,
  getSubjectsForClass,
  canUploadForClass,
  validateMarks,
  getGradeColor,
  generateRemarks
} from "@/lib/results-utils";
import { getCurrentAcademicYear } from "@/lib/business-logic";
import { Student } from "@/lib/types";
import { 
  collection, 
  addDoc, 
  onSnapshot, 
  doc, 
  updateDoc, 
  query,
  where,
  orderBy,
  getDocs
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Upload, FileText, Search, Filter, GraduationCap } from "lucide-react";

export default function ResultsPage() {
  const { user } = useAuth();
  const { toast } = useFloatingToast();
  const [students, setStudents] = useState<Student[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [results, setResults] = useState<StudentResult[]>([]);
  const [gradeScales, setGradeScales] = useState<GradeScale[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterClass, setFilterClass] = useState<ClassLevel | "all">("all");
  const [filterTerm, setFilterTerm] = useState<Term | "all">("all");
  const [uploadData, setUploadData] = useState({
    subjectId: "",
    term: "First" as Term,
    academicYear: getCurrentAcademicYear(),
    marks: "",
    totalMarks: "100",
    remarks: "",
    teacherComment: ""
  });

  // Load data from Firestore
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load students
        const studentsQuery = query(collection(db, "students"), orderBy("name"));
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Student[];
        setStudents(studentsData);

        // Load subjects
        const subjectsQuery = query(collection(db, "subjects"), orderBy("name"));
        const subjectsSnapshot = await getDocs(subjectsQuery);
        const subjectsData = subjectsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Subject[];
        setSubjects(subjectsData);

        // Load results
        const resultsQuery = query(collection(db, "results"), orderBy("createdAt", "desc"));
        const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
          const resultsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as StudentResult[];
          setResults(resultsData);
        });

        setLoading(false);
        return () => unsubscribeResults();
      } catch (error) {
        console.error("Error loading data:", error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleUploadResult = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !selectedStudent) return;

    try {
      const marks = parseInt(uploadData.marks);
      const totalMarks = parseInt(uploadData.totalMarks);

      // Validate marks
      const markErrors = validateMarks(marks, totalMarks);
      if (markErrors.length > 0) {
        toast({
          title: "Invalid Marks",
          description: markErrors.join(", "),
          variant: "error"
        });
        return;
      }

      // Get subject details
      const subject = subjects.find(s => s.id === uploadData.subjectId);
      if (!subject) {
        toast({
          title: "Error",
          description: "Subject not found.",
          variant: "error"
        });
        return;
      }

      // Get or create grade scale for this class
      const classCategory = getClassCategory(selectedStudent.class as ClassLevel);
      const gradeRanges = DEFAULT_GRADE_SCALES[classCategory];
      
      const mockGradeScale: GradeScale = {
        id: "mock",
        classLevel: selectedStudent.class as ClassLevel,
        gradeRanges,
        totalMarks,
        passMarkPercentage: 40,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: user.id
      };

      // Calculate grade and other metrics
      const grade = calculateGrade(marks, mockGradeScale);
      const percentage = calculatePercentage(marks, totalMarks);
      const passed = isPassed(marks, mockGradeScale);

      const resultData: Omit<StudentResult, 'id'> = {
        studentId: selectedStudent.id,
        studentName: selectedStudent.name,
        classLevel: selectedStudent.class as ClassLevel,
        subjectId: subject.id,
        subjectName: subject.name,
        subjectCode: subject.code,
        term: uploadData.term,
        academicYear: uploadData.academicYear,
        marks,
        totalMarks,
        grade,
        percentage,
        isPassed: passed,
        remarks: uploadData.remarks || generateRemarks(grade),
        teacherComment: uploadData.teacherComment,
        uploadedBy: user.id,
        uploadedByName: user.name,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await addDoc(collection(db, "results"), resultData);

      toast({
        title: "Result Uploaded",
        description: `Result for ${selectedStudent.name} in ${subject.name} has been uploaded successfully.`,
        variant: "success"
      });

      // Reset form
      setUploadData({
        subjectId: "",
        term: "First",
        academicYear: getCurrentAcademicYear(),
        marks: "",
        totalMarks: "100",
        remarks: "",
        teacherComment: ""
      });
      setSelectedStudent(null);
      setShowUploadModal(false);

    } catch (error) {
      console.error("Error uploading result:", error);
      toast({
        title: "Error",
        description: "Failed to upload result. Please try again.",
        variant: "error"
      });
    }
  };

  const handleSelectStudent = (student: Student) => {
    setSelectedStudent(student);
    setShowUploadModal(true);
  };

  const resetUploadForm = () => {
    setUploadData({
      subjectId: "",
      term: "First",
      academicYear: getCurrentAcademicYear(),
      marks: "",
      totalMarks: "100",
      remarks: "",
      teacherComment: ""
    });
    setSelectedStudent(null);
    setShowUploadModal(false);
  };

  const getAvailableSubjects = () => {
    if (!selectedStudent) return [];
    return getSubjectsForClass(selectedStudent.class as ClassLevel, subjects);
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = filterClass === "all" || student.class === filterClass;
    return matchesSearch && matchesClass;
  });

  const filteredResults = results.filter(result => {
    const matchesTerm = filterTerm === "all" || result.term === filterTerm;
    return matchesTerm;
  });

  const allClassLevels: ClassLevel[] = [
    "Nursery 1", "Nursery 2", "KG 1", "KG 2",
    "Basic 1", "Basic 2", "Basic 3", "Basic 4", "Basic 5", "Basic 6",
    "Basic 7", "Basic 8", "Basic 9"
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Calculate stats
  const totalStudents = students.length;
  const totalResults = results.length;
  const currentYearResults = results.filter(r => r.academicYear === getCurrentAcademicYear());
  const passedResults = currentYearResults.filter(r => r.isPassed);
  const passRate = currentYearResults.length > 0 ? Math.round((passedResults.length / currentYearResults.length) * 100) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Results Management</h1>
          <p className="text-gray-600 mt-2">Upload and manage student results</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-blue-600">{totalStudents}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <GraduationCap className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Results</p>
                <p className="text-2xl font-bold text-green-600">{totalResults}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Year</p>
                <p className="text-2xl font-bold text-purple-600">{currentYearResults.length}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <Upload className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pass Rate</p>
                <p className="text-2xl font-bold text-orange-600">{passRate}%</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <Filter className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="relative flex-1 min-w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search students by name or class..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/50 backdrop-blur-sm border-white/30"
              />
            </div>
            <Select value={filterClass} onValueChange={(value) => setFilterClass(value as ClassLevel | "all")}>
              <SelectTrigger className="w-48 bg-white/50 backdrop-blur-sm border-white/30">
                <SelectValue placeholder="Filter by class" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                {allClassLevels.map(classLevel => (
                  <SelectItem key={classLevel} value={classLevel}>
                    {classLevel}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={filterTerm} onValueChange={(value) => setFilterTerm(value as Term | "all")}>
              <SelectTrigger className="w-32 bg-white/50 backdrop-blur-sm border-white/30">
                <SelectValue placeholder="Term" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Terms</SelectItem>
                <SelectItem value="First">First Term</SelectItem>
                <SelectItem value="Second">Second Term</SelectItem>
                <SelectItem value="Third">Third Term</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>



      {/* Students List */}
      <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl overflow-hidden">
        <CardHeader className="p-6 border-b border-gray-200">
          <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <GraduationCap className="w-6 h-6 text-blue-600" />
            Students ({filteredStudents.length})
          </CardTitle>
        </CardHeader>

        <div className="max-h-96 overflow-y-auto">
          {filteredStudents.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No students found matching your criteria.</p>
            </div>
          ) : (
            filteredStudents.map((student, index) => {
              const studentResults = results.filter(r => r.studentId === student.id);
              const latestResults = studentResults.filter(r => r.academicYear === getCurrentAcademicYear());
              const canUpload = canUploadForClass(user?.role || "", user?.assignedClasses || [], student.class as ClassLevel);

              return (
                <div
                  key={student.id}
                  className={`flex items-center justify-between p-4 hover:bg-white/50 transition-all duration-200 ${
                    index !== filteredStudents.length - 1 ? 'border-b border-gray-100' : ''
                  }`}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                      {student.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-1">
                        <h3 className="font-semibold text-gray-900 truncate">{student.name}</h3>
                        <Badge variant="outline" className="text-xs">
                          {student.class}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <FileText className="w-4 h-4 text-green-600" />
                          {latestResults.length} results
                        </span>

                        {latestResults.length > 0 && (
                          <div className="flex gap-1">
                            {latestResults.slice(0, 3).map(result => (
                              <Badge
                                key={result.id}
                                style={{ backgroundColor: getGradeColor(result.grade), color: 'white' }}
                                className="text-xs"
                              >
                                {result.subjectCode}
                              </Badge>
                            ))}
                            {latestResults.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{latestResults.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <Button
                    size="sm"
                    onClick={() => handleSelectStudent(student)}
                    disabled={!canUpload}
                    className={`flex items-center space-x-2 ml-4 ${
                      canUpload
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                        : ''
                    }`}
                  >
                    <Upload className="w-4 h-4" />
                    <span className="text-sm font-medium">Upload</span>
                  </Button>
                </div>
              )
            })
          )}
        </div>
      </Card>

      {/* Recent Results */}
      {filteredResults.length > 0 && (
        <Card className="bg-white/70 backdrop-blur-xl border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3">
              <FileText className="w-6 h-6 text-blue-600" />
              Recent Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredResults.slice(0, 10).map(result => (
                <div key={result.id} className="flex justify-between items-center p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/70 transition-all duration-200">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm">
                      {result.studentName.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{result.studentName}</p>
                      <p className="text-sm text-gray-600 flex items-center gap-2">
                        <span>{result.subjectName}</span>
                        <span>•</span>
                        <span>{result.classLevel}</span>
                        <span>•</span>
                        <span>{result.term} Term</span>
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      style={{ backgroundColor: getGradeColor(result.grade), color: 'white' }}
                      className="text-sm font-semibold"
                    >
                      {result.grade}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">
                      {result.marks}/{result.totalMarks} ({result.percentage}%)
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Result Modal */}
      <Dialog open={showUploadModal} onOpenChange={setShowUploadModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Upload Result for {selectedStudent?.name || ''}</DialogTitle>
          </DialogHeader>

          {selectedStudent && (
            <form onSubmit={handleUploadResult} className="space-y-4">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-200">
                <p className="text-sm font-semibold text-gray-700">Student: {selectedStudent.name}</p>
                <p className="text-xs text-gray-600">Class: {selectedStudent.class}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Select
                    value={uploadData.subjectId}
                    onValueChange={(value) => setUploadData(prev => ({ ...prev, subjectId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableSubjects().map(subject => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name} ({subject.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="term">Term</Label>
                  <Select
                    value={uploadData.term}
                    onValueChange={(value) => setUploadData(prev => ({ ...prev, term: value as Term }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="First">First Term</SelectItem>
                      <SelectItem value="Second">Second Term</SelectItem>
                      <SelectItem value="Third">Third Term</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="academicYear">Academic Year</Label>
                  <Input
                    id="academicYear"
                    value={uploadData.academicYear}
                    onChange={(e) => setUploadData(prev => ({ ...prev, academicYear: e.target.value }))}
                    placeholder="2025-2026"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="marks">Marks Obtained</Label>
                  <Input
                    id="marks"
                    type="number"
                    min="0"
                    value={uploadData.marks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, marks: e.target.value }))}
                    placeholder="Enter marks"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="totalMarks">Total Marks</Label>
                  <Input
                    id="totalMarks"
                    type="number"
                    min="1"
                    value={uploadData.totalMarks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, totalMarks: e.target.value }))}
                    placeholder="100"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="remarks">Remarks (Optional)</Label>
                  <Textarea
                    id="remarks"
                    value={uploadData.remarks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}
                    placeholder="Additional remarks..."
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="teacherComment">Teacher Comment (Optional)</Label>
                  <Textarea
                    id="teacherComment"
                    value={uploadData.teacherComment}
                    onChange={(e) => setUploadData(prev => ({ ...prev, teacherComment: e.target.value }))}
                    placeholder="Teacher's comment..."
                    rows={2}
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-3 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetUploadForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Result
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
