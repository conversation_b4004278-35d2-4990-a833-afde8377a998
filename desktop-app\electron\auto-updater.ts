import { app, BrowserWindow, ipcMain, dialog } from 'electron';
import pkg from 'electron-updater';
const { autoUpdater } = pkg;
import log from 'electron-log';

// Configure logging
log.transports.file.level = 'info';
autoUpdater.logger = log;

export interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseNotes?: string;
  downloadSize?: number;
}

export interface UpdateProgress {
  bytesPerSecond: number;
  percent: number;
  transferred: number;
  total: number;
}

export interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  updateInfo: UpdateInfo | null;
  progress: UpdateProgress | null;
}

// Auto-Updater Service for Main Process
export class AutoUpdaterService {
  private mainWindow: BrowserWindow | null = null;
  private updateStatus: UpdateStatus = {
    checking: false,
    available: false,
    downloading: false,
    downloaded: false,
    error: null,
    updateInfo: null,
    progress: null
  };

  constructor() {
    this.setupAutoUpdater();
    this.setupIpcHandlers();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupAutoUpdater(): void {
    // Configure auto-updater
    autoUpdater.autoDownload = false; // We'll control when to download
    autoUpdater.autoInstallOnAppQuit = true;

    // Set update server (GitHub releases)
    // This will be automatically configured from package.json publish settings
    // But you can override it here if needed
    try {
      autoUpdater.setFeedURL({
        provider: 'github',
        owner: 'your-username', // Replace with your actual GitHub username
        repo: 'School-Management-Web-App-', // Your repository name
        private: false // Set to true if private repo
      });
      console.log('✅ Auto-updater feed URL configured');
    } catch (error) {
      console.warn('⚠️ Failed to set auto-updater feed URL:', error);
    }

    // Auto-updater event handlers
    autoUpdater.on('checking-for-update', () => {
      console.log('🔄 Checking for updates...');
      this.updateStatus.checking = true;
      this.updateStatus.error = null;
      this.notifyRenderer('update-checking');
    });

    autoUpdater.on('update-available', (info) => {
      console.log('✅ Update available:', info.version);
      this.updateStatus.checking = false;
      this.updateStatus.available = true;
      this.updateStatus.updateInfo = {
        version: info.version,
        releaseDate: info.releaseDate,
        releaseNotes: info.releaseNotes,
        downloadSize: info.files?.[0]?.size
      };
      this.notifyRenderer('update-available', this.updateStatus.updateInfo);
      this.showUpdateDialog(this.updateStatus.updateInfo);
    });

    autoUpdater.on('update-not-available', (info) => {
      console.log('ℹ️ No updates available. Current version:', info.version);
      this.updateStatus.checking = false;
      this.updateStatus.available = false;
      this.notifyRenderer('update-not-available');
    });

    autoUpdater.on('error', (error) => {
      console.error('❌ Auto-updater error:', error);
      this.updateStatus.checking = false;
      this.updateStatus.downloading = false;
      this.updateStatus.error = error.message;
      this.notifyRenderer('update-error', error.message);
    });

    autoUpdater.on('download-progress', (progress) => {
      console.log(`📥 Download progress: ${Math.round(progress.percent)}%`);
      this.updateStatus.progress = progress;
      this.notifyRenderer('update-download-progress', progress);
    });

    autoUpdater.on('update-downloaded', (info) => {
      console.log('✅ Update downloaded:', info.version);
      this.updateStatus.downloading = false;
      this.updateStatus.downloaded = true;
      this.notifyRenderer('update-downloaded', this.updateStatus.updateInfo);
      this.showRestartDialog();
    });
  }

  private setupIpcHandlers(): void {
    // Check for updates
    ipcMain.handle('updater:checkForUpdates', async () => {
      try {
        await this.checkForUpdates();
        return { success: true };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    // Download update
    ipcMain.handle('updater:downloadUpdate', async () => {
      try {
        await this.downloadUpdate();
        return { success: true };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    // Install update and restart
    ipcMain.handle('updater:installUpdate', () => {
      this.installUpdate();
      return { success: true };
    });

    // Get update status
    ipcMain.handle('updater:getStatus', () => {
      return this.updateStatus;
    });

    // Set auto-update preferences
    ipcMain.handle('updater:setAutoUpdate', (event, enabled: boolean) => {
      // Store preference (you might want to save this to settings)
      console.log(`🔧 Auto-update ${enabled ? 'enabled' : 'disabled'}`);
      return { success: true };
    });

    console.log('✅ Auto-updater IPC handlers registered');
  }

  // Public methods
  async checkForUpdates(): Promise<void> {
    if (this.updateStatus.checking) {
      console.log('⚠️ Already checking for updates');
      return;
    }

    try {
      console.log('🔄 Manually checking for updates...');
      await autoUpdater.checkForUpdates();
    } catch (error) {
      console.error('❌ Failed to check for updates:', error);
      throw error;
    }
  }

  async downloadUpdate(): Promise<void> {
    if (!this.updateStatus.available) {
      throw new Error('No update available to download');
    }

    if (this.updateStatus.downloading) {
      console.log('⚠️ Already downloading update');
      return;
    }

    try {
      console.log('📥 Starting update download...');
      this.updateStatus.downloading = true;
      this.notifyRenderer('update-download-started');
      await autoUpdater.downloadUpdate();
    } catch (error) {
      this.updateStatus.downloading = false;
      console.error('❌ Failed to download update:', error);
      throw error;
    }
  }

  installUpdate(): void {
    if (!this.updateStatus.downloaded) {
      throw new Error('No update downloaded to install');
    }

    console.log('🔄 Installing update and restarting...');
    autoUpdater.quitAndInstall();
  }

  // UI Dialogs
  private async showUpdateDialog(updateInfo: UpdateInfo): Promise<void> {
    if (!this.mainWindow) return;

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'Update Available',
      message: `A new version (${updateInfo.version}) is available!`,
      detail: updateInfo.releaseNotes || 'Would you like to download and install it?',
      buttons: ['Download Now', 'Later', 'View Release Notes'],
      defaultId: 0,
      cancelId: 1
    });

    switch (result.response) {
      case 0: // Download Now
        await this.downloadUpdate();
        break;
      case 1: // Later
        console.log('ℹ️ User chose to update later');
        break;
      case 2: // View Release Notes
        // You could open a browser window or show more details
        console.log('ℹ️ User wants to view release notes');
        break;
    }
  }

  private async showRestartDialog(): Promise<void> {
    if (!this.mainWindow) return;

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'Update Ready',
      message: 'Update has been downloaded successfully!',
      detail: 'The application will restart to apply the update.',
      buttons: ['Restart Now', 'Restart Later'],
      defaultId: 0,
      cancelId: 1
    });

    if (result.response === 0) {
      this.installUpdate();
    } else {
      console.log('ℹ️ User chose to restart later');
    }
  }

  // Notify renderer process
  private notifyRenderer(event: string, data?: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(`updater:${event}`, data);
    }
  }

  // Auto-check for updates (call this periodically)
  startPeriodicCheck(intervalMinutes: number = 60): void {
    // Check immediately
    setTimeout(() => {
      this.checkForUpdates().catch(console.error);
    }, 5000); // Wait 5 seconds after app start

    // Then check periodically
    setInterval(() => {
      this.checkForUpdates().catch(console.error);
    }, intervalMinutes * 60 * 1000);

    console.log(`⏰ Auto-update check scheduled every ${intervalMinutes} minutes`);
  }

  // Get current app version
  getCurrentVersion(): string {
    return app.getVersion();
  }
}

// Global instance
let updaterServiceInstance: AutoUpdaterService | null = null;

export function initializeAutoUpdater(): AutoUpdaterService {
  if (!updaterServiceInstance) {
    updaterServiceInstance = new AutoUpdaterService();
  }
  return updaterServiceInstance;
}

export function getAutoUpdater(): AutoUpdaterService | null {
  return updaterServiceInstance;
}
