import { autoUpdater } from 'electron-updater';
import { BrowserWindow, ipcMain } from 'electron';
import log from 'electron-log';

// Configure logging
log.transports.file.level = 'info';
autoUpdater.logger = log;

export class AutoUpdaterService {
  private mainWindow: BrowserWindow | null = null;
  private updateStatus = {
    checking: false,
    available: false,
    downloading: false,
    downloaded: false,
    progress: null,
    error: null,
    updateInfo: null
  };

  constructor() {
    this.setupAutoUpdater();
    this.setupIpcHandlers();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupAutoUpdater(): void {
    // Detailed configuration
    autoUpdater.autoDownload = false;
    autoUpdater.autoInstallOnAppQuit = true;

    // Comprehensive event handling
    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for updates...');
      this.updateStatus.checking = true;
      this.updateStatus.error = null;
      this.notify<PERSON>ender<PERSON>('update-checking');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info.version);
      this.updateStatus.checking = false;
      this.updateStatus.available = true;
      this.updateStatus.updateInfo = {
        version: info.version,
        releaseDate: info.releaseDate,
        releaseNotes: info.releaseNotes,
        downloadSize: info.files?.[0]?.size
      };
      this.notifyRenderer('update-available', this.updateStatus.updateInfo);
      this.showUpdateDialog(this.updateStatus.updateInfo);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('No updates available. Current version:', info.version);
      this.updateStatus.checking = false;
      this.updateStatus.available = false;
      this.notifyRenderer('update-not-available');
    });

    autoUpdater.on('error', (error) => {
      log.error('Auto-updater error:', error);
      this.updateStatus.checking = false;
      this.updateStatus.downloading = false;
      this.updateStatus.error = error.message;
      this.notifyRenderer('update-error', error.message);
    });

    autoUpdater.on('download-progress', (progress) => {
      log.info(`Download progress: ${Math.round(progress.percent)}%`);
      this.updateStatus.progress = progress;
      this.notifyRenderer('update-download-progress', progress);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded:', info.version);
      this.updateStatus.downloading = false;
      this.updateStatus.downloaded = true;
      this.showRestartDialog();
    });
  }

  private setupIpcHandlers(): void {
    // Comprehensive IPC handlers for update management
    ipcMain.handle('updater:checkForUpdates', async () => {
      try {
        await this.checkForUpdates();
        return { success: true };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    ipcMain.handle('updater:downloadUpdate', async () => {
      try {
        await this.downloadUpdate();
        return { success: true };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    ipcMain.handle('updater:installUpdate', () => {
      this.installUpdate();
      return { success: true };
    });

    ipcMain.handle('updater:getStatus', () => {
      return this.updateStatus;
    });
  }

  async checkForUpdates(): Promise<void> {
    try {
      await autoUpdater.checkForUpdatesAndNotify();
    } catch (error) {
      log.error('Check for updates failed:', error);
      throw error;
    }
  }

  async downloadUpdate(): Promise<void> {
    try {
      this.updateStatus.downloading = true;
      await autoUpdater.downloadUpdate();
    } catch (error) {
      log.error('Download update failed:', error);
      this.updateStatus.downloading = false;
      throw error;
    }
  }

  installUpdate(): void {
    autoUpdater.quitAndInstall();
  }

  private showUpdateDialog(updateInfo: any): void {
    if (!this.mainWindow) return;

    // Implement a dialog to show update details
    this.mainWindow.webContents.send('update-available', updateInfo);
  }

  private showRestartDialog(): void {
    if (!this.mainWindow) return;

    // Prompt user to restart and install update
    this.mainWindow.webContents.send('update-ready-to-install');
  }

  private notifyRenderer(event: string, data?: any): void {
    if (!this.mainWindow) return;
    this.mainWindow.webContents.send(`updater:${event}`, data);
  }

  // Periodic update checks
  startPeriodicCheck(intervalMinutes: number = 60): void {
    // Check immediately after startup
    setTimeout(() => {
      this.checkForUpdates().catch(log.error);
    }, 5000);

    // Periodic checks
    setInterval(() => {
      this.checkForUpdates().catch(log.error);
    }, intervalMinutes * 60 * 1000);

    log.info(`Periodic update checks scheduled every ${intervalMinutes} minutes`);
  }
}

// Singleton instance
let autoUpdaterInstance: AutoUpdaterService | null = null;

export function getAutoUpdaterService(): AutoUpdaterService {
  if (!autoUpdaterInstance) {
    autoUpdaterInstance = new AutoUpdaterService();
  }
  return autoUpdaterInstance;
}
