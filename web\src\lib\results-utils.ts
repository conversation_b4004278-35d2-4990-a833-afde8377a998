import {
  Grade,
  GradeScale,
  StudentResult,
  ClassLevel,
  DEFAULT_GRADE_SCALES,
  GRADE_POINTS,
  Subject
} from './results-types';

/**
 * Calculate grade based on marks and grade scale
 */
export function calculateGrade(marks: number, gradeScale: GradeScale): Grade {
  const { gradeRanges } = gradeScale;
  
  if (marks >= gradeRanges.A.min && marks <= gradeRanges.A.max) return 'A';
  if (marks >= gradeRanges.B.min && marks <= gradeRanges.B.max) return 'B';
  if (marks >= gradeRanges.C.min && marks <= gradeRanges.C.max) return 'C';
  if (marks >= gradeRanges.D.min && marks <= gradeRanges.D.max) return 'D';
  if (marks >= gradeRanges.E.min && marks <= gradeRanges.E.max) return 'E';
  return 'F';
}

/**
 * Calculate percentage from marks
 */
export function calculatePercentage(marks: number, totalMarks: number): number {
  if (totalMarks === 0) return 0;
  return Math.round((marks / totalMarks) * 100 * 100) / 100; // Round to 2 decimal places
}

/**
 * Check if student passed based on grade scale
 */
export function isPassed(marks: number, gradeScale: GradeScale): boolean {
  const percentage = calculatePercentage(marks, gradeScale.totalMarks);
  return percentage >= gradeScale.passMarkPercentage;
}

/**
 * Get class level category for grade scale
 */
export function getClassCategory(classLevel: ClassLevel): string {
  if (['Nursery 1', 'Nursery 2', 'KG 1', 'KG 2'].includes(classLevel)) {
    return 'Early Years';
  }
  if (['Basic 1', 'Basic 2', 'Basic 3', 'Basic 4', 'Basic 5', 'Basic 6'].includes(classLevel)) {
    return 'Primary';
  }
  return 'Junior High';
}

/**
 * Get default grade scale for a class level
 */
export function getDefaultGradeScale(classLevel: ClassLevel): GradeScale['gradeRanges'] {
  const category = getClassCategory(classLevel);
  return DEFAULT_GRADE_SCALES[category];
}

/**
 * Calculate overall grade from multiple results
 */
export function calculateOverallGrade(results: StudentResult[]): Grade {
  if (results.length === 0) return 'F';
  
  const totalPoints = results.reduce((sum, result) => sum + GRADE_POINTS[result.grade], 0);
  const averagePoints = totalPoints / results.length;
  
  if (averagePoints >= 3.5) return 'A';
  if (averagePoints >= 2.5) return 'B';
  if (averagePoints >= 1.5) return 'C';
  if (averagePoints >= 0.75) return 'D';
  if (averagePoints >= 0.25) return 'E';
  return 'F';
}

/**
 * Calculate class position for a student
 */
export function calculateClassPosition(
  studentResults: StudentResult[],
  allClassResults: StudentResult[][],
  _classLevel: ClassLevel
): { position: number; totalStudents: number } {
  // Calculate average percentage for each student
  const studentAverages = allClassResults.map(results => {
    const totalMarks = results.reduce((sum, r) => sum + r.totalMarks, 0);
    const totalObtained = results.reduce((sum, r) => sum + r.marks, 0);
    return totalMarks > 0 ? (totalObtained / totalMarks) * 100 : 0;
  });
  
  // Calculate current student's average
  const currentStudentTotal = studentResults.reduce((sum, r) => sum + r.totalMarks, 0);
  const currentStudentObtained = studentResults.reduce((sum, r) => sum + r.marks, 0);
  const currentAverage = currentStudentTotal > 0 ? (currentStudentObtained / currentStudentTotal) * 100 : 0;
  
  // Sort averages in descending order and find position
  const sortedAverages = [...studentAverages].sort((a, b) => b - a);
  const position = sortedAverages.findIndex(avg => avg <= currentAverage) + 1;
  
  return {
    position: position || studentAverages.length,
    totalStudents: studentAverages.length
  };
}

/**
 * Generate subject code from subject name
 */
export function generateSubjectCode(subjectName: string): string {
  return subjectName
    .toUpperCase()
    .replace(/[^A-Z\s]/g, '')
    .split(' ')
    .map(word => word.substring(0, 3))
    .join('')
    .substring(0, 6);
}

/**
 * Validate marks input
 */
export function validateMarks(marks: number, totalMarks: number): string[] {
  const errors: string[] = [];
  
  if (marks < 0) {
    errors.push('Marks cannot be negative');
  }
  
  if (marks > totalMarks) {
    errors.push(`Marks cannot exceed total marks (${totalMarks})`);
  }
  
  if (!Number.isInteger(marks)) {
    errors.push('Marks must be a whole number');
  }
  
  return errors;
}

/**
 * Format grade with color coding
 */
export function getGradeColor(grade: Grade): string {
  const colors = {
    A: '#10B981', // Green
    B: '#3B82F6', // Blue
    C: '#F59E0B', // Yellow
    D: '#F97316', // Orange
    E: '#EF4444', // Red
    F: '#DC2626'  // Dark Red
  };
  return colors[grade];
}

/**
 * Get subjects for a specific class level
 */
export function getSubjectsForClass(
  classLevel: ClassLevel,
  allSubjects: Subject[]
): Subject[] {
  return allSubjects.filter(subject => 
    subject.classLevels.includes(classLevel)
  );
}

/**
 * Check if user can upload results for a class
 */
export function canUploadForClass(
  userRole: string,
  userAssignedClasses: string[],
  targetClass: ClassLevel
): boolean {
  if (userRole === 'admin') return true;
  if (userRole === 'teacher') {
    return userAssignedClasses.includes(targetClass);
  }
  return false;
}

/**
 * Format result summary for display
 */
export function formatResultSummary(results: StudentResult[]): {
  totalSubjects: number;
  totalMarks: number;
  totalObtained: number;
  averagePercentage: number;
  passedSubjects: number;
  failedSubjects: number;
} {
  const totalSubjects = results.length;
  const totalMarks = results.reduce((sum, r) => sum + r.totalMarks, 0);
  const totalObtained = results.reduce((sum, r) => sum + r.marks, 0);
  const averagePercentage = totalMarks > 0 ? Math.round((totalObtained / totalMarks) * 100 * 100) / 100 : 0;
  const passedSubjects = results.filter(r => r.isPassed).length;
  const failedSubjects = totalSubjects - passedSubjects;
  
  return {
    totalSubjects,
    totalMarks,
    totalObtained,
    averagePercentage,
    passedSubjects,
    failedSubjects
  };
}

/**
 * Generate remarks based on grade
 */
export function generateRemarks(grade: Grade): string {
  const remarks = {
    A: 'Excellent performance! Keep up the outstanding work.',
    B: 'Very good performance! Continue working hard.',
    C: 'Good performance! There is room for improvement.',
    D: 'Fair performance. More effort is needed.',
    E: 'Below average performance. Significant improvement required.',
    F: 'Poor performance. Immediate attention and extra support needed.'
  };
  return remarks[grade];
}

/**
 * Sort results by various criteria
 */
export function sortResults(
  results: StudentResult[],
  sortBy: 'name' | 'marks' | 'grade' | 'subject',
  order: 'asc' | 'desc' = 'asc'
): StudentResult[] {
  return [...results].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.studentName.localeCompare(b.studentName);
        break;
      case 'marks':
        comparison = a.marks - b.marks;
        break;
      case 'grade':
        comparison = GRADE_POINTS[b.grade] - GRADE_POINTS[a.grade]; // Higher grade points first
        break;
      case 'subject':
        comparison = a.subjectName.localeCompare(b.subjectName);
        break;
    }
    
    return order === 'desc' ? -comparison : comparison;
  });
}
