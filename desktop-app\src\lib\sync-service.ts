import { 
  collection as firebaseCollection,
  doc as firebaseDoc,
  addDoc as firebaseAddDoc,
  setDoc as firebaseSetDoc,
  updateDoc as firebaseUpdateDoc,
  deleteDoc as firebaseDeleteDoc,
  getDocs as firebaseGetDocs,
  query as firebaseQuery,
  orderBy as firebaseOrderBy,
  where as firebaseWhere,
  Timestamp
} from 'firebase/firestore';
import { db as firebaseDb } from './firebase';
import { DatabaseService, SyncMetadata } from './database-service';
import { getNetworkMonitor } from './network-monitor';

// Types
export interface SyncResult {
  success: boolean;
  totalProcessed: number;
  created: number;
  updated: number;
  deleted: number;
  conflicts: number;
  errors: string[];
}

export interface SyncConflict {
  collection: string;
  documentId: string;
  localData: any;
  remoteData: any;
  localTimestamp: Date;
  remoteTimestamp: Date;
}

export interface SyncProgress {
  collection: string;
  processed: number;
  total: number;
  status: 'pending' | 'syncing' | 'completed' | 'error';
}

// Sync Service Class
export class SyncService {
  private databaseService: DatabaseService;
  private networkMonitor: ReturnType<typeof getNetworkMonitor>;
  private isSyncing: boolean = false;
  private syncProgress: Map<string, SyncProgress> = new Map();
  private readonly COLLECTIONS = ['users', 'students', 'results', 'feeStructures', 'studentPayments', 'subjects'];

  constructor(databaseService: DatabaseService) {
    this.databaseService = databaseService;
    this.networkMonitor = getNetworkMonitor();
    
    // Listen for network changes
    this.networkMonitor.onOnline(() => {
      console.log('🌐 Network online - starting sync...');
      this.performFullSync();
    });
  }

  // Main Sync Methods
  async performFullSync(): Promise<SyncResult> {
    if (this.isSyncing) {
      console.log('⏳ Sync already in progress');
      return this.createEmptyResult();
    }

    if (!this.networkMonitor.isOnline()) {
      console.log('📡 Cannot sync - offline');
      return this.createEmptyResult();
    }

    this.isSyncing = true;
    this.databaseService.setConnectionStatus('syncing');

    console.log('🔄 Starting full sync...');

    const result: SyncResult = {
      success: true,
      totalProcessed: 0,
      created: 0,
      updated: 0,
      deleted: 0,
      conflicts: 0,
      errors: []
    };

    try {
      // Step 1: Sync from Firebase to MongoDB (get latest remote changes)
      const firebaseToMongoResult = await this.syncFromFirebase();
      this.mergeResults(result, firebaseToMongoResult);

      // Step 2: Sync from MongoDB to Firebase (push local changes)
      const mongoToFirebaseResult = await this.syncToFirebase();
      this.mergeResults(result, mongoToFirebaseResult);

      // Step 3: Clear sync queue
      this.databaseService.clearSyncQueue();

      console.log('✅ Full sync completed:', result);
      this.databaseService.setConnectionStatus('online');

    } catch (error) {
      console.error('❌ Full sync failed:', error);
      result.success = false;
      result.errors.push(error.message);
      this.databaseService.setConnectionStatus('error');
    } finally {
      this.isSyncing = false;
    }

    return result;
  }

  async syncFromFirebase(): Promise<SyncResult> {
    console.log('⬇️ Syncing from Firebase to MongoDB...');

    const result = this.createEmptyResult();

    for (const collectionName of this.COLLECTIONS) {
      try {
        const collectionResult = await this.syncCollectionFromFirebase(collectionName);
        this.mergeResults(result, collectionResult);
      } catch (error) {
        console.error(`❌ Failed to sync collection ${collectionName} from Firebase:`, error);
        result.errors.push(`${collectionName}: ${error.message}`);
      }
    }

    return result;
  }

  async syncToFirebase(): Promise<SyncResult> {
    console.log('⬆️ Syncing from MongoDB to Firebase...');

    const result = this.createEmptyResult();
    const syncQueue = this.databaseService.getSyncQueue();

    for (const queueItem of syncQueue) {
      try {
        const itemResult = await this.processSyncQueueItem(queueItem);
        this.mergeResults(result, itemResult);
      } catch (error) {
        console.error('❌ Failed to process sync queue item:', error);
        result.errors.push(`Queue item: ${error.message}`);
      }
    }

    return result;
  }

  // Collection-specific sync
  private async syncCollectionFromFirebase(collectionName: string): Promise<SyncResult> {
    const result = this.createEmptyResult();

    try {
      // Get all documents from Firebase
      const firebaseQuery = firebaseQuery(
        firebaseCollection(firebaseDb, collectionName),
        firebaseOrderBy('updatedAt', 'desc')
      );
      
      const firebaseSnapshot = await firebaseGetDocs(firebaseQuery);
      const firebaseDocs = firebaseSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      }));

      // Get corresponding documents from MongoDB
      const mongoDocs = await this.databaseService.read(collectionName);
      const mongoDocsMap = new Map(mongoDocs.map(doc => [doc.syncMeta?.firebaseId || doc.id, doc]));

      for (const firebaseDoc of firebaseDocs) {
        const mongoDoc = mongoDocsMap.get(firebaseDoc.id);

        if (!mongoDoc) {
          // Document doesn't exist in MongoDB - create it
          await this.createDocumentInMongo(collectionName, firebaseDoc);
          result.created++;
        } else {
          // Document exists - check for conflicts and update if needed
          const conflict = this.detectConflict(mongoDoc, firebaseDoc);
          
          if (conflict) {
            // Resolve conflict using last-write-wins
            const resolution = this.resolveConflict(conflict);
            
            if (resolution === 'remote') {
              await this.updateDocumentInMongo(collectionName, mongoDoc.id, firebaseDoc);
              result.updated++;
            }
            result.conflicts++;
          } else if (this.shouldUpdateFromFirebase(mongoDoc, firebaseDoc)) {
            await this.updateDocumentInMongo(collectionName, mongoDoc.id, firebaseDoc);
            result.updated++;
          }
        }

        result.totalProcessed++;
      }

    } catch (error) {
      result.errors.push(`Collection ${collectionName}: ${error.message}`);
      throw error;
    }

    return result;
  }

  private async processSyncQueueItem(queueItem: any): Promise<SyncResult> {
    const result = this.createEmptyResult();

    try {
      const { collection, operation, data } = queueItem;

      switch (operation) {
        case 'create':
          await this.createDocumentInFirebase(collection, data);
          result.created++;
          break;

        case 'update':
          await this.updateDocumentInFirebase(collection, data);
          result.updated++;
          break;

        case 'delete':
          await this.deleteDocumentInFirebase(collection, data);
          result.deleted++;
          break;

        default:
          throw new Error(`Unknown operation: ${operation}`);
      }

      result.totalProcessed++;

    } catch (error) {
      result.errors.push(`Queue item: ${error.message}`);
      throw error;
    }

    return result;
  }

  // Document operations
  private async createDocumentInMongo(collectionName: string, firebaseDoc: any): Promise<void> {
    const mongoDoc = {
      ...firebaseDoc,
      syncMeta: {
        lastModified: firebaseDoc.updatedAt || new Date(),
        source: 'firebase',
        syncStatus: 'synced',
        firebaseId: firebaseDoc.id
      }
    };

    // Remove Firebase-specific fields
    delete mongoDoc.id;
    
    await this.databaseService.create(collectionName, mongoDoc);
  }

  private async updateDocumentInMongo(collectionName: string, mongoId: string, firebaseDoc: any): Promise<void> {
    const updateData = {
      ...firebaseDoc,
      syncMeta: {
        lastModified: firebaseDoc.updatedAt || new Date(),
        source: 'firebase',
        syncStatus: 'synced',
        firebaseId: firebaseDoc.id
      }
    };

    // Remove Firebase-specific fields
    delete updateData.id;
    
    await this.databaseService.update(collectionName, mongoId, updateData);
  }

  private async createDocumentInFirebase(collectionName: string, mongoDoc: any): Promise<void> {
    const firebaseDoc = { ...mongoDoc };
    
    // Remove MongoDB-specific fields
    delete firebaseDoc._id;
    delete firebaseDoc.syncMeta;
    
    // Add Firebase timestamps
    firebaseDoc.createdAt = firebaseDoc.createdAt || new Date().toISOString();
    firebaseDoc.updatedAt = new Date().toISOString();

    const docRef = await firebaseAddDoc(firebaseCollection(firebaseDb, collectionName), firebaseDoc);
    
    // Update MongoDB document with Firebase ID
    await this.databaseService.update(collectionName, mongoDoc.id, {
      'syncMeta.firebaseId': docRef.id,
      'syncMeta.syncStatus': 'synced'
    });
  }

  private async updateDocumentInFirebase(collectionName: string, mongoDoc: any): Promise<void> {
    if (!mongoDoc.syncMeta?.firebaseId) {
      throw new Error('Cannot update Firebase document: missing Firebase ID');
    }

    const firebaseDoc = { ...mongoDoc };
    
    // Remove MongoDB-specific fields
    delete firebaseDoc._id;
    delete firebaseDoc.id;
    delete firebaseDoc.syncMeta;
    
    // Update timestamp
    firebaseDoc.updatedAt = new Date().toISOString();

    await firebaseUpdateDoc(
      firebaseDoc(firebaseDb, collectionName, mongoDoc.syncMeta.firebaseId),
      firebaseDoc
    );

    // Update sync status in MongoDB
    await this.databaseService.update(collectionName, mongoDoc.id, {
      'syncMeta.syncStatus': 'synced'
    });
  }

  private async deleteDocumentInFirebase(collectionName: string, data: any): Promise<void> {
    if (!data.firebaseId) {
      throw new Error('Cannot delete Firebase document: missing Firebase ID');
    }

    await firebaseDeleteDoc(firebaseDoc(firebaseDb, collectionName, data.firebaseId));
  }

  // Conflict detection and resolution
  private detectConflict(mongoDoc: any, firebaseDoc: any): SyncConflict | null {
    const mongoTimestamp = new Date(mongoDoc.syncMeta?.lastModified || 0);
    const firebaseTimestamp = new Date(firebaseDoc.updatedAt || 0);

    // Check if both documents were modified after last sync
    if (mongoDoc.syncMeta?.source === 'mongodb' && 
        firebaseTimestamp > mongoTimestamp &&
        mongoTimestamp > new Date(mongoDoc.syncMeta?.lastSync || 0)) {
      
      return {
        collection: 'unknown', // Will be set by caller
        documentId: mongoDoc.id,
        localData: mongoDoc,
        remoteData: firebaseDoc,
        localTimestamp: mongoTimestamp,
        remoteTimestamp: firebaseTimestamp
      };
    }

    return null;
  }

  private resolveConflict(conflict: SyncConflict): 'local' | 'remote' {
    // Last-write-wins strategy
    return conflict.remoteTimestamp > conflict.localTimestamp ? 'remote' : 'local';
  }

  private shouldUpdateFromFirebase(mongoDoc: any, firebaseDoc: any): boolean {
    const mongoTimestamp = new Date(mongoDoc.syncMeta?.lastModified || 0);
    const firebaseTimestamp = new Date(firebaseDoc.updatedAt || 0);

    return firebaseTimestamp > mongoTimestamp;
  }

  // Utility methods
  private createEmptyResult(): SyncResult {
    return {
      success: true,
      totalProcessed: 0,
      created: 0,
      updated: 0,
      deleted: 0,
      conflicts: 0,
      errors: []
    };
  }

  private mergeResults(target: SyncResult, source: SyncResult): void {
    target.totalProcessed += source.totalProcessed;
    target.created += source.created;
    target.updated += source.updated;
    target.deleted += source.deleted;
    target.conflicts += source.conflicts;
    target.errors.push(...source.errors);
    target.success = target.success && source.success;
  }

  // Public status methods
  isSyncInProgress(): boolean {
    return this.isSyncing;
  }

  getSyncProgress(): Map<string, SyncProgress> {
    return new Map(this.syncProgress);
  }
}

// Singleton instance
let syncServiceInstance: SyncService | null = null;

export function getSyncService(databaseService: DatabaseService): SyncService {
  if (!syncServiceInstance) {
    syncServiceInstance = new SyncService(databaseService);
  }
  return syncServiceInstance;
}
