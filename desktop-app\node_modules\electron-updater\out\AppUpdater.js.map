{"version": 3, "file": "AppUpdater.js", "sourceRoot": "", "sources": ["../src/AppUpdater.ts"], "names": [], "mappings": ";;;AAAA,+DAa6B;AAC7B,mCAAoC;AACpC,2BAA4B;AAC5B,mCAAqC;AACrC,uCAAsE;AAEtE,qCAA8B;AAC9B,uCAA+B;AAC/B,6BAA4B;AAC5B,mCAA8K;AAE9K,qEAAuF;AACvF,6DAAyD;AACzD,iEAA2F;AAC3F,iEAA6D;AAC7D,uDAAwF;AAKxF,+BAAiC;AACjC,iCAAsC;AAEtC,0GAAsG;AACtG,mCAAuJ;AAevJ,MAAsB,UAAW,SAAS,qBAAyD;IA4EjG;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO,CAAC,KAAoB;QAC9B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC1B,qCAAqC;YACrC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAA,+BAAQ,EAAC,sCAAsC,KAAK,EAAE,EAAE,6BAA6B,CAAC,CAAA;YAC9F,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAA,+BAAQ,EAAC,qCAAqC,EAAE,6BAA6B,CAAC,CAAA;YACtF,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;IAC5B,CAAC;IAOD;;OAEG;IACH,aAAa,CAAC,KAAa;QACzB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE;YAC3D,aAAa,EAAE,KAAK;SACrB,CAAC,CAAA;IACJ,CAAC;IAID,yDAAyD;IACzD,IAAI,UAAU;QACZ,OAAO,IAAA,oCAAa,GAAE,CAAA;IACxB,CAAC;IAED;;;OAGG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED,IAAI,MAAM,CAAC,KAAoB;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;IACzD,CAAC;IAUD,qCAAqC;IACrC;;;OAGG;IACH,IAAI,gBAAgB,CAAC,KAAoB;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,eAAI,CAAM,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;IAClE,CAAC;IAID;;;OAGG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAChC,CAAC;IAED,IAAI,iBAAiB,CAAC,KAA0B;QAC9C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;QACjC,CAAC;IACH,CAAC;IAoBD,YAAsB,OAA6C,EAAE,GAAgB;QACnF,KAAK,EAAE,CAAA;QA5LT;;;WAGG;QACH,iBAAY,GAAG,IAAI,CAAA;QAEnB;;;WAGG;QACH,yBAAoB,GAAG,IAAI,CAAA;QAE3B;;;WAGG;QACH,2BAAsB,GAAG,IAAI,CAAA;QAE7B;;;;WAIG;QACH,oBAAe,GAAG,KAAK,CAAA;QAEvB;;;WAGG;QACH,kBAAa,GAAG,KAAK,CAAA;QAErB;;;;;;WAMG;QACH,mBAAc,GAAG,KAAK,CAAA;QAEtB;;;;;;;WAOG;QACH,wBAAmB,GAAG,KAAK,CAAA;QAE3B;;;;WAIG;QACH,gCAA2B,GAAG,KAAK,CAAA;QAEnC;;;;;;WAMG;QACH,yBAAoB,GAAG,KAAK,CAAA;QAOpB,aAAQ,GAAkB,IAAI,CAAA;QAE5B,2BAAsB,GAAkC,IAAI,CAAA;QA4BtE;;WAEG;QACH,mBAAc,GAA+B,IAAI,CAAA;QAWvC,YAAO,GAAW,OAAO,CAAA;QAmBnC,qCAAqC;QACrC;;WAEG;QACM,YAAO,GAAG,IAAI,qBAAa,CAAC,IAAI,CAAC,CAAA;QAElC,yBAAoB,GAAkB,IAAI,CAAA;QAaxC,uBAAkB,GAAwB,CAAC,UAAsB,EAA8B,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAgB3I,kBAAa,GAAkC,IAAI,CAAA;QAExC,yBAAoB,GAAG,IAAI,eAAI,CAAS,GAAG,EAAE,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAA;QAEjG,8CAA8C;QAC9C,gBAAgB;QAChB,iBAAY,GAAG,IAAI,eAAI,CAAM,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAEnD,2BAAsB,GAAsC,IAAI,CAAA;QAChE,oBAAe,GAAkC,IAAI,CAAA;QAInD,0BAAqB,GAAiC,IAAI,CAAA;QAoapE;;;WAGG;QACH,qBAAgB,GAAkC,IAAI,CAAA;QAhapD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9D,CAAC,CAAC,CAAA;QAEF,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,IAAI,CAAC,GAAG,GAAG,IAAI,uCAAkB,EAAE,CAAA;YACnC,IAAI,CAAC,YAAY,GAAG,IAAI,2CAAoB,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC9G,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;YACd,IAAI,CAAC,YAAY,GAAG,IAAW,CAAA;QACjC,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAA;QAC7C,MAAM,cAAc,GAAG,IAAA,cAAY,EAAC,oBAAoB,CAAC,CAAA;QACzD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAA,+BAAQ,EAAC,+CAA+C,oBAAoB,GAAG,EAAE,6BAA6B,CAAC,CAAA;QACvH,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QACpC,IAAI,CAAC,eAAe,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAA;QAE9D,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YAExB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC1D,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,UAAU;QACR,OAAO,4BAA4B,CAAA;IACrC,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,OAA0D;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAA;QAC1D,oEAAoE;QACpE,IAAI,QAAuB,CAAA;QAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,QAAQ,GAAG,IAAI,iCAAe,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE;gBAC1E,GAAG,cAAc;gBACjB,yBAAyB,EAAE,IAAA,wDAAsC,EAAC,OAAO,CAAC;aAC3E,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,IAAA,8BAAY,EAAC,OAAO,EAAE,IAAI,EAAE,cAAc,CAAC,CAAA;QACxD,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAChD,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9B,CAAC;QAED,IAAI,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAA;QACxD,IAAI,sBAAsB,IAAI,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAA;YAC9D,OAAO,sBAAsB,CAAA;QAC/B,CAAC;QAED,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAA;QAEjE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QACxC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,EAAE;aAC9C,IAAI,CAAC,EAAE,CAAC,EAAE;YACT,cAAc,EAAE,CAAA;YAChB,OAAO,EAAE,CAAA;QACX,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YAChB,cAAc,EAAE,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,6BAA6B,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YAC/E,MAAM,CAAC,CAAA;QACT,CAAC,CAAC,CAAA;QAEJ,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAA;QACpD,OAAO,sBAAsB,CAAA;IAC/B,CAAC;IAEM,eAAe;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAA;QAClE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4FAA4F,CAAC,CAAA;YAC/G,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qCAAqC;IACrC,wBAAwB,CAAC,oBAA2C;QAClE,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACtC,IAAI,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,eAAe,CAAA,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;oBAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAA;gBAChF,CAAC;gBACD,OAAO,EAAE,CAAA;YACX,CAAC;YAED,KAAK,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE;gBAChC,MAAM,mBAAmB,GAAG,UAAU,CAAC,0BAA0B,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAA;gBAC7H,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAA;YACpE,CAAC,CAAC,CAAA;YAEF,OAAO,EAAE,CAAA;QACX,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,OAAe,EAAE,OAAe,EAAE,oBAA2C;QACrH,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACjC,oBAAoB,GAAG;gBACrB,KAAK,EAAE,kCAAkC;gBACzC,IAAI,EAAE,6FAA6F;aACpG,CAAA;QACH,CAAC;QACD,oBAAoB,GAAG;YACrB,KAAK,EAAE,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;YAC7F,IAAI,EAAE,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;SAC5F,CAAA;QACD,OAAO,oBAAoB,CAAA;IAC7B,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAsB;QACjD,MAAM,oBAAoB,GAAG,UAAU,CAAC,iBAAiB,CAAA;QACzD,IAAI,iBAAiB,GAAG,oBAAoB,CAAA;QAC5C,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,iBAAiB,GAAG,QAAQ,CAAC,iBAAwB,EAAE,EAAE,CAAC,CAAA;QAC1D,IAAI,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,oBAAoB,EAAE,CAAC,CAAA;YACvE,OAAO,IAAI,CAAA;QACb,CAAC;QAED,0CAA0C;QAC1C,iBAAiB,GAAG,iBAAiB,GAAG,GAAG,CAAA;QAE3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAA;QAC3D,MAAM,GAAG,GAAG,2BAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;QACtD,MAAM,UAAU,GAAG,GAAG,GAAG,UAAU,CAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,iBAAiB,iBAAiB,UAAU,cAAc,aAAa,EAAE,CAAC,CAAA;QACnH,OAAO,UAAU,GAAG,iBAAiB,CAAA;IACvC,CAAC;IAEO,mBAAmB,CAAC,OAA4B;QACtD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;QAC7C,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,UAAsB;QACpD,MAAM,aAAa,GAAG,IAAA,cAAY,EAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QACtD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,IAAA,+BAAQ,EACZ,wHAAwH,UAAU,CAAC,OAAO,GAAG,EAC7I,6BAA6B,CAC9B,CAAA;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;QAC1C,IAAI,IAAA,WAAe,EAAC,aAAa,EAAE,cAAc,CAAC,EAAE,CAAC;YACnD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;QAC5D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,KAAK,CAAA;QACd,CAAC;QAED,yFAAyF;QACzF,yFAAyF;QACzF,MAAM,oBAAoB,GAAG,IAAA,WAAoB,EAAC,aAAa,EAAE,cAAc,CAAC,CAAA;QAChF,MAAM,oBAAoB,GAAG,IAAA,WAAiB,EAAC,aAAa,EAAE,cAAc,CAAC,CAAA;QAE7E,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,IAAI,oBAAoB,CAAA;IACpD,CAAC;IAEO,sBAAsB,CAAC,UAAsB;QACnD,MAAM,oBAAoB,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,oBAAoB,CAAA;QAC7D,MAAM,gBAAgB,GAAG,IAAA,YAAO,GAAE,CAAA;QAClC,IAAI,oBAAoB,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,IAAI,IAAA,WAAiB,EAAC,gBAAgB,EAAE,oBAAoB,CAAC,EAAE,CAAC;oBAC9D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,gBAAgB,iDAAiD,oBAAoB,gBAAgB,gBAAgB,EAAE,CAAC,CAAA;oBAChK,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wCAAwC,gBAAgB,6BAA6B,oBAAoB,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YACjK,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,KAAK,CAAC,wBAAwB;QACtC,MAAM,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA;QAE1B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAA,8BAAY,EAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAA;QACtH,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAA;QACvC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAA;QAC3D,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,EAAE,aAAa,EAAE,CAAC,CAAC,CAAA;QAC1F,OAAO;YACL,IAAI,EAAE,MAAM,MAAM,CAAC,gBAAgB,EAAE;YACrC,QAAQ,EAAE,MAAM;SACjB,CAAA;IACH,CAAC;IAEO,4BAA4B;QAClC,OAAO;YACL,yBAAyB,EAAE,IAAI;YAC/B,QAAQ,EAAE,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAE,OAAO,CAAC,QAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YACjH,QAAQ,EAAE,IAAI,CAAC,YAAY;SAC5B,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAEhC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;QACpD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,sBAAsB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,sCAAsC,UAAU,CAAC,OAAO,kBACxG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YACpC,IAAI,CACL,CAAA;YACD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAA;YAC7C,OAAO;gBACL,iBAAiB,EAAE,KAAK;gBACxB,WAAW,EAAE,UAAU;gBACvB,UAAU;aACX,CAAA;QACH,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAA;QACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QAElC,MAAM,iBAAiB,GAAG,IAAI,wCAAiB,EAAE,CAAA;QACjD,8BAA8B;QAC9B,OAAO;YACL,iBAAiB,EAAE,IAAI;YACvB,WAAW,EAAE,UAAU;YACvB,UAAU;YACV,iBAAiB;YACjB,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI;SACnF,CAAA;IACH,CAAC;IAES,iBAAiB,CAAC,UAAsB;QAChD,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,iBAAiB,UAAU,CAAC,OAAO,UAAU,IAAA,8BAAO,EAAC,UAAU,CAAC,KAAK,CAAC;aACnE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;aACjB,IAAI,CAAC,IAAI,CAAC,GAAG,CACjB,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAA;IAC3C,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,oBAAuC,IAAI,wCAAiB,EAAE;QAC3E,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAA;QACxD,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YACzB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAA;YAC7D,OAAO,IAAI,CAAC,eAAe,CAAA;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,2BAA2B,IAAA,8BAAO,EAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;aACjE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;aACjB,IAAI,CAAC,IAAI,CAAC,EAAE,CAChB,CAAA;QACD,MAAM,YAAY,GAAG,CAAC,CAAQ,EAAS,EAAE;YACvC,2FAA2F;YAC3F,IAAI,CAAC,CAAC,CAAC,YAAY,wCAAiB,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC;oBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;gBACvB,CAAC;gBAAC,OAAO,WAAgB,EAAE,CAAC;oBAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,WAAW,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC,CAAA;gBACvF,CAAC;YACH,CAAC;YAED,OAAO,CAAC,CAAA;QACV,CAAC,CAAA;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC3C,qBAAqB;YACrB,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAC;YAC1E,iBAAiB;YACjB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,2BAA2B,EAAE,IAAI,CAAC,2BAA2B;SAC9D,CAAC;aACC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YAChB,MAAM,YAAY,CAAC,CAAC,CAAC,CAAA;QACvB,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEJ,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAES,aAAa,CAAC,CAAQ;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;IAClD,CAAC;IAES,wBAAwB,CAAC,KAA4B;QAC7D,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,KAAK,CAAC,CAAA;IACrC,CAAC;IAiBO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAA;QAC1D,CAAC;QACD,OAAO,IAAA,cAAI,EAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAA;IACjE,CAAC;IAEO,qBAAqB,CAAC,QAAuB;QACnD,MAAM,wBAAwB,GAAG,QAAQ,CAAC,wBAAwB,CAAA;QAClE,IAAI,wBAAwB,IAAI,IAAI,EAAE,CAAC;YACrC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;YAC1C,OAAO,cAAc,IAAI,IAAI;gBAC3B,CAAC,CAAC,wBAAwB;gBAC1B,CAAC,CAAC;oBACE,GAAG,wBAAwB;oBAC3B,GAAG,cAAc;iBAClB,CAAA;QACP,CAAC;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;IACpD,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QAC3D,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,EAAE,OAAO,CAAC,CAAA;YACxC,IAAI,2BAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,OAAO,EAAE,CAAA;YACX,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yDAAyD,EAAE,EAAE,CAAC,CAAA;YAClF,CAAC;QACH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,EAAE,CAAC,CAAA;YAChF,CAAC;QACH,CAAC;QAED,MAAM,EAAE,GAAG,2BAAI,CAAC,EAAE,CAAC,IAAA,oBAAW,EAAC,IAAI,CAAC,EAAE,2BAAI,CAAC,GAAG,CAAC,CAAA;QAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAA;QACzD,IAAI,CAAC;YACH,MAAM,IAAA,qBAAU,EAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAC5B,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,EAAE,CAAC,CAAA;QAC/D,CAAC;QACD,OAAO,EAAE,CAAA;IACX,CAAC;IAED,gBAAgB;IAChB,IAAI,iBAAiB;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAA;QACnC,oEAAoE;QACpE,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,GAAG,UAAU,CAAC,WAAW,EAAE,CAAA;YAClC,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,KAAK,eAAe,EAAE,CAAC;gBACnD,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAQO,KAAK,CAAC,yBAAyB;QACrC,IAAI,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAA;QACxC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAA;YACnE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,CAAC,KAAK,CAAC,+GAA+G,CAAC,CAAA;YAC/H,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC5E,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;gBACzB,MAAM,CAAC,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAA;YAChD,CAAC;YAED,MAAM,GAAG,IAAI,+CAAsB,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAA;QACtC,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,WAAiC;QAC/D,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAA;QACrC,MAAM,eAAe,GAAoB;YACvC,OAAO,EAAE,WAAW,CAAC,qBAAqB,CAAC,cAAc;YACzD,iBAAiB,EAAE,WAAW,CAAC,qBAAqB,CAAC,iBAAiB;YACtE,IAAI,EAAG,QAAQ,CAAC,IAAY,CAAC,IAAI;YACjC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;SAC7B,CAAA;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,yBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,EAAE,CAAC,CAAA;QACrE,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAA;QAC/E,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAA;QAClC,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;QAExC,SAAS,sBAAsB;YAC7B,0CAA0C;YAC1C,MAAM,OAAO,GAAG,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YACrE,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC/B,CAAC;iBAAM,CAAC;gBACN,kCAAkC;gBAClC,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAA;YACtC,CAAC;QACH,CAAC;QAED,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAA;QACrE,MAAM,QAAQ,GAAG,sBAAsB,CAAC,wBAAwB,CAAA;QAChE,MAAM,IAAA,gBAAK,EAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1C,MAAM,cAAc,GAAG,sBAAsB,EAAE,CAAA;QAC/C,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACpD,MAAM,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,CAAA;QAEpI,MAAM,IAAI,GAAG,KAAK,EAAE,WAAoB,EAAE,EAAE;YAC1C,MAAM,sBAAsB,CAAC,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW,CAAC,CAAA;YAC1H,MAAM,WAAW,CAAC,IAAK,CAAC;gBACtB,GAAG,UAAU;gBACb,cAAc,EAAE,UAAU;aAC3B,CAAC,CAAA;YACF,OAAO,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QACvE,CAAC,CAAA;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;QACxB,MAAM,gBAAgB,GAAG,MAAM,sBAAsB,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;QACnH,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC7B,UAAU,GAAG,gBAAgB,CAAA;YAC7B,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;YACjC,MAAM,sBAAsB,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;gBAC9C,SAAS;YACX,CAAC,CAAC,CAAA;YACF,OAAO,MAAM,IAAA,iBAAM,EAAC,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACzC,SAAS;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,MAAM,cAAc,GAAG,MAAM,IAAA,6CAAoB,EAAC,QAAQ,cAAc,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC1F,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,CAAC,CAAA;YACrF,MAAM,IAAA,4BAAK,EACT,GAAG,EAAE,CAAC,IAAA,iBAAM,EAAC,cAAc,EAAE,UAAU,CAAC,EACxC,EAAE,EACF,GAAG,EACH,CAAC,EACD,CAAC,EACD,KAAK,CAAC,EAAE,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CACjE,CAAA;QACH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,eAAe,EAAE,CAAA;YAEvB,IAAI,CAAC,YAAY,wCAAiB,EAAE,CAAC;gBACnC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACrB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAA;YAC3C,CAAC;YACD,MAAM,CAAC,CAAA;QACT,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,eAAe,OAAO,2BAA2B,UAAU,EAAE,CAAC,CAAA;QACvE,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IACS,KAAK,CAAC,6BAA6B,CAC3C,QAAgC,EAChC,qBAA4C,EAC5C,aAAqB,EACrB,QAAuB,EACvB,oBAA4B;QAE5B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;gBACtF,OAAO,IAAI,CAAA;YACb,CAAC;YACD,MAAM,gBAAgB,GAAG,IAAA,oBAAa,EAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAChI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,gBAAgB,CAAC,CAAC,CAAC,WAAW,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAErG,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAQ,EAAqB,EAAE;gBAC7D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE;oBACzD,OAAO,EAAE,qBAAqB,CAAC,cAAc;oBAC7C,iBAAiB,EAAE,qBAAqB,CAAC,iBAAiB;iBAC3D,CAAC,CAAA;gBAEF,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtC,MAAM,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,YAAY,CAAC,CAAA;gBACpD,CAAC;gBAED,IAAI,CAAC;oBACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAU,EAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;gBAChD,CAAC;gBAAC,OAAO,CAAM,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,IAAI,aAAa,CAAC,EAAE,CAAC,CAAA;gBACrE,CAAC;YACH,CAAC,CAAA;YAED,MAAM,eAAe,GAAkC;gBACrD,MAAM,EAAE,QAAQ,CAAC,GAAG;gBACpB,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAuB,CAAC,QAAQ,EAAE,oBAAoB,CAAC;gBAC/E,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,OAAO,EAAE,aAAa;gBACtB,yBAAyB,EAAE,QAAQ,CAAC,yBAAyB;gBAC7D,cAAc,EAAE,qBAAqB,CAAC,cAAc;gBACpD,iBAAiB,EAAE,qBAAqB,CAAC,iBAAiB;aAC3D,CAAA;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,yBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,EAAE,CAAC,CAAA;YACrE,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1F,MAAM,IAAI,6DAA6B,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;YAC7I,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;YAChG,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;gBAClC,YAAY;gBACZ,MAAM,CAAC,CAAA;YACT,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;CACF;AAjwBD,gCAiwBC;AAUD,SAAS,uBAAuB,CAAC,OAAe;IAC9C,MAAM,0BAA0B,GAAG,IAAA,mBAA4B,EAAC,OAAO,CAAC,CAAA;IACxE,OAAO,0BAA0B,IAAI,IAAI,IAAI,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAA;AACpF,CAAC;AAED,eAAe;AACf,MAAa,UAAU;IACrB,6DAA6D;IAC7D,IAAI,CAAC,OAAa;QAChB,SAAS;IACX,CAAC;IAED,6DAA6D;IAC7D,IAAI,CAAC,OAAa;QAChB,SAAS;IACX,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,OAAa;QACjB,SAAS;IACX,CAAC;CACF;AAfD,gCAeC", "sourcesContent": ["import {\n  AllPublishOptions,\n  asArray,\n  CancellationToken,\n  newError,\n  PublishConfiguration,\n  UpdateInfo,\n  UUID,\n  DownloadOptions,\n  CancellationError,\n  ProgressInfo,\n  BlockMap,\n  retry,\n} from \"builder-util-runtime\"\nimport { randomBytes } from \"crypto\"\nimport { release } from \"os\"\nimport { EventEmitter } from \"events\"\nimport { mkdir, outputFile, readFile, rename, unlink } from \"fs-extra\"\nimport { OutgoingHttpHeaders } from \"http\"\nimport { load } from \"js-yaml\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { eq as isVersionsEqual, gt as isVersionGreaterThan, lt as isVersionLessThan, parse as parseVersion, prerelease as getVersionPreleaseComponents, SemVer } from \"semver\"\nimport { AppAdapter } from \"./AppAdapter\"\nimport { createTempUpdateFile, DownloadedUpdateHelper } from \"./DownloadedUpdateHelper\"\nimport { ElectronAppAdapter } from \"./ElectronAppAdapter\"\nimport { ElectronHttpExecutor, getNetSession, LoginCallback } from \"./electronHttpExecutor\"\nimport { GenericProvider } from \"./providers/GenericProvider\"\nimport { createClient, isUrlProbablySupportMultiRangeRequests } from \"./providerFactory\"\nimport { Provider, ProviderPlatform } from \"./providers/Provider\"\nimport type { TypedEmitter } from \"tiny-typed-emitter\"\nimport Session = Electron.Session\nimport type { AuthInfo } from \"electron\"\nimport { gunzipSync } from \"zlib\"\nimport { blockmapFiles } from \"./util\"\nimport { DifferentialDownloaderOptions } from \"./differentialDownloader/DifferentialDownloader\"\nimport { GenericDifferentialDownloader } from \"./differentialDownloader/GenericDifferentialDownloader\"\nimport { DOWNLOAD_PROGRESS, Logger, ResolvedUpdateFileInfo, UPDATE_DOWNLOADED, UpdateCheckResult, UpdateDownloadedEvent, UpdaterSignal } from \"./types\"\nimport { VerifyUpdateSupport } from \"./main\"\n\nexport type AppUpdaterEvents = {\n  error: (error: Error, message?: string) => void\n  login: (info: AuthInfo, callback: LoginCallback) => void\n  \"checking-for-update\": () => void\n  \"update-not-available\": (info: UpdateInfo) => void\n  \"update-available\": (info: UpdateInfo) => void\n  \"update-downloaded\": (event: UpdateDownloadedEvent) => void\n  \"download-progress\": (info: ProgressInfo) => void\n  \"update-cancelled\": (info: UpdateInfo) => void\n  \"appimage-filename-updated\": (path: string) => void\n}\n\nexport abstract class AppUpdater extends (EventEmitter as new () => TypedEmitter<AppUpdaterEvents>) {\n  /**\n   * Whether to automatically download an update when it is found.\n   * @default true\n   */\n  autoDownload = true\n\n  /**\n   * Whether to automatically install a downloaded update on app quit (if `quitAndInstall` was not called before).\n   * @default true\n   */\n  autoInstallOnAppQuit = true\n\n  /**\n   * Whether to run the app after finish install when run the installer is NOT in silent mode.\n   * @default true\n   */\n  autoRunAppAfterInstall = true\n\n  /**\n   * *GitHub provider only.* Whether to allow update to pre-release versions. Defaults to `true` if application version contains prerelease components (e.g. `0.12.1-alpha.1`, here `alpha` is a prerelease component), otherwise `false`.\n   *\n   * If `true`, downgrade will be allowed (`allowDowngrade` will be set to `true`).\n   */\n  allowPrerelease = false\n\n  /**\n   * *GitHub provider only.* Get all release notes (from current version to latest), not just the latest.\n   * @default false\n   */\n  fullChangelog = false\n\n  /**\n   * Whether to allow version downgrade (when a user from the beta channel wants to go back to the stable channel).\n   *\n   * Taken in account only if channel differs (pre-release version component in terms of semantic versioning).\n   *\n   * @default false\n   */\n  allowDowngrade = false\n\n  /**\n   * Web installer files might not have signature verification, this switch prevents to load them unless it is needed.\n   *\n   * Currently false to prevent breaking the current API, but it should be changed to default true at some point that\n   * breaking changes are allowed.\n   *\n   * @default false\n   */\n  disableWebInstaller = false\n\n  /**\n   * *NSIS only* Disable differential downloads and always perform full download of installer.\n   *\n   * @default false\n   */\n  disableDifferentialDownload = false\n\n  /**\n   * Allows developer to force the updater to work in \"dev\" mode, looking for \"dev-app-update.yml\" instead of \"app-update.yml\"\n   * Dev: `path.join(this.app.getAppPath(), \"dev-app-update.yml\")`\n   * Prod: `path.join(process.resourcesPath!, \"app-update.yml\")`\n   *\n   * @default false\n   */\n  forceDevUpdateConfig = false\n\n  /**\n   * The current application version.\n   */\n  readonly currentVersion: SemVer\n\n  private _channel: string | null = null\n\n  protected downloadedUpdateHelper: DownloadedUpdateHelper | null = null\n\n  /**\n   * Get the update channel. Doesn't return `channel` from the update configuration, only if was previously set.\n   */\n  get channel(): string | null {\n    return this._channel\n  }\n\n  /**\n   * Set the update channel. Overrides `channel` in the update configuration.\n   *\n   * `allowDowngrade` will be automatically set to `true`. If this behavior is not suitable for you, simple set `allowDowngrade` explicitly after.\n   */\n  set channel(value: string | null) {\n    if (this._channel != null) {\n      // noinspection SuspiciousTypeOfGuard\n      if (typeof value !== \"string\") {\n        throw newError(`Channel must be a string, but got: ${value}`, \"ERR_UPDATER_INVALID_CHANNEL\")\n      } else if (value.length === 0) {\n        throw newError(`Channel must be not an empty string`, \"ERR_UPDATER_INVALID_CHANNEL\")\n      }\n    }\n\n    this._channel = value\n    this.allowDowngrade = true\n  }\n\n  /**\n   *  The request headers.\n   */\n  requestHeaders: OutgoingHttpHeaders | null = null\n\n  /**\n   *  Shortcut for explicitly adding auth tokens to request headers\n   */\n  addAuthHeader(token: string) {\n    this.requestHeaders = Object.assign({}, this.requestHeaders, {\n      authorization: token,\n    })\n  }\n\n  protected _logger: Logger = console\n\n  // noinspection JSMethodCanBeStatic,JSUnusedGlobalSymbols\n  get netSession(): Session {\n    return getNetSession()\n  }\n\n  /**\n   * The logger. You can pass [electron-log](https://github.com/megahertz/electron-log), [winston](https://github.com/winstonjs/winston) or another logger with the following interface: `{ info(), warn(), error() }`.\n   * Set it to `null` if you would like to disable a logging feature.\n   */\n  get logger(): Logger | null {\n    return this._logger\n  }\n\n  set logger(value: Logger | null) {\n    this._logger = value == null ? new NoOpLogger() : value\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  /**\n   * For type safety you can use signals, e.g. `autoUpdater.signals.updateDownloaded(() => {})` instead of `autoUpdater.on('update-available', () => {})`\n   */\n  readonly signals = new UpdaterSignal(this)\n\n  private _appUpdateConfigPath: string | null = null\n\n  // noinspection JSUnusedGlobalSymbols\n  /**\n   * test only\n   * @private\n   */\n  set updateConfigPath(value: string | null) {\n    this.clientPromise = null\n    this._appUpdateConfigPath = value\n    this.configOnDisk = new Lazy<any>(() => this.loadUpdateConfig())\n  }\n\n  protected _isUpdateSupported: VerifyUpdateSupport = (updateInfo: UpdateInfo): boolean | Promise<boolean> => this.checkIfUpdateSupported(updateInfo)\n\n  /**\n   * Allows developer to override default logic for determining if an update is supported.\n   * The default logic compares the `UpdateInfo` minimum system version against the `os.release()` with `semver` package\n   */\n  get isUpdateSupported(): VerifyUpdateSupport {\n    return this._isUpdateSupported\n  }\n\n  set isUpdateSupported(value: VerifyUpdateSupport) {\n    if (value) {\n      this._isUpdateSupported = value\n    }\n  }\n\n  private clientPromise: Promise<Provider<any>> | null = null\n\n  protected readonly stagingUserIdPromise = new Lazy<string>(() => this.getOrCreateStagingUserId())\n\n  // public, allow to read old config for anyone\n  /** @internal */\n  configOnDisk = new Lazy<any>(() => this.loadUpdateConfig())\n\n  private checkForUpdatesPromise: Promise<UpdateCheckResult> | null = null\n  private downloadPromise: Promise<Array<string>> | null = null\n\n  protected readonly app: AppAdapter\n\n  protected updateInfoAndProvider: UpdateInfoAndProvider | null = null\n\n  /** @internal */\n  readonly httpExecutor: ElectronHttpExecutor\n\n  protected constructor(options: AllPublishOptions | null | undefined, app?: AppAdapter) {\n    super()\n\n    this.on(\"error\", (error: Error) => {\n      this._logger.error(`Error: ${error.stack || error.message}`)\n    })\n\n    if (app == null) {\n      this.app = new ElectronAppAdapter()\n      this.httpExecutor = new ElectronHttpExecutor((authInfo, callback) => this.emit(\"login\", authInfo, callback))\n    } else {\n      this.app = app\n      this.httpExecutor = null as any\n    }\n\n    const currentVersionString = this.app.version\n    const currentVersion = parseVersion(currentVersionString)\n    if (currentVersion == null) {\n      throw newError(`App version is not a valid semver version: \"${currentVersionString}\"`, \"ERR_UPDATER_INVALID_VERSION\")\n    }\n    this.currentVersion = currentVersion\n    this.allowPrerelease = hasPrereleaseComponents(currentVersion)\n\n    if (options != null) {\n      this.setFeedURL(options)\n\n      if (typeof options !== \"string\" && options.requestHeaders) {\n        this.requestHeaders = options.requestHeaders\n      }\n    }\n  }\n\n  //noinspection JSMethodCanBeStatic,JSUnusedGlobalSymbols\n  getFeedURL(): string | null | undefined {\n    return \"Deprecated. Do not use it.\"\n  }\n\n  /**\n   * Configure update provider. If value is `string`, [GenericServerOptions](./publish.md#genericserveroptions) will be set with value as `url`.\n   * @param options If you want to override configuration in the `app-update.yml`.\n   */\n  setFeedURL(options: PublishConfiguration | AllPublishOptions | string) {\n    const runtimeOptions = this.createProviderRuntimeOptions()\n    // https://github.com/electron-userland/electron-builder/issues/1105\n    let provider: Provider<any>\n    if (typeof options === \"string\") {\n      provider = new GenericProvider({ provider: \"generic\", url: options }, this, {\n        ...runtimeOptions,\n        isUseMultipleRangeRequest: isUrlProbablySupportMultiRangeRequests(options),\n      })\n    } else {\n      provider = createClient(options, this, runtimeOptions)\n    }\n    this.clientPromise = Promise.resolve(provider)\n  }\n\n  /**\n   * Asks the server whether there is an update.\n   * @returns null if the updater is disabled, otherwise info about the latest version\n   */\n  checkForUpdates(): Promise<UpdateCheckResult | null> {\n    if (!this.isUpdaterActive()) {\n      return Promise.resolve(null)\n    }\n\n    let checkForUpdatesPromise = this.checkForUpdatesPromise\n    if (checkForUpdatesPromise != null) {\n      this._logger.info(\"Checking for update (already in progress)\")\n      return checkForUpdatesPromise\n    }\n\n    const nullizePromise = () => (this.checkForUpdatesPromise = null)\n\n    this._logger.info(\"Checking for update\")\n    checkForUpdatesPromise = this.doCheckForUpdates()\n      .then(it => {\n        nullizePromise()\n        return it\n      })\n      .catch((e: any) => {\n        nullizePromise()\n        this.emit(\"error\", e, `Cannot check for updates: ${(e.stack || e).toString()}`)\n        throw e\n      })\n\n    this.checkForUpdatesPromise = checkForUpdatesPromise\n    return checkForUpdatesPromise\n  }\n\n  public isUpdaterActive(): boolean {\n    const isEnabled = this.app.isPackaged || this.forceDevUpdateConfig\n    if (!isEnabled) {\n      this._logger.info(\"Skip checkForUpdates because application is not packed and dev update config is not forced\")\n      return false\n    }\n    return true\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  checkForUpdatesAndNotify(downloadNotification?: DownloadNotification): Promise<UpdateCheckResult | null> {\n    return this.checkForUpdates().then(it => {\n      if (!it?.downloadPromise) {\n        if (this._logger.debug != null) {\n          this._logger.debug(\"checkForUpdatesAndNotify called, downloadPromise is null\")\n        }\n        return it\n      }\n\n      void it.downloadPromise.then(() => {\n        const notificationContent = AppUpdater.formatDownloadNotification(it.updateInfo.version, this.app.name, downloadNotification)\n        new (require(\"electron\").Notification)(notificationContent).show()\n      })\n\n      return it\n    })\n  }\n\n  private static formatDownloadNotification(version: string, appName: string, downloadNotification?: DownloadNotification): DownloadNotification {\n    if (downloadNotification == null) {\n      downloadNotification = {\n        title: \"A new update is ready to install\",\n        body: `{appName} version {version} has been downloaded and will be automatically installed on exit`,\n      }\n    }\n    downloadNotification = {\n      title: downloadNotification.title.replace(\"{appName}\", appName).replace(\"{version}\", version),\n      body: downloadNotification.body.replace(\"{appName}\", appName).replace(\"{version}\", version),\n    }\n    return downloadNotification\n  }\n\n  private async isStagingMatch(updateInfo: UpdateInfo): Promise<boolean> {\n    const rawStagingPercentage = updateInfo.stagingPercentage\n    let stagingPercentage = rawStagingPercentage\n    if (stagingPercentage == null) {\n      return true\n    }\n\n    stagingPercentage = parseInt(stagingPercentage as any, 10)\n    if (isNaN(stagingPercentage)) {\n      this._logger.warn(`Staging percentage is NaN: ${rawStagingPercentage}`)\n      return true\n    }\n\n    // convert from user 0-100 to internal 0-1\n    stagingPercentage = stagingPercentage / 100\n\n    const stagingUserId = await this.stagingUserIdPromise.value\n    const val = UUID.parse(stagingUserId).readUInt32BE(12)\n    const percentage = val / 0xffffffff\n    this._logger.info(`Staging percentage: ${stagingPercentage}, percentage: ${percentage}, user id: ${stagingUserId}`)\n    return percentage < stagingPercentage\n  }\n\n  private computeFinalHeaders(headers: OutgoingHttpHeaders) {\n    if (this.requestHeaders != null) {\n      Object.assign(headers, this.requestHeaders)\n    }\n    return headers\n  }\n\n  private async isUpdateAvailable(updateInfo: UpdateInfo): Promise<boolean> {\n    const latestVersion = parseVersion(updateInfo.version)\n    if (latestVersion == null) {\n      throw newError(\n        `This file could not be downloaded, or the latest version (from update server) does not have a valid semver version: \"${updateInfo.version}\"`,\n        \"ERR_UPDATER_INVALID_VERSION\"\n      )\n    }\n\n    const currentVersion = this.currentVersion\n    if (isVersionsEqual(latestVersion, currentVersion)) {\n      return false\n    }\n\n    if (!(await Promise.resolve(this.isUpdateSupported(updateInfo)))) {\n      return false\n    }\n\n    const isStagingMatch = await this.isStagingMatch(updateInfo)\n    if (!isStagingMatch) {\n      return false\n    }\n\n    // https://github.com/electron-userland/electron-builder/pull/3111#issuecomment-405033227\n    // https://github.com/electron-userland/electron-builder/pull/3111#issuecomment-405030797\n    const isLatestVersionNewer = isVersionGreaterThan(latestVersion, currentVersion)\n    const isLatestVersionOlder = isVersionLessThan(latestVersion, currentVersion)\n\n    if (isLatestVersionNewer) {\n      return true\n    }\n    return this.allowDowngrade && isLatestVersionOlder\n  }\n\n  private checkIfUpdateSupported(updateInfo: UpdateInfo) {\n    const minimumSystemVersion = updateInfo?.minimumSystemVersion\n    const currentOSVersion = release()\n    if (minimumSystemVersion) {\n      try {\n        if (isVersionLessThan(currentOSVersion, minimumSystemVersion)) {\n          this._logger.info(`Current OS version ${currentOSVersion} is less than the minimum OS version required ${minimumSystemVersion} for version ${currentOSVersion}`)\n          return false\n        }\n      } catch (e: any) {\n        this._logger.warn(`Failed to compare current OS version(${currentOSVersion}) with minimum OS version(${minimumSystemVersion}): ${(e.message || e).toString()}`)\n      }\n    }\n    return true\n  }\n\n  protected async getUpdateInfoAndProvider(): Promise<UpdateInfoAndProvider> {\n    await this.app.whenReady()\n\n    if (this.clientPromise == null) {\n      this.clientPromise = this.configOnDisk.value.then(it => createClient(it, this, this.createProviderRuntimeOptions()))\n    }\n\n    const client = await this.clientPromise\n    const stagingUserId = await this.stagingUserIdPromise.value\n    client.setRequestHeaders(this.computeFinalHeaders({ \"x-user-staging-id\": stagingUserId }))\n    return {\n      info: await client.getLatestVersion(),\n      provider: client,\n    }\n  }\n\n  private createProviderRuntimeOptions() {\n    return {\n      isUseMultipleRangeRequest: true,\n      platform: this._testOnlyOptions == null ? (process.platform as ProviderPlatform) : this._testOnlyOptions.platform,\n      executor: this.httpExecutor,\n    }\n  }\n\n  private async doCheckForUpdates(): Promise<UpdateCheckResult> {\n    this.emit(\"checking-for-update\")\n\n    const result = await this.getUpdateInfoAndProvider()\n    const updateInfo = result.info\n    if (!(await this.isUpdateAvailable(updateInfo))) {\n      this._logger.info(\n        `Update for version ${this.currentVersion.format()} is not available (latest version: ${updateInfo.version}, downgrade is ${\n          this.allowDowngrade ? \"allowed\" : \"disallowed\"\n        }).`\n      )\n      this.emit(\"update-not-available\", updateInfo)\n      return {\n        isUpdateAvailable: false,\n        versionInfo: updateInfo,\n        updateInfo,\n      }\n    }\n\n    this.updateInfoAndProvider = result\n    this.onUpdateAvailable(updateInfo)\n\n    const cancellationToken = new CancellationToken()\n    //noinspection ES6MissingAwait\n    return {\n      isUpdateAvailable: true,\n      versionInfo: updateInfo,\n      updateInfo,\n      cancellationToken,\n      downloadPromise: this.autoDownload ? this.downloadUpdate(cancellationToken) : null,\n    }\n  }\n\n  protected onUpdateAvailable(updateInfo: UpdateInfo): void {\n    this._logger.info(\n      `Found version ${updateInfo.version} (url: ${asArray(updateInfo.files)\n        .map(it => it.url)\n        .join(\", \")})`\n    )\n    this.emit(\"update-available\", updateInfo)\n  }\n\n  /**\n   * Start downloading update manually. You can use this method if `autoDownload` option is set to `false`.\n   * @returns {Promise<Array<string>>} Paths to downloaded files.\n   */\n  downloadUpdate(cancellationToken: CancellationToken = new CancellationToken()): Promise<Array<string>> {\n    const updateInfoAndProvider = this.updateInfoAndProvider\n    if (updateInfoAndProvider == null) {\n      const error = new Error(\"Please check update first\")\n      this.dispatchError(error)\n      return Promise.reject(error)\n    }\n\n    if (this.downloadPromise != null) {\n      this._logger.info(\"Downloading update (already in progress)\")\n      return this.downloadPromise\n    }\n\n    this._logger.info(\n      `Downloading update from ${asArray(updateInfoAndProvider.info.files)\n        .map(it => it.url)\n        .join(\", \")}`\n    )\n    const errorHandler = (e: Error): Error => {\n      // https://github.com/electron-userland/electron-builder/issues/1150#issuecomment-436891159\n      if (!(e instanceof CancellationError)) {\n        try {\n          this.dispatchError(e)\n        } catch (nestedError: any) {\n          this._logger.warn(`Cannot dispatch error event: ${nestedError.stack || nestedError}`)\n        }\n      }\n\n      return e\n    }\n\n    this.downloadPromise = this.doDownloadUpdate({\n      updateInfoAndProvider,\n      requestHeaders: this.computeRequestHeaders(updateInfoAndProvider.provider),\n      cancellationToken,\n      disableWebInstaller: this.disableWebInstaller,\n      disableDifferentialDownload: this.disableDifferentialDownload,\n    })\n      .catch((e: any) => {\n        throw errorHandler(e)\n      })\n      .finally(() => {\n        this.downloadPromise = null\n      })\n\n    return this.downloadPromise\n  }\n\n  protected dispatchError(e: Error): void {\n    this.emit(\"error\", e, (e.stack || e).toString())\n  }\n\n  protected dispatchUpdateDownloaded(event: UpdateDownloadedEvent): void {\n    this.emit(UPDATE_DOWNLOADED, event)\n  }\n\n  protected abstract doDownloadUpdate(downloadUpdateOptions: DownloadUpdateOptions): Promise<Array<string>>\n\n  /**\n   * Restarts the app and installs the update after it has been downloaded.\n   * It should only be called after `update-downloaded` has been emitted.\n   *\n   * **Note:** `autoUpdater.quitAndInstall()` will close all application windows first and only emit `before-quit` event on `app` after that.\n   * This is different from the normal quit event sequence.\n   *\n   * @param isSilent *windows-only* Runs the installer in silent mode. Defaults to `false`.\n   * @param isForceRunAfter Run the app after finish even on silent install. Not applicable for macOS.\n   * Ignored if `isSilent` is set to `false`(In this case you can still set `autoRunAppAfterInstall` to `false` to prevent run the app after finish).\n   */\n  abstract quitAndInstall(isSilent?: boolean, isForceRunAfter?: boolean): void\n\n  private async loadUpdateConfig(): Promise<any> {\n    if (this._appUpdateConfigPath == null) {\n      this._appUpdateConfigPath = this.app.appUpdateConfigPath\n    }\n    return load(await readFile(this._appUpdateConfigPath, \"utf-8\"))\n  }\n\n  private computeRequestHeaders(provider: Provider<any>): OutgoingHttpHeaders {\n    const fileExtraDownloadHeaders = provider.fileExtraDownloadHeaders\n    if (fileExtraDownloadHeaders != null) {\n      const requestHeaders = this.requestHeaders\n      return requestHeaders == null\n        ? fileExtraDownloadHeaders\n        : {\n            ...fileExtraDownloadHeaders,\n            ...requestHeaders,\n          }\n    }\n    return this.computeFinalHeaders({ accept: \"*/*\" })\n  }\n\n  private async getOrCreateStagingUserId(): Promise<string> {\n    const file = path.join(this.app.userDataPath, \".updaterId\")\n    try {\n      const id = await readFile(file, \"utf-8\")\n      if (UUID.check(id)) {\n        return id\n      } else {\n        this._logger.warn(`Staging user id file exists, but content was invalid: ${id}`)\n      }\n    } catch (e: any) {\n      if (e.code !== \"ENOENT\") {\n        this._logger.warn(`Couldn't read staging user ID, creating a blank one: ${e}`)\n      }\n    }\n\n    const id = UUID.v5(randomBytes(4096), UUID.OID)\n    this._logger.info(`Generated new staging user ID: ${id}`)\n    try {\n      await outputFile(file, id)\n    } catch (e: any) {\n      this._logger.warn(`Couldn't write out staging user ID: ${e}`)\n    }\n    return id\n  }\n\n  /** @internal */\n  get isAddNoCacheQuery(): boolean {\n    const headers = this.requestHeaders\n    // https://github.com/electron-userland/electron-builder/issues/3021\n    if (headers == null) {\n      return true\n    }\n\n    for (const headerName of Object.keys(headers)) {\n      const s = headerName.toLowerCase()\n      if (s === \"authorization\" || s === \"private-token\") {\n        return false\n      }\n    }\n    return true\n  }\n\n  /**\n   * @private\n   * @internal\n   */\n  _testOnlyOptions: TestOnlyUpdaterOptions | null = null\n\n  private async getOrCreateDownloadHelper(): Promise<DownloadedUpdateHelper> {\n    let result = this.downloadedUpdateHelper\n    if (result == null) {\n      const dirName = (await this.configOnDisk.value).updaterCacheDirName\n      const logger = this._logger\n      if (dirName == null) {\n        logger.error(\"updaterCacheDirName is not specified in app-update.yml Was app build using at least electron-builder 20.34.0?\")\n      }\n      const cacheDir = path.join(this.app.baseCachePath, dirName || this.app.name)\n      if (logger.debug != null) {\n        logger.debug(`updater cache dir: ${cacheDir}`)\n      }\n\n      result = new DownloadedUpdateHelper(cacheDir)\n      this.downloadedUpdateHelper = result\n    }\n    return result\n  }\n\n  protected async executeDownload(taskOptions: DownloadExecutorTask): Promise<Array<string>> {\n    const fileInfo = taskOptions.fileInfo\n    const downloadOptions: DownloadOptions = {\n      headers: taskOptions.downloadUpdateOptions.requestHeaders,\n      cancellationToken: taskOptions.downloadUpdateOptions.cancellationToken,\n      sha2: (fileInfo.info as any).sha2,\n      sha512: fileInfo.info.sha512,\n    }\n\n    if (this.listenerCount(DOWNLOAD_PROGRESS) > 0) {\n      downloadOptions.onProgress = it => this.emit(DOWNLOAD_PROGRESS, it)\n    }\n\n    const updateInfo = taskOptions.downloadUpdateOptions.updateInfoAndProvider.info\n    const version = updateInfo.version\n    const packageInfo = fileInfo.packageInfo\n\n    function getCacheUpdateFileName(): string {\n      // NodeJS URL doesn't decode automatically\n      const urlPath = decodeURIComponent(taskOptions.fileInfo.url.pathname)\n      if (urlPath.endsWith(`.${taskOptions.fileExtension}`)) {\n        return path.basename(urlPath)\n      } else {\n        // url like /latest, generate name\n        return taskOptions.fileInfo.info.url\n      }\n    }\n\n    const downloadedUpdateHelper = await this.getOrCreateDownloadHelper()\n    const cacheDir = downloadedUpdateHelper.cacheDirForPendingUpdate\n    await mkdir(cacheDir, { recursive: true })\n    const updateFileName = getCacheUpdateFileName()\n    let updateFile = path.join(cacheDir, updateFileName)\n    const packageFile = packageInfo == null ? null : path.join(cacheDir, `package-${version}${path.extname(packageInfo.path) || \".7z\"}`)\n\n    const done = async (isSaveCache: boolean) => {\n      await downloadedUpdateHelper.setDownloadedFile(updateFile, packageFile, updateInfo, fileInfo, updateFileName, isSaveCache)\n      await taskOptions.done!({\n        ...updateInfo,\n        downloadedFile: updateFile,\n      })\n      return packageFile == null ? [updateFile] : [updateFile, packageFile]\n    }\n\n    const log = this._logger\n    const cachedUpdateFile = await downloadedUpdateHelper.validateDownloadedPath(updateFile, updateInfo, fileInfo, log)\n    if (cachedUpdateFile != null) {\n      updateFile = cachedUpdateFile\n      return await done(false)\n    }\n\n    const removeFileIfAny = async () => {\n      await downloadedUpdateHelper.clear().catch(() => {\n        // ignore\n      })\n      return await unlink(updateFile).catch(() => {\n        // ignore\n      })\n    }\n\n    const tempUpdateFile = await createTempUpdateFile(`temp-${updateFileName}`, cacheDir, log)\n    try {\n      await taskOptions.task(tempUpdateFile, downloadOptions, packageFile, removeFileIfAny)\n      await retry(\n        () => rename(tempUpdateFile, updateFile),\n        60,\n        500,\n        0,\n        0,\n        error => error instanceof Error && /^EBUSY:/.test(error.message)\n      )\n    } catch (e: any) {\n      await removeFileIfAny()\n\n      if (e instanceof CancellationError) {\n        log.info(\"cancelled\")\n        this.emit(\"update-cancelled\", updateInfo)\n      }\n      throw e\n    }\n\n    log.info(`New version ${version} has been downloaded to ${updateFile}`)\n    return await done(true)\n  }\n  protected async differentialDownloadInstaller(\n    fileInfo: ResolvedUpdateFileInfo,\n    downloadUpdateOptions: DownloadUpdateOptions,\n    installerPath: string,\n    provider: Provider<any>,\n    oldInstallerFileName: string\n  ): Promise<boolean> {\n    try {\n      if (this._testOnlyOptions != null && !this._testOnlyOptions.isUseDifferentialDownload) {\n        return true\n      }\n      const blockmapFileUrls = blockmapFiles(fileInfo.url, this.app.version, downloadUpdateOptions.updateInfoAndProvider.info.version)\n      this._logger.info(`Download block maps (old: \"${blockmapFileUrls[0]}\", new: ${blockmapFileUrls[1]})`)\n\n      const downloadBlockMap = async (url: URL): Promise<BlockMap> => {\n        const data = await this.httpExecutor.downloadToBuffer(url, {\n          headers: downloadUpdateOptions.requestHeaders,\n          cancellationToken: downloadUpdateOptions.cancellationToken,\n        })\n\n        if (data == null || data.length === 0) {\n          throw new Error(`Blockmap \"${url.href}\" is empty`)\n        }\n\n        try {\n          return JSON.parse(gunzipSync(data).toString())\n        } catch (e: any) {\n          throw new Error(`Cannot parse blockmap \"${url.href}\", error: ${e}`)\n        }\n      }\n\n      const downloadOptions: DifferentialDownloaderOptions = {\n        newUrl: fileInfo.url,\n        oldFile: path.join(this.downloadedUpdateHelper!.cacheDir, oldInstallerFileName),\n        logger: this._logger,\n        newFile: installerPath,\n        isUseMultipleRangeRequest: provider.isUseMultipleRangeRequest,\n        requestHeaders: downloadUpdateOptions.requestHeaders,\n        cancellationToken: downloadUpdateOptions.cancellationToken,\n      }\n\n      if (this.listenerCount(DOWNLOAD_PROGRESS) > 0) {\n        downloadOptions.onProgress = it => this.emit(DOWNLOAD_PROGRESS, it)\n      }\n\n      const blockMapDataList = await Promise.all(blockmapFileUrls.map(u => downloadBlockMap(u)))\n      await new GenericDifferentialDownloader(fileInfo.info, this.httpExecutor, downloadOptions).download(blockMapDataList[0], blockMapDataList[1])\n      return false\n    } catch (e: any) {\n      this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack || e}`)\n      if (this._testOnlyOptions != null) {\n        // test mode\n        throw e\n      }\n      return true\n    }\n  }\n}\n\nexport interface DownloadUpdateOptions {\n  readonly updateInfoAndProvider: UpdateInfoAndProvider\n  readonly requestHeaders: OutgoingHttpHeaders\n  readonly cancellationToken: CancellationToken\n  readonly disableWebInstaller?: boolean\n  readonly disableDifferentialDownload?: boolean\n}\n\nfunction hasPrereleaseComponents(version: SemVer) {\n  const versionPrereleaseComponent = getVersionPreleaseComponents(version)\n  return versionPrereleaseComponent != null && versionPrereleaseComponent.length > 0\n}\n\n/** @private */\nexport class NoOpLogger implements Logger {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  info(message?: any) {\n    // ignore\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  warn(message?: any) {\n    // ignore\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  error(message?: any) {\n    // ignore\n  }\n}\n\nexport interface UpdateInfoAndProvider {\n  info: UpdateInfo\n  provider: Provider<any>\n}\n\nexport interface DownloadExecutorTask {\n  readonly fileExtension: string\n  readonly fileInfo: ResolvedUpdateFileInfo\n  readonly downloadUpdateOptions: DownloadUpdateOptions\n  readonly task: (destinationFile: string, downloadOptions: DownloadOptions, packageFile: string | null, removeTempDirIfAny: () => Promise<any>) => Promise<any>\n\n  readonly done?: (event: UpdateDownloadedEvent) => Promise<any>\n}\n\nexport interface DownloadNotification {\n  body: string\n  title: string\n}\n\n/** @private */\nexport interface TestOnlyUpdaterOptions {\n  platform: ProviderPlatform\n\n  isUseDifferentialDownload?: boolean\n}\n"]}