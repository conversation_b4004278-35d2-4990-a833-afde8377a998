
"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Student, Result, Term } from "@/lib/types";
import { Badge } from "../ui/badge";
import { Download, Pencil } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { Accordion, AccordionContent, AccordionI<PERSON>, AccordionTrigger } from "../ui/accordion";

const formSchema = z.object({
  subject: z.string().min(2, "Subject is required."),
  grade: z.string().min(1, "Grade is required."),
  term: z.nativeEnum(Term),
  year: z.string().min(4, "Year is required.").max(4, "Year must be 4 digits."),
});

type UploadResultFormValues = z.infer<typeof formSchema>;

interface UploadResultDialogProps {
  children: React.ReactNode;
  student: Student;
  onUpload: (studentId: string, result: Omit<Result, 'id'| 'studentId' | 'fileUrl' | 'date'>) => void;
  onUpdate: (studentId: string, resultId: string, result: Omit<Result, 'id'| 'studentId' | 'fileUrl' | 'date'>) => void;
}

export function UploadResultDialog({ children, student, onUpload, onUpdate }: UploadResultDialogProps) {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [editingResult, setEditingResult] = useState<Result | null>(null);

  const form = useForm<UploadResultFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: "",
      grade: "",
      term: Term.First,
      year: new Date().getFullYear().toString(),
    },
  });

  React.useEffect(() => {
    if (editingResult) {
      form.reset({
        subject: editingResult.subject,
        grade: editingResult.grade,
        term: editingResult.term,
        year: editingResult.year,
      });
    } else {
      form.reset({
        subject: "",
        grade: "",
        term: Term.First,
        year: new Date().getFullYear().toString(),
      });
    }
  }, [editingResult, form]);

  function onSubmit(values: UploadResultFormValues) {
    if (editingResult) {
        onUpdate(student.id, editingResult.id, values);
    } else {
        onUpload(student.id, values);
    }
    setEditingResult(null);
    form.reset();
    // Keep dialog open after submit to allow more actions
    // setOpen(false); 
  }

  const resultsByTerm = {
    [Term.First]: student.results.filter(r => r.term === Term.First),
    [Term.Second]: student.results.filter(r => r.term === Term.Second),
    [Term.Third]: student.results.filter(r => r.term === Term.Third),
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => { setOpen(isOpen); if (!isOpen) setEditingResult(null); }}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Results for {student.name}</DialogTitle>
          <DialogDescription>
            {editingResult ? `Editing result for ${editingResult.subject}` : user?.role === 'admin' ? "Add, edit or view results." : "View results."}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
            <h4 className="font-medium text-sm">Existing Results</h4>
            {student.results.length > 0 ? (
                <Accordion type="multiple" className="w-full">
                    {Object.values(Term).map(term => (
                        resultsByTerm[term].length > 0 && (
                            <AccordionItem key={term} value={term}>
                                <AccordionTrigger>{term} Term Results</AccordionTrigger>
                                <AccordionContent>
                                    <ul className="grid gap-3 pt-2">
                                        {resultsByTerm[term].map((result) => (
                                            <li key={result.id} className="flex items-center justify-between">
                                                <span className="text-muted-foreground text-sm">
                                                {result.year} - {result.subject}
                                                </span>
                                                <div className="flex items-center gap-2">
                                                    <Badge variant="outline">{result.grade}</Badge>
                                                    {user?.role === 'admin' && (
                                                        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setEditingResult(result)}>
                                                            <Pencil className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                    <Button variant="ghost" size="icon" className="h-6 w-6" asChild>
                                                        <a 
                                                            href={result.fileUrl} 
                                                            download
                                                            aria-label={`Download ${result.subject} result for ${result.year}`}
                                                            title={`Download ${result.subject} result for ${result.year}`}
                                                        >
                                                            <Download className="h-3 w-3"/>
                                                        </a>
                                                    </Button>
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </AccordionContent>
                            </AccordionItem>
                        )
                    ))}
                </Accordion>
             ) : (
               <p className="text-sm text-muted-foreground">No results have been uploaded for this student yet.</p>
             )}
        </div>
        
        {user?.role === 'admin' && (
            <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4 border-t">
                <h4 className="font-medium text-sm">{editingResult ? "Edit Result" : "Add New Result"}</h4>
                <div className="grid grid-cols-2 gap-4">
                    <FormField
                    control={form.control}
                    name="year"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Year</FormLabel>
                        <FormControl>
                            <Input placeholder="e.g. 2024" {...field} />
                        </FormControl>
                        <FormMessage />
                        </FormItem>
                    )}
                    />
                    <FormField
                    control={form.control}
                    name="term"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Term</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                            <SelectTrigger>
                                <SelectValue placeholder="Select term" />
                            </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                                <SelectItem value={Term.First}>First Term</SelectItem>
                                <SelectItem value={Term.Second}>Second Term</SelectItem>
                                <SelectItem value={Term.Third}>Third Term</SelectItem>
                            </SelectContent>
                        </Select>
                        <FormMessage />
                        </FormItem>
                    )}
                    />
                </div>
                <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>Subject</FormLabel>
                    <FormControl>
                        <Input placeholder="e.g. Mathematics" {...field} />
                    </FormControl>
                    <FormMessage />
                    </FormItem>
                )}
                />
                <FormField
                control={form.control}
                name="grade"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>Grade</FormLabel>
                    <FormControl>
                        <Input placeholder="e.g. A+" {...field} />
                    </FormControl>
                    <FormMessage />
                    </FormItem>
                )}
                />
                
                <DialogFooter>
                    {editingResult && (
                        <Button type="button" variant="ghost" onClick={() => setEditingResult(null)}>Cancel Edit</Button>
                    )}
                    <Button type="submit">{editingResult ? "Update Result" : "Add Result"}</Button>
                </DialogFooter>
            </form>
            </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
