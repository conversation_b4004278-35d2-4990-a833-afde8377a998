#!/bin/bash

echo "=== Apply security fixes properly ==="

# Add dist-electron to .gitignore
echo "dist-electron" >> desktop-app/.gitignore

# Remove the tracked dist files
git rm --cached desktop-app/dist-electron/main.js desktop-app/dist-electron/preload.mjs

# Stage the .gitignore changes
git add desktop-app/.gitignore

echo -e "\n=== Verify changes are staged ==="
git status

echo -e "\n=== Show what will be committed ==="
git diff --cached --name-only

echo -e "\n=== Verify .gitignore now contains dist-electron ==="
tail -5 desktop-app/.gitignore

echo -e "\n=== Ready to commit and push ==="
echo "The following changes are staged and ready:"
echo "1. Removed desktop-app/dist-electron/main.js from tracking"
echo "2. Removed desktop-app/dist-electron/preload.mjs from tracking" 
echo "3. Added 'dist-electron' to desktop-app/.gitignore"
echo ""
echo "To complete the push, you need to provide:"
echo "- Your GitHub username and personal access token, OR"
echo "- Configure SSH authentication"