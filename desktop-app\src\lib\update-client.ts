// Renderer-side update client for communicating with main process auto-updater

export interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseNotes?: string;
  downloadSize?: number;
}

export interface UpdateProgress {
  bytesPerSecond: number;
  percent: number;
  transferred: number;
  total: number;
}

export interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  updateInfo: UpdateInfo | null;
  progress: UpdateProgress | null;
}

export type UpdateEventCallback = (data?: any) => void;

// Update Client for Renderer Process
export class UpdateClient {
  private eventListeners: Map<string, Set<UpdateEventCallback>> = new Map();

  constructor() {
    // Check if we're in Electron environment
    if (!window.electronAPI) {
      console.warn('⚠️ Electron API not available - update functionality disabled');
      return;
    }

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!window.electronAPI) return;

    // Listen for update events from main process
    window.electronAPI.on('updater:update-checking', () => {
      this.emit('checking');
    });

    window.electronAPI.on('updater:update-available', (updateInfo: UpdateInfo) => {
      this.emit('available', updateInfo);
    });

    window.electronAPI.on('updater:update-not-available', () => {
      this.emit('not-available');
    });

    window.electronAPI.on('updater:update-error', (error: string) => {
      this.emit('error', error);
    });

    window.electronAPI.on('updater:update-download-started', () => {
      this.emit('download-started');
    });

    window.electronAPI.on('updater:update-download-progress', (progress: UpdateProgress) => {
      this.emit('download-progress', progress);
    });

    window.electronAPI.on('updater:update-downloaded', (updateInfo: UpdateInfo) => {
      this.emit('downloaded', updateInfo);
    });
  }

  // Update Operations
  async checkForUpdates(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      console.log('🔄 Checking for updates...');
      const result = await window.electronAPI.invoke('updater:checkForUpdates');
      
      if (result.success) {
        console.log('✅ Update check initiated');
      } else {
        console.error('❌ Update check failed:', result.error);
      }
      
      return result;

    } catch (error) {
      console.error('❌ Failed to check for updates:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async downloadUpdate(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      console.log('📥 Starting update download...');
      const result = await window.electronAPI.invoke('updater:downloadUpdate');
      
      if (result.success) {
        console.log('✅ Update download initiated');
      } else {
        console.error('❌ Update download failed:', result.error);
      }
      
      return result;

    } catch (error) {
      console.error('❌ Failed to download update:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async installUpdate(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      console.log('🔄 Installing update and restarting...');
      const result = await window.electronAPI.invoke('updater:installUpdate');
      
      // Note: App will restart, so this might not log
      if (result.success) {
        console.log('✅ Update installation initiated');
      }
      
      return result;

    } catch (error) {
      console.error('❌ Failed to install update:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getUpdateStatus(): Promise<UpdateStatus> {
    try {
      if (!window.electronAPI) {
        return {
          checking: false,
          available: false,
          downloading: false,
          downloaded: false,
          error: 'Electron API not available',
          updateInfo: null,
          progress: null
        };
      }

      return await window.electronAPI.invoke('updater:getStatus');

    } catch (error) {
      console.error('❌ Failed to get update status:', error);
      return {
        checking: false,
        available: false,
        downloading: false,
        downloaded: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        updateInfo: null,
        progress: null
      };
    }
  }

  async setAutoUpdate(enabled: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.invoke('updater:setAutoUpdate', enabled);
      console.log(`🔧 Auto-update ${enabled ? 'enabled' : 'disabled'}`);
      return result;

    } catch (error) {
      console.error('❌ Failed to set auto-update preference:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Event Management
  on(event: string, callback: UpdateEventCallback): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    
    this.eventListeners.get(event)!.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.off(event, callback);
    };
  }

  off(event: string, callback: UpdateEventCallback): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in update event callback for ${event}:`, error);
        }
      });
    }
  }

  // Utility Methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatProgress(progress: UpdateProgress): string {
    const percent = Math.round(progress.percent);
    const transferred = this.formatFileSize(progress.transferred);
    const total = this.formatFileSize(progress.total);
    const speed = this.formatFileSize(progress.bytesPerSecond) + '/s';
    
    return `${percent}% (${transferred}/${total}) at ${speed}`;
  }

  // Cleanup
  cleanup(): void {
    console.log('🔄 Cleaning up update client...');
    this.eventListeners.clear();
    console.log('✅ Update client cleaned up');
  }
}

// Singleton instance
let updateClientInstance: UpdateClient | null = null;

export function getUpdateClient(): UpdateClient {
  if (!updateClientInstance) {
    updateClientInstance = new UpdateClient();
  }
  return updateClientInstance;
}

// Export types for use in other files
export type { UpdateStatus, UpdateProgress, UpdateInfo };
