// @see - https://www.electron.build/configuration/configuration
{
  "$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  "appId": "com.maggieprep.management",
  "productName": "Maggie Preparatory School",
  "asar": true,
  "directories": {
    "output": "release/${version}"
  },
  "files": [
    "dist",
    "dist-electron"
  ],
  "publish": [
    {
      "provider": "github",
      "owner": "Bosiakoba",
      "repo": "School-Management-Web-App-",
      "private": false,
      "releaseType": "release"
    }
  ],
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64"]
      },
      {
        "target": "portable",
        "arch": ["x64"]
      }
    ],
    "artifactName": "${productName}-Windows-${version}-Setup.${ext}",
    "icon": "public/logo.ico",
    "publisherName": "Maggie Preparatory School",
    "requestedExecutionLevel": "asInvoker"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "Maggie Preparatory School",
    "installerIcon": "public/logo.ico",
    "uninstallerIcon": "public/logo.ico",
    "installerHeaderIcon": "public/logo.ico",
    "deleteAppDataOnUninstall": false,
    "runAfterFinish": true
  },
  "mac": {
    "target": ["dmg", "zip"],
    "artifactName": "${productName}-Mac-${version}-Installer.${ext}",
    "icon": "public/logo.ico",
    "category": "public.app-category.education"
  },
  "linux": {
    "target": ["AppImage", "deb"],
    "artifactName": "${productName}-Linux-${version}.${ext}",
    "icon": "public/logo.ico",
    "category": "Education"
  },
  "afterPack": "./afterPack.js"
}
