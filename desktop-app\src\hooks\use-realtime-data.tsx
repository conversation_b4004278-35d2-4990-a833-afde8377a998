import { useState, useEffect, useCallback, useRef } from 'react';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  getDocs,
  limit,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { useAuth } from './use-auth';
import { Student, User as UserType, Notification, EnrollmentStatus, Gender } from '../lib/types';

// Real-time students hook
export function useRealtimeStudents() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!user) {
      if (isMountedRef.current) {
        setStudents([]);
        setLoading(false);
      }
      return;
    }

    if (isMountedRef.current) {
      setLoading(true);
      setError(null);
    }

    let studentsQuery;
    const studentsCollection = collection(db, 'students');

    // Build query based on user role
    if (user.role === 'teacher' && user.assignedClasses && user.assignedClasses.length > 0) {
      studentsQuery = query(
        studentsCollection,
        where('class', 'in', user.assignedClasses),
        orderBy('name')
      );
    } else if (user.role === 'admin') {
      studentsQuery = query(studentsCollection, orderBy('dateAdded', 'desc'));
    } else if (user.role === 'parent' && user.childrenIds && user.childrenIds.length > 0) {
      studentsQuery = query(
        studentsCollection,
        where('__name__', 'in', user.childrenIds)
      );
    } else {
      if (isMountedRef.current) {
        setStudents([]);
        setLoading(false);
      }
      return;
    }

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      studentsQuery,
      async (snapshot) => {
        try {
          console.log('🔄 Students data updated, processing...');

          // Process students with their results
          const studentsData = await Promise.all(
            snapshot.docs.map(async (doc) => {
              const student = { id: doc.id, ...doc.data() } as Student;

              // Fetch results for each student
              try {
                const resultsCollection = collection(db, `students/${student.id}/results`);
                const resultsSnapshot = await getDocs(resultsCollection);
                student.results = resultsSnapshot.docs.map(resDoc => ({
                  id: resDoc.id,
                  ...resDoc.data()
                }));
              } catch (error) {
                console.warn(`Failed to fetch results for student ${student.id}:`, error);
                student.results = [];
              }

              return student;
            })
          );

          if (isMountedRef.current) {
            setStudents(studentsData);
            setLoading(false);
            console.log('✅ Students data synchronized:', studentsData.length, 'students');
          }
        } catch (error) {
          console.error('❌ Error processing students data:', error);
          if (isMountedRef.current) {
            setError('Failed to load students data');
            setLoading(false);
          }
        }
      },
      (error) => {
        console.error('❌ Students listener error:', error);
        if (isMountedRef.current) {
          setError('Failed to sync students data');
          setLoading(false);
        }
      }
    );

    return () => {
      console.log('🔄 Cleaning up students listener');
      unsubscribe();
    };
  }, [user]);

  return { students, loading, error };
}

// Real-time users hook
export function useRealtimeUsers() {
  const [users, setUsers] = useState<UserType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      setUsers([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const usersCollection = collection(db, 'users');
    const usersQuery = query(usersCollection, orderBy('name'));

    const unsubscribe = onSnapshot(
      usersQuery,
      (snapshot) => {
        try {
          const usersData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserType[];

          setUsers(usersData);
          setLoading(false);
          console.log('✅ Users data synchronized:', usersData.length, 'users');
        } catch (error) {
          console.error('❌ Error processing users data:', error);
          setError('Failed to load users data');
          setLoading(false);
        }
      },
      (error) => {
        console.error('❌ Users listener error:', error);
        setError('Failed to sync users data');
        setLoading(false);
      }
    );

    return () => {
      console.log('🔄 Cleaning up users listener');
      unsubscribe();
    };
  }, [user]);

  return { users, loading, error };
}

// Real-time notifications hook
export function useRealtimeNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const notificationsCollection = collection(db, 'notifications');
    const notificationsQuery = query(
      notificationsCollection, 
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    const unsubscribe = onSnapshot(
      notificationsQuery,
      (snapshot) => {
        try {
          const notificationsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Notification[];

          setNotifications(notificationsData);

          // Calculate unread count
          const lastView = user.lastNotificationView ? new Date(user.lastNotificationView) : new Date(0);
          const newUnreadCount = notificationsData.filter(n => 
            new Date(n.createdAt) > lastView
          ).length;
          setUnreadCount(newUnreadCount);

          setLoading(false);
          console.log('✅ Notifications synchronized:', notificationsData.length, 'notifications,', newUnreadCount, 'unread');
        } catch (error) {
          console.error('❌ Error processing notifications:', error);
          setError('Failed to load notifications');
          setLoading(false);
        }
      },
      (error) => {
        console.error('❌ Notifications listener error:', error);
        setError('Failed to sync notifications');
        setLoading(false);
      }
    );

    return () => {
      console.log('🔄 Cleaning up notifications listener');
      unsubscribe();
    };
  }, [user]);

  return { notifications, unreadCount, loading, error };
}

// Real-time dashboard stats hook
export function useRealtimeDashboardStats() {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalUsers: 0,
    totalTeachers: 0,
    totalParents: 0,
    activeStudents: 0,
    studentsWithResults: 0,
    pendingRegistrations: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const calculateStats = useCallback((studentsData: Student[], usersData: UserType[]) => {
    const totalStudents = studentsData.length;
    const totalUsers = usersData.length;
    const totalTeachers = usersData.filter(u => u.role === 'teacher').length;
    const totalParents = usersData.filter(u => u.role === 'parent').length;
    const activeStudents = studentsData.filter(s => s.enrollmentStatus === 'active').length;
    const studentsWithResults = studentsData.filter(s => s.results && s.results.length > 0).length;
    const pendingRegistrations = studentsData.filter(s => s.enrollmentStatus === 'on_hold').length;

    return {
      totalStudents,
      totalUsers,
      totalTeachers,
      totalParents,
      activeStudents,
      studentsWithResults,
      pendingRegistrations
    };
  }, []);

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const studentsCollection = collection(db, 'students');
    const usersCollection = collection(db, 'users');

    let studentsUnsubscribe: Unsubscribe;
    let usersUnsubscribe: Unsubscribe;
    let studentsData: Student[] = [];
    let usersData: UserType[] = [];

    // Listen to students changes
    studentsUnsubscribe = onSnapshot(
      studentsCollection,
      async (snapshot) => {
        try {
          studentsData = await Promise.all(
            snapshot.docs.map(async (doc) => {
              const student = { id: doc.id, ...doc.data() } as Student;
              
              try {
                const resultsCollection = collection(db, `students/${student.id}/results`);
                const resultsSnapshot = await getDocs(resultsCollection);
                student.results = resultsSnapshot.docs.map(resDoc => ({
                  id: resDoc.id,
                  ...resDoc.data()
                })) as any[];
              } catch (error) {
                student.results = [];
              }
              
              return student;
            })
          );

          // Update stats if we have both datasets
          if (usersData.length > 0) {
            const newStats = calculateStats(studentsData, usersData);
            setStats(newStats);
            setLoading(false);
          }
        } catch (error) {
          console.error('❌ Error processing students for stats:', error);
          setError('Failed to calculate statistics');
        }
      }
    );

    // Listen to users changes
    usersUnsubscribe = onSnapshot(
      usersCollection,
      (snapshot) => {
        try {
          usersData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserType[];

          // Update stats if we have both datasets
          if (studentsData.length > 0) {
            const newStats = calculateStats(studentsData, usersData);
            setStats(newStats);
            setLoading(false);
          }
        } catch (error) {
          console.error('❌ Error processing users for stats:', error);
          setError('Failed to calculate statistics');
        }
      }
    );

    return () => {
      console.log('🔄 Cleaning up dashboard stats listeners');
      if (studentsUnsubscribe) studentsUnsubscribe();
      if (usersUnsubscribe) usersUnsubscribe();
    };
  }, [user, calculateStats]);

  return { stats, loading, error };
}
