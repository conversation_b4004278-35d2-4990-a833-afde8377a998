// Academic Fees Management Types

export type ClassLevel = 
  | 'Nursery 1' | 'Nursery 2' 
  | 'KG 1' | 'KG 2'
  | 'Basic 1' | 'Basic 2' | 'Basic 3' | 'Basic 4' | 'Basic 5' | 'Basic 6' | 'Basic 7' | 'Basic 8' | 'Basic 9';

export type PaymentStatus = 'pending' | 'partial' | 'completed' | 'overdue';

export type PaymentMethod = 'cash' | 'bank_transfer' | 'mobile_money' | 'cheque' | 'card';

// Fee Structure for each class level
export interface FeeStructure {
  id: string;
  classLevel: ClassLevel;
  academicYear: string; // e.g., "2024-2025"
  fees: {
    tuition: number;
    registration: number;
    books: number;
    uniform: number;
    feeding: number;
    transport?: number;
    examination: number;
    development: number;
    pta: number; // Parent-Teacher Association
    sports: number;
    library: number;
    laboratory?: number;
    other?: number;
  };
  totalAmount: number;
  currency: string; // e.g., "GHS", "USD"
  createdAt: string;
  updatedAt: string;
  createdBy: string; // Admin user ID
  isActive: boolean;
}

// Individual student payment record
export interface StudentPayment {
  id: string;
  studentId: string;
  studentName: string;
  classLevel: ClassLevel;
  academicYear: string;
  feeStructureId: string; // Reference to FeeStructure
  
  // Payment breakdown
  payments: PaymentRecord[];
  
  // Calculated fields
  totalAmountDue: number;
  totalAmountPaid: number;
  amountRemaining: number;
  paymentStatus: PaymentStatus;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  lastPaymentDate?: string;
  nextDueDate?: string;
  
  // Parent information
  parentId: string;
  parentName: string;
  parentPhone?: string;
  parentEmail?: string;
}

// Individual payment transaction
export interface PaymentRecord {
  id: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  description: string; // e.g., "Tuition fee payment", "Books and uniform"
  receiptNumber: string;
  collectedBy: string; // User ID of person who collected payment
  collectedByName: string;
  
  // Fee breakdown for this payment
  feeBreakdown: {
    tuition?: number;
    registration?: number;
    books?: number;
    uniform?: number;
    feeding?: number;
    transport?: number;
    examination?: number;
    development?: number;
    pta?: number;
    sports?: number;
    library?: number;
    laboratory?: number;
    other?: number;
  };
  
  // Bank/Mobile Money details (optional)
  transactionReference?: string;
  bankName?: string;
  accountNumber?: string;
  
  createdAt: string;
  updatedAt: string;
}

// Payment summary for analytics
export interface PaymentSummary {
  academicYear: string;
  classLevel?: ClassLevel; // Optional - for class-specific summaries
  
  totalStudents: number;
  studentsWithPayments: number;
  studentsFullyPaid: number;
  studentsPartiallyPaid: number;
  studentsNoPaid: number;
  studentsOverdue: number;
  
  totalAmountExpected: number;
  totalAmountCollected: number;
  totalAmountPending: number;
  collectionRate: number; // Percentage
  
  // Monthly breakdown
  monthlyCollections: {
    month: string; // "2024-01", "2024-02", etc.
    amount: number;
    paymentsCount: number;
  }[];
  
  // Fee type breakdown
  feeTypeBreakdown: {
    tuition: { expected: number; collected: number };
    registration: { expected: number; collected: number };
    books: { expected: number; collected: number };
    uniform: { expected: number; collected: number };
    feeding: { expected: number; collected: number };
    transport: { expected: number; collected: number };
    examination: { expected: number; collected: number };
    development: { expected: number; collected: number };
    pta: { expected: number; collected: number };
    sports: { expected: number; collected: number };
    library: { expected: number; collected: number };
    laboratory: { expected: number; collected: number };
    other: { expected: number; collected: number };
  };
  
  lastUpdated: string;
}

// Fee reminder/notification
export interface FeeReminder {
  id: string;
  studentId: string;
  studentName: string;
  parentId: string;
  parentName: string;
  parentPhone?: string;
  parentEmail?: string;
  
  classLevel: ClassLevel;
  academicYear: string;
  amountDue: number;
  dueDate: string;
  daysOverdue: number;
  
  reminderType: 'first_notice' | 'second_notice' | 'final_notice' | 'overdue';
  sentDate?: string;
  sentBy?: string;
  sentVia?: 'sms' | 'email' | 'phone_call' | 'letter';
  
  isResolved: boolean;
  resolvedDate?: string;
  
  createdAt: string;
  updatedAt: string;
}

// Utility functions for fee calculations
export interface FeeCalculations {
  calculateTotalFees: (feeStructure: FeeStructure) => number;
  calculateAmountPaid: (payments: PaymentRecord[]) => number;
  calculateAmountRemaining: (totalDue: number, totalPaid: number) => number;
  determinePaymentStatus: (totalDue: number, totalPaid: number, dueDate?: string) => PaymentStatus;
  generateReceiptNumber: () => string;
  calculateCollectionRate: (expected: number, collected: number) => number;
}

// API interfaces for fee management
export interface CreateFeeStructureRequest {
  classLevel: ClassLevel;
  academicYear: string;
  fees: FeeStructure['fees'];
  currency: string;
}

export interface UpdateFeeStructureRequest extends Partial<CreateFeeStructureRequest> {
  id: string;
}

export interface RecordPaymentRequest {
  studentId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  description: string;
  feeBreakdown: PaymentRecord['feeBreakdown'];
  transactionReference?: string;
  bankName?: string;
  accountNumber?: string;
}

export interface GetPaymentHistoryRequest {
  studentId?: string;
  classLevel?: ClassLevel;
  academicYear?: string;
  startDate?: string;
  endDate?: string;
  paymentStatus?: PaymentStatus;
}

export interface GenerateReportRequest {
  academicYear: string;
  classLevel?: ClassLevel;
  reportType: 'summary' | 'detailed' | 'outstanding' | 'collection_analysis';
  startDate?: string;
  endDate?: string;
}

// Default fee structure template
export const DEFAULT_FEE_STRUCTURE: Omit<FeeStructure['fees'], 'transport' | 'laboratory' | 'other'> = {
  tuition: 0,
  registration: 0,
  books: 0,
  uniform: 0,
  feeding: 0,
  examination: 0,
  development: 0,
  pta: 0,
  sports: 0,
  library: 0
};

// Class level groupings for easier management
export const CLASS_LEVEL_GROUPS = {
  'Early Years': ['Nursery 1', 'Nursery 2', 'KG 1', 'KG 2'] as ClassLevel[],
  'Lower Primary': ['Basic 1', 'Basic 2', 'Basic 3'] as ClassLevel[],
  'Upper Primary': ['Basic 4', 'Basic 5', 'Basic 6'] as ClassLevel[],
  'Junior High': ['Basic 7', 'Basic 8', 'Basic 9'] as ClassLevel[]
};

// Payment status colors for UI
export const PAYMENT_STATUS_COLORS = {
  pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
  partial: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
  completed: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
  overdue: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' }
};

// Fee type labels for UI
export const FEE_TYPE_LABELS = {
  tuition: 'Tuition Fee',
  registration: 'Registration Fee',
  books: 'Books & Materials',
  uniform: 'Uniform',
  feeding: 'Feeding Fee',
  transport: 'Transport Fee',
  examination: 'Examination Fee',
  development: 'Development Levy',
  pta: 'PTA Dues',
  sports: 'Sports Fee',
  library: 'Library Fee',
  laboratory: 'Laboratory Fee',
  other: 'Other Fees'
};
