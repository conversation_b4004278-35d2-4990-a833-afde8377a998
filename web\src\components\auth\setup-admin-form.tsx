"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createUserWithEmailAndPassword } from "firebase/auth";
import { doc, setDoc, getDocs, collection, query, where } from "firebase/firestore";
import { auth, db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import { Eye, EyeOff } from "lucide-react";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Please enter a valid email address."),
  password: z.string().min(6, "Password must be at least 6 characters."),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SetupAdminFormValues = z.infer<typeof formSchema>;

export function SetupAdminForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [adminExists, setAdminExists] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const { toast } = useFloatingToast();

  const form = useForm<SetupAdminFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Check if admin already exists
  useEffect(() => {
    const checkAdminExists = async () => {
      try {
        const q = query(collection(db, "users"), where("role", "==", "admin"));
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          setAdminExists(true);
          toast({
            title: "Administrator Already Exists",
            description: "An administrator account has already been set up. Please contact the existing admin for access.",
            variant: "warning"
          });
        }
      } catch (error) {
        console.error("Error checking admin existence:", error);
        toast({
          title: "Setup Error",
          description: "Failed to check administrator status. Please try again.",
          variant: "error"
        });
      } finally {
        setIsChecking(false);
      }
    };

    checkAdminExists();
  }, [toast]);

  async function onSubmit(values: SetupAdminFormValues) {
    if (adminExists) {
      toast({
        title: "Setup Blocked",
        description: "An administrator account already exists. Please contact the existing admin.",
        variant: "error"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        values.email,
        values.password
      );

      // Create user document in Firestore
      await setDoc(doc(db, "users", userCredential.user.uid), {
        name: values.name,
        email: values.email,
        role: "admin",
        dateCreated: new Date().toISOString(),
        isActive: true,
      });

      toast({
        title: "Administrator Created Successfully!",
        description: "Your administrator account has been set up. You can now sign in.",
        variant: "success"
      });

      // Reset form
      form.reset();
    } catch (error: any) {
      console.error("Setup error:", error);
      let errorMessage = "Failed to create administrator account. Please try again.";
      
      if (error.code === "auth/email-already-in-use") {
        errorMessage = "An account with this email already exists.";
      } else if (error.code === "auth/weak-password") {
        errorMessage = "Password is too weak. Please choose a stronger password.";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Please enter a valid email address.";
      }
      
      toast({
        title: "Setup Failed",
        description: errorMessage,
        variant: "error"
      });
    } finally {
      setIsLoading(false);
    }
  }

  if (isChecking) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="text-white/80 mt-4">Checking administrator status...</p>
      </div>
    );
  }

  if (adminExists) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <h3 className="text-white text-lg font-semibold mb-2">Administrator Already Exists</h3>
        <p className="text-white/80 text-sm">
          An administrator account has already been set up for this school management system. 
          Please contact the existing administrator for access.
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name" className="text-white font-medium">
            Full Name
          </Label>
          <Input
            id="name"
            type="text"
            placeholder="Enter your full name"
            {...form.register("name")}
            className="bg-black/30 border-white/30 text-white placeholder:text-blue-200 backdrop-blur-sm focus:border-white/50 focus:ring-white/20 [&::-webkit-input-placeholder]:text-blue-200 [&::-moz-placeholder]:text-blue-200 [&:-ms-input-placeholder]:text-blue-200 [color-scheme:dark] [&::-webkit-text-fill-color]:white"
          />
          {form.formState.errors.name && (
            <p className="text-red-200 text-sm">
              {form.formState.errors.name.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-white font-medium">
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            {...form.register("email")}
            className="bg-black/30 border-white/30 text-white placeholder:text-blue-200 backdrop-blur-sm focus:border-white/50 focus:ring-white/20 [&::-webkit-input-placeholder]:text-blue-200 [&::-moz-placeholder]:text-blue-200 [&:-ms-input-placeholder]:text-blue-200 [color-scheme:dark] [&::-webkit-text-fill-color]:white"
          />
          {form.formState.errors.email && (
            <p className="text-red-200 text-sm">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-white font-medium">
            Password
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="Create a strong password"
              {...form.register("password")}
              className="bg-black/30 border-white/30 text-white placeholder:text-blue-200 backdrop-blur-sm focus:border-white/50 focus:ring-white/20 pr-10 [&::-webkit-input-placeholder]:text-blue-200 [&::-moz-placeholder]:text-blue-200 [&:-ms-input-placeholder]:text-blue-200 [color-scheme:dark] [&::-webkit-text-fill-color]:white"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white/80 transition-colors"
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          {form.formState.errors.password && (
            <p className="text-red-200 text-sm">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-white font-medium">
            Confirm Password
          </Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm your password"
              {...form.register("confirmPassword")}
              className="bg-black/30 border-white/30 text-white placeholder:text-blue-200 backdrop-blur-sm focus:border-white/50 focus:ring-white/20 pr-10 [&::-webkit-input-placeholder]:text-blue-200 [&::-moz-placeholder]:text-blue-200 [&:-ms-input-placeholder]:text-blue-200 [color-scheme:dark] [&::-webkit-text-fill-color]:white"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white/80 transition-colors"
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          {form.formState.errors.confirmPassword && (
            <p className="text-red-200 text-sm">
              {form.formState.errors.confirmPassword.message}
            </p>
          )}
        </div>
      </div>

      <Button
        type="submit"
        disabled={isLoading}
        className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
      >
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Setting up...</span>
          </div>
        ) : (
          "Create Administrator Account"
        )}
      </Button>
    </form>
  );
}
