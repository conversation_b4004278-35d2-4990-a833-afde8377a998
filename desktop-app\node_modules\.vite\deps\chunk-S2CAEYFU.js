import {
  __esm,
  __export,
  __require
} from "./chunk-PLDDJCW6.js";

// node_modules/.vite-electron-renderer/events.mjs
var events_exports = {};
__export(events_exports, {
  EventEmitter: () => EventEmitter,
  EventEmitterAsyncResource: () => EventEmitterAsyncResource,
  addAbortListener: () => addAbortListener,
  captureRejectionSymbol: () => captureRejectionSymbol,
  captureRejections: () => captureRejections,
  default: () => keyword_default,
  defaultMaxListeners: () => defaultMaxListeners,
  errorMonitor: () => errorMonitor,
  getEventListeners: () => getEventListeners,
  getMaxListeners: () => getMaxListeners,
  init: () => init,
  kMaxEventTargetListeners: () => kMaxEventTargetListeners,
  kMaxEventTargetListenersWarned: () => kMaxEventTargetListenersWarned,
  length: () => length,
  listenerCount: () => listenerCount,
  name: () => name,
  on: () => on,
  once: () => once,
  prototype: () => prototype,
  setMaxListeners: () => setMaxListeners,
  usingDomains: () => usingDomains
});
var avoid_parse_require, _M_, length, name, prototype, addAbortListener, once, on, getEventListeners, getMaxListeners, EventEmitter, usingDomains, captureRejectionSymbol, captureRejections, EventEmitterAsyncResource, errorMonitor, defaultMaxListeners, kMaxEventTargetListeners, kMaxEventTargetListenersWarned, setMaxListeners, init, listenerCount, keyword_default;
var init_events = __esm({
  "node_modules/.vite-electron-renderer/events.mjs"() {
    avoid_parse_require = __require;
    _M_ = avoid_parse_require("events");
    length = _M_.length;
    name = _M_.name;
    prototype = _M_.prototype;
    addAbortListener = _M_.addAbortListener;
    once = _M_.once;
    on = _M_.on;
    getEventListeners = _M_.getEventListeners;
    getMaxListeners = _M_.getMaxListeners;
    EventEmitter = _M_.EventEmitter;
    usingDomains = _M_.usingDomains;
    captureRejectionSymbol = _M_.captureRejectionSymbol;
    captureRejections = _M_.captureRejections;
    EventEmitterAsyncResource = _M_.EventEmitterAsyncResource;
    errorMonitor = _M_.errorMonitor;
    defaultMaxListeners = _M_.defaultMaxListeners;
    kMaxEventTargetListeners = _M_.kMaxEventTargetListeners;
    kMaxEventTargetListenersWarned = _M_.kMaxEventTargetListenersWarned;
    setMaxListeners = _M_.setMaxListeners;
    init = _M_.init;
    listenerCount = _M_.listenerCount;
    keyword_default = _M_.default || _M_;
  }
});

export {
  length,
  name,
  prototype,
  addAbortListener,
  once,
  on,
  getEventListeners,
  getMaxListeners,
  EventEmitter,
  usingDomains,
  captureRejectionSymbol,
  captureRejections,
  EventEmitterAsyncResource,
  errorMonitor,
  defaultMaxListeners,
  kMaxEventTargetListeners,
  kMaxEventTargetListenersWarned,
  setMaxListeners,
  init,
  listenerCount,
  keyword_default,
  events_exports,
  init_events
};
//# sourceMappingURL=chunk-S2CAEYFU.js.map
