import React, { useState, useEffect } from 'react';
import { getUpdateClient, UpdateStatus } from '../lib/update-client';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Separator } from './ui/separator';
import { Switch } from './ui/switch';
import { Label } from './ui/label';

export const UpdateSettings: React.FC = () => {
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus | null>(null);
  const [autoUpdateEnabled, setAutoUpdateEnabled] = useState(true);
  const [isChecking, setIsChecking] = useState(false);
  const updateClient = getUpdateClient();

  useEffect(() => {
    // Get initial status
    updateClient.getUpdateStatus().then(setUpdateStatus);

    // Listen for update events
    const unsubscribeChecking = updateClient.on('checking', () => {
      setIsChecking(true);
      setUpdateStatus(prev => prev ? { ...prev, checking: true, error: null } : null);
    });

    const unsubscribeAvailable = updateClient.on('available', (updateInfo) => {
      setIsChecking(false);
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        available: true, 
        updateInfo 
      } : null);
    });

    const unsubscribeNotAvailable = updateClient.on('not-available', () => {
      setIsChecking(false);
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        available: false 
      } : null);
    });

    const unsubscribeError = updateClient.on('error', (error: string) => {
      setIsChecking(false);
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        checking: false, 
        downloading: false, 
        error 
      } : null);
    });

    const unsubscribeDownloadStarted = updateClient.on('download-started', () => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        downloading: true, 
        progress: null 
      } : null);
    });

    const unsubscribeDownloadProgress = updateClient.on('download-progress', (progress) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        progress 
      } : null);
    });

    const unsubscribeDownloaded = updateClient.on('downloaded', (updateInfo) => {
      setUpdateStatus(prev => prev ? { 
        ...prev, 
        downloading: false, 
        downloaded: true, 
        updateInfo 
      } : null);
    });

    return () => {
      unsubscribeChecking();
      unsubscribeAvailable();
      unsubscribeNotAvailable();
      unsubscribeError();
      unsubscribeDownloadStarted();
      unsubscribeDownloadProgress();
      unsubscribeDownloaded();
    };
  }, [updateClient]);

  const handleCheckForUpdates = async () => {
    setIsChecking(true);
    await updateClient.checkForUpdates();
  };

  const handleDownload = async () => {
    await updateClient.downloadUpdate();
  };

  const handleInstall = async () => {
    await updateClient.installUpdate();
  };

  const handleAutoUpdateToggle = async (enabled: boolean) => {
    setAutoUpdateEnabled(enabled);
    await updateClient.setAutoUpdate(enabled);
  };

  const getCurrentVersion = () => {
    // Get version from package.json or electron
    return window.electronAPI ? '1.0.0' : 'Web Version';
  };

  const getStatusBadge = () => {
    if (!updateStatus) return null;

    if (updateStatus.checking || isChecking) {
      return <Badge variant="secondary">Checking...</Badge>;
    }
    
    if (updateStatus.error) {
      return <Badge variant="destructive">Error</Badge>;
    }
    
    if (updateStatus.downloaded) {
      return <Badge variant="default">Ready to Install</Badge>;
    }
    
    if (updateStatus.downloading) {
      return <Badge variant="secondary">Downloading...</Badge>;
    }
    
    if (updateStatus.available) {
      return <Badge variant="outline">Update Available</Badge>;
    }
    
    return <Badge variant="secondary">Up to Date</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          App Updates
          {getStatusBadge()}
        </CardTitle>
        <CardDescription>
          Manage automatic updates and check for new versions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Version */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium">Current Version</Label>
            <p className="text-sm text-muted-foreground">{getCurrentVersion()}</p>
          </div>
        </div>

        <Separator />

        {/* Auto-Update Setting */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-sm font-medium">Automatic Updates</Label>
            <p className="text-sm text-muted-foreground">
              Automatically download and install updates
            </p>
          </div>
          <Switch
            checked={autoUpdateEnabled}
            onCheckedChange={handleAutoUpdateToggle}
          />
        </div>

        <Separator />

        {/* Update Status */}
        {updateStatus && (
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Update Status</Label>
            </div>

            {/* Available Update Info */}
            {updateStatus.available && updateStatus.updateInfo && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">
                      Version {updateStatus.updateInfo.version} Available
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Released: {new Date(updateStatus.updateInfo.releaseDate).toLocaleDateString()}
                    </p>
                    {updateStatus.updateInfo.downloadSize && (
                      <p className="text-xs text-blue-600 mt-1">
                        Size: {updateClient.formatFileSize(updateStatus.updateInfo.downloadSize)}
                      </p>
                    )}
                  </div>
                  {!updateStatus.downloading && !updateStatus.downloaded && (
                    <Button onClick={handleDownload} size="sm">
                      Download
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Download Progress */}
            {updateStatus.downloading && updateStatus.progress && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-yellow-900">
                      Downloading Update
                    </span>
                    <span className="text-sm text-yellow-700">
                      {Math.round(updateStatus.progress.percent)}%
                    </span>
                  </div>
                  <Progress value={updateStatus.progress.percent} className="h-2" />
                  <p className="text-xs text-yellow-700">
                    {updateClient.formatProgress(updateStatus.progress)}
                  </p>
                </div>
              </div>
            )}

            {/* Ready to Install */}
            {updateStatus.downloaded && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-green-900">
                      Update Ready to Install
                    </h4>
                    <p className="text-sm text-green-700 mt-1">
                      The app will restart to apply the update.
                    </p>
                  </div>
                  <Button onClick={handleInstall} size="sm" className="bg-green-600 hover:bg-green-700">
                    Install & Restart
                  </Button>
                </div>
              </div>
            )}

            {/* Error State */}
            {updateStatus.error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-red-900">
                      Update Error
                    </h4>
                    <p className="text-sm text-red-700 mt-1">
                      {updateStatus.error}
                    </p>
                  </div>
                  <Button 
                    onClick={handleCheckForUpdates} 
                    size="sm" 
                    variant="outline"
                    disabled={isChecking}
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        <Separator />

        {/* Manual Check Button */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium">Manual Check</Label>
            <p className="text-sm text-muted-foreground">
              Check for updates manually
            </p>
          </div>
          <Button 
            onClick={handleCheckForUpdates} 
            variant="outline" 
            disabled={isChecking || (updateStatus?.checking ?? false)}
          >
            {isChecking || (updateStatus?.checking ?? false) ? 'Checking...' : 'Check for Updates'}
          </Button>
        </div>

        {/* Release Notes */}
        {updateStatus?.updateInfo?.releaseNotes && (
          <>
            <Separator />
            <div>
              <Label className="text-sm font-medium">Release Notes</Label>
              <div className="mt-2 p-3 bg-gray-50 rounded-lg text-sm">
                <pre className="whitespace-pre-wrap font-sans">
                  {updateStatus.updateInfo.releaseNotes}
                </pre>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default UpdateSettings;
