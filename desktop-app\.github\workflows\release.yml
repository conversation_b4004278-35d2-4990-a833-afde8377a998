name: Build and Release Desktop App

on:
  push:
    tags:
      - 'v*.*.*'  # Triggers on version tags like v1.0.0
  workflow_dispatch:  # Allows manual triggering

jobs:
  build-and-release:
    runs-on: windows-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: desktop-app/package-lock.json
          
      - name: Install dependencies
        working-directory: desktop-app
        run: npm ci
        
      - name: Build application
        working-directory: desktop-app
        run: npm run build
        
      - name: Build and publish Electron app
        working-directory: desktop-app
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npm run electron:publish
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: desktop-app-installers
          path: |
            desktop-app/dist-installer/*.exe
            desktop-app/dist-installer/*.dmg
            desktop-app/dist-installer/*.AppImage
            desktop-app/dist-installer/latest*.yml
          retention-days: 30
