
"use client";

import { useMemo } from "react";
import ReactECharts from "echarts-for-react";
import * as echarts from "echarts";

interface SubjectPerformanceBreakdownProps {
  students: any[];
}

export function SubjectPerformanceBreakdown({ students }: SubjectPerformanceBreakdownProps) {
  const chartData = useMemo(() => {
    // Group results by subject
    const dataBySubject: Record<string, Record<string, number>> = {};

    students.forEach(student => {
      if (student.results) {
        student.results.forEach((result: any) => {
          if (!dataBySubject[result.subject]) {
            dataBySubject[result.subject] = {};
          }
          
          // Simplify grade to A, B, C, D, F
          let simplifiedGrade = result.grade;
          if (result.grade.startsWith('A')) simplifiedGrade = 'A';
          else if (result.grade.startsWith('B')) simplifiedGrade = 'B';
          else if (result.grade.startsWith('C')) simplifiedGrade = 'C';
          else if (result.grade.startsWith('D')) simplifiedGrade = 'D';
          else if (result.grade === 'F') simplifiedGrade = 'F';
          
          dataBySubject[result.subject][simplifiedGrade] = (dataBySubject[result.subject][simplifiedGrade] || 0) + 1;
        });
      }
    });

    const subjects = Object.keys(dataBySubject);
    const grades = ['A', 'B', 'C', 'D', 'F'];
    const colors = ['#8b5cf6', '#10b981', '#f59e0b', '#3b82f6', '#ef4444'];

    // Create series data for each grade
    const series = grades.map((grade, index) => ({
      name: grade,
      type: 'bar',
      stack: 'total',
      data: subjects.map(subject => dataBySubject[subject][grade] || 0),
      itemStyle: {
        color: colors[index],
        borderRadius: [0, 0, 2, 2]
      },
      barWidth: '60%',
      barCategoryGap: '0%',
      barGap: '0%'
    }));

    return {
      subjects,
      series,
      colors
    };
  }, [students]);

  if (chartData.subjects.length === 0) {
    return <p className="text-sm text-muted-foreground text-center py-8">No results available to display chart for this class.</p>
  }

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: 'rgba(0, 0, 0, 0.1)',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: function(params: any) {
        let result = `<div class="font-semibold mb-2">${params[0].name}</div>`;
        let total = 0;
        
        params.forEach((param: any) => {
          if (param.value > 0) {
            result += `<div class="flex items-center justify-between mb-1">
              <span class="flex items-center">
                <span class="w-3 h-3 rounded mr-2" style="background-color: ${param.color}"></span>
                <span>${param.seriesName}</span>
              </span>
              <span class="font-medium">${param.value}</span>
            </div>`;
            total += param.value;
          }
        });
        
        if (total > 0) {
          result += `<div class="border-t pt-2 mt-2 font-semibold">Total: ${total}</div>`;
        }
        
        return result;
      }
    },
    legend: {
      data: chartData.series.map(s => s.name),
      top: '0%',
      textStyle: {
        fontSize: 11,
        color: '#6b7280'
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 8
    },
    xAxis: {
      type: 'category',
      data: chartData.subjects,
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 10,
        color: '#6b7280',
        margin: 8,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 10,
        color: '#6b7280'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.05)',
          type: 'dashed'
        }
      }
    },
    series: chartData.series,
    animation: true,
    animationDuration: 300,
    animationEasing: 'cubicOut'
  };

  return (
    <div className="w-full">
      <div className="w-full h-[300px] sm:h-[350px] md:h-[400px]">
        <ReactECharts
          option={option}
          style={{ height: '100%', width: '100%' }}
          opts={{ renderer: 'canvas' }}
          className="w-full h-full"
        />
      </div>
    </div>
  );
}
