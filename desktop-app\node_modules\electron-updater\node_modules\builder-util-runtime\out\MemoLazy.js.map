{"version": 3, "file": "MemoLazy.js", "sourceRoot": "", "sources": ["../src/MemoLazy.ts"], "names": [], "mappings": ";;;AAAA,MAAa,QAAQ;IAInB,YACU,QAAiB,EACjB,OAAoC;QADpC,aAAQ,GAAR,QAAQ,CAAS;QACjB,YAAO,GAAP,OAAO,CAA6B;QALtC,aAAQ,GAAkB,SAAS,CAAA;QACnC,WAAM,GAA2B,SAAS,CAAA;IAK/C,CAAC;IAEJ,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAA;IAClC,CAAC;IAED,IAAI,KAAK;QACP,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;YACjE,uEAAuE;YACvE,OAAO,IAAI,CAAC,MAAM,CAAA;QACpB,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;QAEnB,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,KAAK,CAAC,KAAiB;QACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;CACF;AA9BD,4BA8BC;AAED,SAAS,MAAM,CAAC,UAAe,EAAE,WAAgB;IAC/C,MAAM,aAAa,GAAG,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,CAAA;IAC3E,MAAM,cAAc,GAAG,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,IAAI,CAAA;IAE9E,kDAAkD;IAClD,IAAI,aAAa,IAAI,cAAc,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEtC,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC9G,CAAC;IAED,6CAA6C;IAC7C,OAAO,UAAU,KAAK,WAAW,CAAA;AACnC,CAAC", "sourcesContent": ["export class MemoLazy<S, V> {\n  private selected: S | undefined = undefined\n  private _value: Promise<V> | undefined = undefined\n\n  constructor(\n    private selector: () => S,\n    private creator: (selected: S) => Promise<V>\n  ) {}\n\n  get hasValue() {\n    return this._value !== undefined\n  }\n\n  get value(): Promise<V> {\n    const selected = this.selector()\n    if (this._value !== undefined && equals(this.selected, selected)) {\n      // value exists and selected hasn't changed, so return the cached value\n      return this._value\n    }\n\n    this.selected = selected\n    const result = this.creator(selected)\n    this.value = result\n\n    return result\n  }\n\n  set value(value: Promise<V>) {\n    this._value = value\n  }\n}\n\nfunction equals(firstValue: any, secondValue: any): boolean {\n  const isFirstObject = typeof firstValue === \"object\" && firstValue !== null\n  const isSecondObject = typeof secondValue === \"object\" && secondValue !== null\n\n  // do a shallow comparison of objects, arrays etc.\n  if (isFirstObject && isSecondObject) {\n    const keys1 = Object.keys(firstValue)\n    const keys2 = Object.keys(secondValue)\n\n    return keys1.length === keys2.length && keys1.every((key: any) => equals(firstValue[key], secondValue[key]))\n  }\n\n  // otherwise just compare the values directly\n  return firstValue === secondValue\n}\n"]}