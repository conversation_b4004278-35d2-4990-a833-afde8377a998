import { useState, useEffect } from 'react'

interface DynamicIslandToastProps {
  title: string
  description?: string
  variant?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  onClose?: () => void
  id: string
}

export function DynamicIslandToast({ title, description, variant = 'info', duration = 4000, onClose }: DynamicIslandToastProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Animate in
    const showTimer = setTimeout(() => setIsVisible(true), 100)

    // Auto dismiss
    const hideTimer = setTimeout(() => {
      setIsExiting(true)
      setTimeout(() => {
        onClose?.()
      }, 500)
    }, duration)

    return () => {
      clearTimeout(showTimer)
      clearTimeout(hideTimer)
    }
  }, [duration, onClose])

  const variantStyles = {
    success: {
      bg: 'bg-green-900/95',
      border: 'border-green-400/30',
      icon: 'text-green-300',
      title: 'text-white',
      description: 'text-green-100'
    },
    error: {
      bg: 'bg-red-900/95',
      border: 'border-red-400/30',
      icon: 'text-red-300',
      title: 'text-white',
      description: 'text-red-100'
    },
    warning: {
      bg: 'bg-yellow-900/95',
      border: 'border-yellow-400/30',
      icon: 'text-yellow-300',
      title: 'text-white',
      description: 'text-yellow-100'
    },
    info: {
      bg: 'bg-blue-900/95',
      border: 'border-blue-400/30',
      icon: 'text-blue-300',
      title: 'text-white',
      description: 'text-blue-100'
    }
  }

  const styles = variantStyles[variant]

  const getIcon = () => {
    switch (variant) {
      case 'success':
        return (
          <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        )
      case 'error':
        return (
          <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        )
      case 'warning':
        return (
          <div className="w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01" />
            </svg>
          </div>
        )
      case 'info':
      default:
        return (
          <div className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01" />
            </svg>
          </div>
        )
    }
  }

  return (
    <div
      className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-[9999] transition-all duration-500 ease-out ${
        isVisible && !isExiting
          ? 'translate-y-0 opacity-100 scale-100'
          : '-translate-y-full opacity-0 scale-95'
      }`}
    >
      <div
        className={`
          relative overflow-hidden
          ${styles.bg}
          border ${styles.border}
          backdrop-blur-xl
          rounded-full
          px-6 py-4
          shadow-2xl
          min-w-[320px] max-w-[480px]
          transition-all duration-300
          hover:scale-105
        `}
      >
        {/* Content */}
        <div className="relative flex items-center space-x-4">
          {/* Icon */}
          <div className={`flex-shrink-0 ${styles.icon}`}>
            {getIcon()}
          </div>

          {/* Text Content */}
          <div className="flex-1 min-w-0">
            <h4 className={`text-sm font-semibold ${styles.title} leading-tight`}>
              {title}
            </h4>
            {description && (
              <p className={`text-xs ${styles.description} mt-0.5 leading-tight opacity-90`}>
                {description}
              </p>
            )}
          </div>

          {/* Close Button */}
          <button
            type="button"
            onClick={() => {
              setIsExiting(true)
              setTimeout(() => onClose?.(), 600)
            }}
            className={`
              flex-shrink-0 w-6 h-6 rounded-full
              bg-white/10 hover:bg-white/20
              flex items-center justify-center
              transition-all duration-200
              hover:scale-110
              ${styles.icon}
            `}
            title="Close notification"
            aria-label="Close notification"
          >
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}

// Toast Container Component - Fixed positioning
export function DynamicIslandContainer({ children }: { children: React.ReactNode }) {
  return (
    <div className="pointer-events-none">
      {children}
    </div>
  )
}
