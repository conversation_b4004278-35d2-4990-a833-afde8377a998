import { build } from 'electron-builder'

// NOTE: Electron-builder requires .ico file for Windows installer icons
// To convert logo.jpg to logo.ico, you can:
// 1. Use ImageMagick: magick convert public/logo.jpg -define icon:auto-resize=256,128,64,48,32,16 public/logo.ico
// 2. Use an online converter like https://icoconvert.com/
// 3. Use graphic design software like GIMP or Photoshop to save as .ico
async function buildInstaller() {
  console.log('🚀 Building Maggie Preparatory School Management System Installer...')

  try {
    // Build the application and create installer
    await build({
      config: {
        appId: 'com.maggieprep.management',
        productName: 'Maggie Preparatory School',
        directories: {
          output: 'dist-installer'
        },
        files: [
          'dist/**/*',
          'dist-electron/**/*',
          'public/logo.jpg',
          'README.txt'
        ],
        win: {
          target: 'nsis',
          icon: 'public/logo.ico'
        },
        nsis: {
          oneClick: false,
          allowToChangeInstallationDirectory: true,
          createDesktopShortcut: true,
          createStartMenuShortcut: true,
          shortcutName: 'Maggie Preparatory School',
          runAfterFinish: true,
          artifactName: 'Maggie-Preparatory-School-Setup-${version}.${ext}'
        },
        publish: null
      }
    })
    
    console.log('✅ Installer built successfully!')
    console.log('📦 Installer location: dist-installer/')
    console.log('🎉 Ready to distribute: Maggie-Preparatory-School-Setup-1.0.0.exe')
    
  } catch (error) {
    console.error('❌ Build failed:', error)
    process.exit(1)
  }
}

// Run the build
buildInstaller()
