// Renderer-side sync client for communicating with main process Firebase sync service

export interface SyncOperation {
  id: string;
  collection: string;
  operation: 'create' | 'update' | 'delete';
  data: any;
  timestamp: Date;
  mongoId?: string;
  firebaseId?: string;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  retryCount: number;
  lastError?: string;
}

export interface SyncResult {
  success: boolean;
  totalProcessed: number;
  created: number;
  updated: number;
  deleted: number;
  conflicts: number;
  errors: string[];
}

export interface SyncStatus {
  syncInProgress: boolean;
  queueLength: number;
  activeSubscriptions: string[];
  firebaseAvailable: boolean;
}

export interface RealtimeStatus {
  activeSubscriptions: string[];
  subscriptionCount: number;
  firebaseAvailable: boolean;
}

// Sync Client for Renderer Process
export class SyncClient {
  private subscriptions: Map<string, Set<(data: any[]) => void>> = new Map();
  private realtimeSubscriptions: Set<string> = new Set();

  constructor() {
    // Check if we're in Electron environment
    if (!window.electronAPI) {
      console.warn('⚠️ Electron API not available - sync functionality disabled');
    }
  }

  // Sync Operations
  async performFullSync(): Promise<SyncResult> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      console.log('🔄 Starting full sync...');
      const result = await window.electronAPI.invoke('sync:performFullSync');
      console.log('✅ Full sync completed:', result);
      return result;

    } catch (error) {
      console.error('❌ Full sync failed:', error);
      throw error;
    }
  }

  async syncCollection(collection: string): Promise<SyncResult> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      console.log(`🔄 Syncing collection: ${collection}`);
      const result = await window.electronAPI.invoke('sync:syncCollection', collection);
      console.log(`✅ Collection ${collection} synced:`, result);
      return result;

    } catch (error) {
      console.error(`❌ Failed to sync collection ${collection}:`, error);
      throw error;
    }
  }

  async getSyncStatus(): Promise<SyncStatus> {
    try {
      if (!window.electronAPI) {
        return {
          syncInProgress: false,
          queueLength: 0,
          activeSubscriptions: [],
          firebaseAvailable: false
        };
      }

      return await window.electronAPI.invoke('sync:getSyncStatus');

    } catch (error) {
      console.error('❌ Failed to get sync status:', error);
      throw error;
    }
  }

  async getSyncQueue(): Promise<SyncOperation[]> {
    try {
      if (!window.electronAPI) {
        return [];
      }

      return await window.electronAPI.invoke('sync:getSyncQueue');

    } catch (error) {
      console.error('❌ Failed to get sync queue:', error);
      throw error;
    }
  }

  async clearSyncQueue(): Promise<void> {
    try {
      if (!window.electronAPI) {
        return;
      }

      await window.electronAPI.invoke('sync:clearSyncQueue');
      console.log('🗑️ Sync queue cleared');

    } catch (error) {
      console.error('❌ Failed to clear sync queue:', error);
      throw error;
    }
  }

  // Real-time Subscriptions
  async subscribeToCollection(collection: string, callback: (data: any[]) => void): Promise<() => void> {
    try {
      // Add callback to local subscriptions
      if (!this.subscriptions.has(collection)) {
        this.subscriptions.set(collection, new Set());
      }
      this.subscriptions.get(collection)!.add(callback);

      // Subscribe to real-time updates if not already subscribed
      if (!this.realtimeSubscriptions.has(collection)) {
        if (window.electronAPI) {
          const result = await window.electronAPI.invoke('sync:subscribeToCollection', collection);
          if (result.success) {
            this.realtimeSubscriptions.add(collection);
            console.log(`✅ Subscribed to real-time updates for ${collection}`);
          } else {
            console.error(`❌ Failed to subscribe to ${collection}:`, result.error);
          }
        }

        // Start polling for updates (fallback mechanism)
        this.startPolling(collection);
      }

      // Return unsubscribe function
      return () => {
        this.unsubscribeCallback(collection, callback);
      };

    } catch (error) {
      console.error(`❌ Failed to subscribe to ${collection}:`, error);
      throw error;
    }
  }

  private async unsubscribeCallback(collection: string, callback: (data: any[]) => void): Promise<void> {
    const callbacks = this.subscriptions.get(collection);
    if (callbacks) {
      callbacks.delete(callback);
      
      // If no more callbacks, unsubscribe from real-time updates
      if (callbacks.size === 0) {
        this.subscriptions.delete(collection);
        
        if (this.realtimeSubscriptions.has(collection)) {
          if (window.electronAPI) {
            try {
              const result = await window.electronAPI.invoke('sync:unsubscribeFromCollection', collection);
              if (result.success) {
                this.realtimeSubscriptions.delete(collection);
                console.log(`✅ Unsubscribed from real-time updates for ${collection}`);
              }
            } catch (error) {
              console.error(`❌ Failed to unsubscribe from ${collection}:`, error);
            }
          }
        }
      }
    }
  }

  private startPolling(collection: string): void {
    // Poll for data changes every 5 seconds
    const pollInterval = setInterval(async () => {
      try {
        const callbacks = this.subscriptions.get(collection);
        if (!callbacks || callbacks.size === 0) {
          clearInterval(pollInterval);
          return;
        }

        // Get updated data from database
        if (window.electronAPI) {
          const result = await window.electronAPI.invoke('db:read', collection, {});
          if (result.success) {
            // Notify all callbacks
            callbacks.forEach(callback => {
              try {
                callback(result.data || []);
              } catch (error) {
                console.error(`❌ Callback error for ${collection}:`, error);
              }
            });
          }
        }

      } catch (error) {
        console.error(`❌ Polling error for ${collection}:`, error);
      }
    }, 5000);

    // Store interval for cleanup
    setTimeout(() => {
      if (!this.subscriptions.has(collection)) {
        clearInterval(pollInterval);
      }
    }, 100);
  }

  async getRealtimeStatus(): Promise<RealtimeStatus> {
    try {
      if (!window.electronAPI) {
        return {
          activeSubscriptions: [],
          subscriptionCount: 0,
          firebaseAvailable: false
        };
      }

      return await window.electronAPI.invoke('sync:getRealtimeStatus');

    } catch (error) {
      console.error('❌ Failed to get real-time status:', error);
      throw error;
    }
  }

  // Utility Methods
  async triggerSync(collection?: string): Promise<SyncResult> {
    if (collection) {
      return await this.syncCollection(collection);
    } else {
      return await this.performFullSync();
    }
  }

  async isSyncInProgress(): Promise<boolean> {
    try {
      const status = await this.getSyncStatus();
      return status.syncInProgress;
    } catch (error) {
      console.error('❌ Failed to check sync status:', error);
      return false;
    }
  }

  async waitForSyncCompletion(timeoutMs: number = 30000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      try {
        const inProgress = await this.isSyncInProgress();
        if (!inProgress) {
          return true;
        }
        
        // Wait 1 second before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('❌ Error waiting for sync completion:', error);
        return false;
      }
    }
    
    console.warn('⚠️ Sync completion timeout reached');
    return false;
  }

  // Cleanup
  async cleanup(): Promise<void> {
    console.log('🔄 Cleaning up sync client...');
    
    // Unsubscribe from all collections
    const collections = Array.from(this.realtimeSubscriptions);
    for (const collection of collections) {
      if (window.electronAPI) {
        try {
          await window.electronAPI.invoke('sync:unsubscribeFromCollection', collection);
        } catch (error) {
          console.error(`❌ Failed to unsubscribe from ${collection}:`, error);
        }
      }
    }
    
    // Clear local subscriptions
    this.subscriptions.clear();
    this.realtimeSubscriptions.clear();
    
    console.log('✅ Sync client cleaned up');
  }
}

// Singleton instance
let syncClientInstance: SyncClient | null = null;

export function getSyncClient(): SyncClient {
  if (!syncClientInstance) {
    syncClientInstance = new SyncClient();
  }
  return syncClientInstance;
}

// Export types for use in other files
export type { SyncStatus, RealtimeStatus };
