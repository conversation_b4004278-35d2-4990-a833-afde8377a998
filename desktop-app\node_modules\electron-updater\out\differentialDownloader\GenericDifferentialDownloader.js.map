{"version": 3, "file": "GenericDifferentialDownloader.js", "sourceRoot": "", "sources": ["../../src/differentialDownloader/GenericDifferentialDownloader.ts"], "names": [], "mappings": ";;;AACA,qEAAiE;AAEjE,MAAa,6BAA8B,SAAQ,+CAAsB;IACvE,QAAQ,CAAC,WAAqB,EAAE,WAAqB;QACnD,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;IAClD,CAAC;CACF;AAJD,sEAIC", "sourcesContent": ["import { BlockMap } from \"builder-util-runtime/out/blockMapApi\"\nimport { DifferentialDownloader } from \"./DifferentialDownloader\"\n\nexport class GenericDifferentialDownloader extends DifferentialDownloader {\n  download(oldBlockMap: BlockMap, newBlockMap: BlockMap): Promise<any> {\n    return this.doDownload(oldBlockMap, newBlockMap)\n  }\n}\n"]}