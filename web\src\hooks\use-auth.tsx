
"use client";

import { useState, useEffect, useCallback, createContext, useContext, type ReactNode } from "react";
import { useRouter } from "next/navigation";
import type { User as FirebaseUser } from "firebase/auth";
import { onAuthStateChanged, signInWithEmailAndPassword, signOut } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { type User } from "@/lib/types";
import { auth, db } from "@/lib/firebase";

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  isLoading: boolean;
  login: (email: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !isLoading && !!user && !!firebaseUser;

  const fetchUserData = useCallback(async (fbUser: FirebaseUser | null) => {
    if (fbUser && fbUser.email) {
      const userDocRef = doc(db, "users", fbUser.email);
      try {
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            const userData = { id: userDoc.id, ...userDoc.data() } as User;
            setUser(userData);
            setFirebaseUser(fbUser);
          } else {
            console.error(`Login failed: User with email ${fbUser.email} exists in Firebase Auth but not in Firestore.`);
            alert(`Login failed: Your user profile was not found in the database. Please contact an administrator to have your account configured correctly.`);
            await signOut(auth);
            setUser(null);
            setFirebaseUser(null);
          }
      } catch (error) {
          console.error("Error fetching user document from Firestore:", error);
          alert("An error occurred while fetching your profile. Please try again.");
          await signOut(auth);
          setUser(null);
          setFirebaseUser(null);
      }
    } else {
      setUser(null);
      setFirebaseUser(null);
    }
    setIsLoading(false);
  }, []);

  const refreshUser = useCallback(async () => {
    const fbUser = auth.currentUser;
    if (fbUser) {
        await fetchUserData(fbUser);
    }
  }, [fetchUserData]);

  useEffect(() => {
    setIsLoading(true);
    const unsubscribe = onAuthStateChanged(auth, fetchUserData);
    return () => unsubscribe();
  }, [fetchUserData]);
  
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
        router.push('/dashboard');
    }
  }, [isLoading, isAuthenticated, router]);

  const login = useCallback(
    async (email: string, password?: string) => {
      if (!password) {
        throw new Error("Password is required.");
      }
      try {
        await signInWithEmailAndPassword(auth, email, password);
        // The onAuthStateChanged listener will handle fetching user data and setting state.
      } catch (error: any) {
        if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential' || error.code === 'auth/invalid-email') {
            throw new Error("Invalid email or password.");
        }
        console.error("Login Error:", error);
        throw new Error("An unexpected error occurred during login.");
      }
    },
    []
  );

  const logout = useCallback(async () => {
    try {
      await signOut(auth);
      setUser(null);
      setFirebaseUser(null);
      router.push("/login");
    } catch (error) {
        console.error("Error signing out:", error);
    }
  }, [router]);
  

  const value = {
    user,
    firebaseUser,
    isLoading,
    login,
    logout,
    isAuthenticated,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
