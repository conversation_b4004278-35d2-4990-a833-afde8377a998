# **App Name**: Chariot

## Core Features:

- Dashboard: Role-Based Dashboard: Display information based on the user's role (admin, teacher, parent).
- Authentication: User Authentication: Secure login functionality using JWT.
- Student List: Student Search and List: Enable admins and teachers to search and filter students by various criteria.
- Parent View: Parent-Student View:  Parents can only see their child's profile, grades, and files.
- Results: Results Uploading: Ad<PERSON> can upload and link to results to student records. Results viewable by other roles.

## Style Guidelines:

- Primary color: Deep purple (#673AB7) to convey a sense of trust and sophistication.
- Background color: Light gray (#F5F5F5) to provide a clean and modern backdrop.
- Accent color: Electric indigo (#7C4DFF) for highlights and interactive elements, drawing the user's eye.
- Body font: 'Inter', a sans-serif font for readability and modern appeal.
- Headline font: 'Space Grotesk', a sans-serif font used for headlines, providing a modern computerized feeling. Paired with 'Inter' for the body.
- Use clean, modern icons to represent actions and information categories.
- Employ a grid-based layout for responsive design and clear information hierarchy.