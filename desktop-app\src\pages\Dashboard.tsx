import { useState, useEffect } from 'react'
import { collection, query, where, onSnapshot, getDocs } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useAuth } from '../hooks/use-auth'
import { useDocumentTitle } from '../hooks/use-document-title'
import { useRealtimeDashboardStats, useRealtimeStudents, useRealtimeNotifications } from '../hooks/use-realtime-data'
import { fixUserRoles, createTestAdmin, createTestTeacher, resetAllPaymentRecords } from '../utils/fix-user-roles'

export default function Dashboard() {
  const { user, isLoading } = useAuth()
  useDocumentTitle('Dashboard')

  // Debug logging
  console.log('🔍 Dashboard - User object:', user);
  console.log('🔍 Dashboard - User role:', user?.role);
  console.log('🔍 Dashboard - Is loading:', isLoading);

  // Show loading state while user is being loaded
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error if no user
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Authentication Error</h3>
            <p className="text-sm text-gray-600">
              Please log in to access the dashboard.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Render role-specific dashboard
  const renderDashboard = () => {
    switch (user?.role) {
      case "admin":
        return <AdminDashboard />
      case "teacher":
        return <TeacherDashboard user={user} />
      case "parent":
        return <ParentDashboard user={user} />
      default:
        return (
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
            <div className="text-center space-y-6 max-w-md mx-auto p-6">
              <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Invalid User Role</h3>
                <p className="text-sm text-gray-600 mb-4">
                  User role: <strong>{user?.role || 'undefined'}</strong><br />
                  Email: <strong>{user?.email}</strong><br />
                  This user needs a proper role assignment.
                </p>
              </div>

              {/* Debug Panel */}
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-3">Quick Fix Options</h4>
                <div className="space-y-2">
                  <button
                    type="button"
                    onClick={() => fixUserRoles()}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    Fix All User Roles
                  </button>
                  <button
                    type="button"
                    onClick={() => user?.email && createTestAdmin(user.email)}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                  >
                    Make This User Admin
                  </button>
                  <button
                    type="button"
                    onClick={() => user?.email && createTestTeacher(user.email)}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                  >
                    Make This User Teacher
                  </button>
                  <button
                    type="button"
                    onClick={() => window.location.reload()}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
                  >
                    Refresh Page
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      if (window.confirm('⚠️ WARNING: This will reset ALL payment records to zero!\n\nThis action will:\n• Clear all student payment histories\n• Delete all payment transaction records\n• Set all students to "pending" payment status\n\nThis is for DEVELOPMENT ONLY and cannot be undone.\n\nAre you sure you want to continue?')) {
                        resetAllPaymentRecords();
                      }
                    }}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                  >
                    🔄 Reset All Payment Records
                  </button>
                </div>
              </div>
            </div>
          </div>
        )
    }
  }

  return renderDashboard()
}

// Admin Dashboard - Shows all school data
function AdminDashboard() {
  const { stats, loading: statsLoading } = useRealtimeDashboardStats();
  const { students, loading: studentsLoading } = useRealtimeStudents();
  const { notifications, loading: notificationsLoading } = useRealtimeNotifications();

  // Calculate attendance percentage (mock for now)
  const attendancePercentage = students.length > 0 ?
    Math.round((students.filter(s => s.enrollmentStatus === 'active').length / students.length) * 100) : 0;

  const dashboardStats = [
    {
      title: 'Total Students',
      value: statsLoading ? '...' : stats.totalStudents.toString(),
      change: `${stats.activeStudents} active`,
      trend: 'up',
      icon: (
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-50 to-cyan-50'
    },
    {
      title: 'Active Teachers',
      value: statsLoading ? '...' : stats.totalTeachers.toString(),
      change: 'All positions filled',
      trend: 'stable',
      icon: (
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      color: 'from-green-500 to-emerald-500',
      bgColor: 'from-green-50 to-emerald-50'
    },
    {
      title: 'Total Parents',
      value: statsLoading ? '...' : stats.totalParents.toString(),
      change: 'Registered parents',
      trend: 'stable',
      icon: (
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      color: 'from-purple-500 to-pink-500',
      bgColor: 'from-purple-50 to-pink-50'
    },
    {
      title: 'With Results',
      value: statsLoading ? '...' : `${stats.studentsWithResults}`,
      change: `${attendancePercentage}% active`,
      trend: 'up',
      icon: (
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: 'from-orange-500 to-red-500',
      bgColor: 'from-orange-50 to-red-50'
    },
  ]

  // Convert notifications to recent activities format
  const recentActivities = notificationsLoading ? [] : notifications.slice(0, 4).map(notification => ({
    icon: (
      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4.828 7l6.586 6.586a2 2 0 002.828 0l6.586-6.586A2 2 0 0019.414 5H4.586A2 2 0 003.172 7z" />
      </svg>
    ),
    text: notification.message,
    time: new Date(notification.createdAt).toLocaleDateString(),
    color: 'text-blue-600 bg-blue-50'
  }))

  // Fallback activities if no notifications
  const fallbackActivities = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      text: `${stats.totalStudents} students enrolled`,
      time: 'Current',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      text: `${stats.studentsWithResults} students have results`,
      time: 'Current',
      color: 'text-green-600 bg-green-50'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      text: `${stats.totalTeachers} active teachers`,
      time: 'Current',
      color: 'text-purple-600 bg-purple-50'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      text: `${stats.totalParents} registered parents`,
      time: 'Current',
      color: 'text-orange-600 bg-orange-50'
    },
  ]

  const displayActivities = recentActivities.length > 0 ? recentActivities : fallbackActivities

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, Admin! 👋
        </h1>
        <p className="text-gray-600">
          Here's what's happening at Maggie Preparatory School today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {dashboardStats.map((stat, index) => (
          <div key={index} className="group relative overflow-hidden">
            {/* Glass morphism card */}
            <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
              {/* Background gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.bgColor} opacity-50`}></div>

              {/* Content */}
              <div className="relative z-10 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-2">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </p>
                  <p className="text-xs text-gray-500 flex items-center space-x-1">
                    {stat.trend === 'up' && (
                      <svg className="w-3 h-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                      </svg>
                    )}
                    <span>{stat.change}</span>
                  </p>
                </div>
                <div className={`w-14 h-14 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  {stat.icon}
                </div>
              </div>

              {/* Hover glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activities */}
        <div className="lg:col-span-2">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl relative overflow-hidden">
            {/* Background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50"></div>

            <div className="relative z-10">
              <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <span>Recent Activities</span>
              </h3>

              <div className="space-y-4">
                {displayActivities.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 rounded-xl hover:bg-white/50 transition-all duration-200 group">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${activity.color}`}>
                      {activity.icon}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                        {activity.text}
                      </p>
                      <p className="text-xs text-gray-500">
                        {activity.time}
                      </p>
                    </div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl relative overflow-hidden">
            {/* Background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-50/50 to-blue-50/50"></div>

            <div className="relative z-10">
              <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <span>Quick Actions</span>
              </h3>

              <div className="space-y-3">
                <button
                  type="button"
                  className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <div className="text-left">
                    <p className="font-medium">Add New Student</p>
                    <p className="text-xs opacity-90">Register a new student</p>
                  </div>
                </button>

                <button
                  type="button"
                  className="w-full flex items-center space-x-3 p-4 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl hover:bg-white/70 transition-all duration-200 text-gray-700 hover:text-gray-900"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <div className="text-left">
                    <p className="font-medium">Upload Results</p>
                    <p className="text-xs opacity-70">Add academic results</p>
                  </div>
                </button>

                <button
                  type="button"
                  className="w-full flex items-center space-x-3 p-4 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl hover:bg-white/70 transition-all duration-200 text-gray-700 hover:text-gray-900"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div className="text-left">
                    <p className="font-medium">Generate Reports</p>
                    <p className="text-xs opacity-70">Create academic reports</p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  )
}

// Teacher Dashboard - Shows only assigned classes data
function TeacherDashboard({ user }: { user: any }) {
  const [students, setStudents] = useState<any[]>([]);

  // Fetch students data directly like other working pages (no loading state)
  useEffect(() => {
    const fetchStudentsAndResults = async () => {
      if (!user) return;

      let studentsQuery;
      const studentsCollection = collection(db, 'students');

      if (user.role === 'teacher' && user.assignedClasses && user.assignedClasses.length > 0) {
        studentsQuery = query(studentsCollection, where('class', 'in', user.assignedClasses));
      } else {
        setStudents([]);
        return;
      }

      // Use real-time listener for instant updates
      const unsubscribe = onSnapshot(studentsQuery, async (snapshot) => {
        const studentsData = await Promise.all(snapshot.docs.map(async (doc) => {
          const student = { id: doc.id, ...doc.data() };
          const resultsCollection = collection(db, `students/${student.id}/results`);
          const resultsSnapshot = await getDocs(resultsCollection);
          student.results = resultsSnapshot.docs.map(resDoc => ({ id: resDoc.id, ...resDoc.data() }));
          return student;
        }));

        setStudents(studentsData);
      }, (error) => {
        console.error('❌ Error in teacher dashboard students listener:', error);
      });

      return unsubscribe;
    };

    fetchStudentsAndResults();
  }, [user]);

  // Debug logging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('TeacherDashboard - User:', user);
    console.log('TeacherDashboard - Students:', students);
  }

  // Calculate real stats from assigned students
  const assignedClasses = user?.assignedClasses || []
  const totalStudents = students.length
  const studentsWithResults = students.filter(s => s.results && s.results.length > 0).length

  // Group students by class
  const classStats = assignedClasses.map((className: string) => {
    const classStudents = students.filter(s => s.class === className)
    return {
      class: className,
      count: classStudents.length,
      withResults: classStudents.filter(s => s.results && s.results.length > 0).length,
      active: classStudents.filter(s => s.enrollmentStatus === 'active').length
    }
  })

  // Check if teacher has no assigned classes
  if (!user?.assignedClasses || user.assignedClasses.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Classes Assigned</h2>
          <p className="text-gray-600">Please contact your administrator to assign classes to your account.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.name}! 👨‍🏫
        </h1>
        <p className="text-gray-600">
          Here's an overview of your assigned classes.
        </p>
      </div>

      {/* Class Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Students</p>
              <p className="text-2xl font-bold text-gray-900">{totalStudents}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Assigned Classes</p>
              <p className="text-2xl font-bold text-gray-900">{assignedClasses.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">With Results</p>
              <p className="text-2xl font-bold text-gray-900">{studentsWithResults}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Class Overview */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Your Classes</h3>
        <div className="space-y-4">
          {classStats.length > 0 ? classStats.map((cls: any, index: number) => (
            <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center text-white font-semibold">
                  {cls.class.split(' ')[1] || cls.class.charAt(0)}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{cls.class}</h4>
                  <p className="text-sm text-gray-600">{cls.count} students</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">With Results: {cls.withResults}</p>
                <p className="text-sm text-gray-600">Active: {cls.active}</p>
              </div>
            </div>
          )) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No assigned classes found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Parent Dashboard - Shows only children's data
function ParentDashboard({ user }: { user: any }) {
  const [children, setChildren] = useState<any[]>([]);
  const [results, setResults] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);
  const [showContactModal, setShowContactModal] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<any>(null);

  // Fetch children data using the same approach as web version
  useEffect(() => {
    if (!user || !user.childrenIds || user.childrenIds.length === 0) {
      setChildren([]);
      return;
    }

    // Fetch students
    const studentsCollection = collection(db, 'students');
    const studentsQuery = query(studentsCollection, where('__name__', 'in', user.childrenIds));

    // Fetch results from main results collection (like web version)
    const resultsQuery = query(
      collection(db, 'results'),
      where('studentId', 'in', user.childrenIds.slice(0, 10)) // Firestore 'in' limit
    );

    // Fetch teachers to get contact information
    const teachersQuery = query(
      collection(db, 'users'),
      where('role', '==', 'teacher')
    );

    // Set up real-time listeners for students
    const unsubscribeStudents = onSnapshot(studentsQuery, (snapshot) => {
      const childrenData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setChildren(childrenData);
      console.log('✅ Desktop parent dashboard - children loaded:', childrenData.length);
    }, (error) => {
      console.error('❌ Error in parent dashboard children listener:', error);
      setChildren([]);
    });

    // Set up real-time listeners for results
    const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
      const resultsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setResults(resultsData);
      console.log('✅ Desktop parent dashboard - results loaded:', resultsData.length);
    }, (error) => {
      console.error('❌ Error in parent dashboard results listener:', error);
      setResults([]);
    });

    // Set up real-time listeners for teachers
    const unsubscribeTeachers = onSnapshot(teachersQuery, (snapshot) => {
      const teachersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setTeachers(teachersData);
      console.log('✅ Desktop parent dashboard - teachers loaded:', teachersData.length);
    }, (error) => {
      console.error('❌ Error in parent dashboard teachers listener:', error);
      setTeachers([]);
    });

    return () => {
      unsubscribeStudents();
      unsubscribeResults();
      unsubscribeTeachers();
    };
  }, [user]);

  // Helper function to get results for a specific student
  const getStudentResults = (studentId: string) => {
    return results.filter((result: any) => result.studentId === studentId);
  };

  // Helper function to find teacher for a specific class
  const getClassTeacher = (className: string) => {
    return teachers.find((teacher: any) =>
      teacher.assignedClasses && teacher.assignedClasses.includes(className)
    );
  };

  // Handle teacher contact
  const handleContactTeacher = (child: any) => {
    if (child.teacherAvailable) {
      setSelectedTeacher({
        name: child.teacher,
        email: child.teacherEmail,
        phone: child.teacherPhone,
        class: child.class,
        childName: child.name
      });
      setShowContactModal(true);
    }
  };

  // Process children data for display using the new results structure
  const processedChildren = children.map(child => {
    // Calculate age from date of birth
    const age = child.dob ? Math.floor((Date.now() - new Date(child.dob).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 0

    // Get results for this child from the main results collection
    const childResults = getStudentResults(child.id);

    // Calculate overall grade from results using percentage field
    const percentages = childResults.map((result: any) => result.percentage || 0);
    const avgPercentage = percentages.length > 0 ? percentages.reduce((sum: number, percentage: number) => sum + percentage, 0) / percentages.length : 0;
    const overallGrade = avgPercentage >= 90 ? 'A' : avgPercentage >= 80 ? 'B' : avgPercentage >= 70 ? 'C' : 'D';

    // Get teacher information for this child's class
    const classTeacher = getClassTeacher(child.class);

    return {
      ...child,
      age,
      attendance: child.enrollmentStatus === 'active' ? 95 : 85, // Mock attendance
      overallGrade,
      recentGrades: percentages.slice(-4), // Last 4 percentages
      resultsCount: childResults.length,
      averagePercentage: Math.round(avgPercentage),
      // Real teacher information
      teacher: classTeacher ? classTeacher.name : 'Not Available',
      teacherEmail: classTeacher ? classTeacher.email : null,
      teacherPhone: classTeacher ? classTeacher.phone : null,
      teacherAvailable: !!classTeacher
    }
  })

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.name}! 👨‍👩‍👧‍👦
        </h1>
        <p className="text-gray-600">
          Here's how your children are doing at school.
        </p>
      </div>

      {/* Children Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {processedChildren.length === 0 ? (
          <div className="col-span-2 text-center py-8">
            <p className="text-gray-500">No children found</p>
          </div>
        ) : processedChildren.map((child) => (
          <div key={child.id} className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xl">
                {child.name.split(' ').map(n => n[0]).join('')}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{child.name}</h3>
                <p className="text-sm text-gray-600">{child.class} • {child.age} years old</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-white/50 rounded-lg">
                <p className="text-xs text-gray-500">Results</p>
                <p className="text-lg font-semibold text-gray-900">{child.resultsCount || 0}</p>
              </div>
              <div className="text-center p-3 bg-white/50 rounded-lg">
                <p className="text-xs text-gray-500">Average</p>
                <p className="text-lg font-semibold text-green-600">{child.averagePercentage || 0}%</p>
              </div>
              <div className="text-center p-3 bg-white/50 rounded-lg">
                <p className="text-xs text-gray-500">Grade</p>
                <p className="text-lg font-semibold text-blue-600">{child.overallGrade}</p>
              </div>
            </div>

            <div className="mt-4">
              <p className="text-xs text-gray-500 mb-2">Recent Scores</p>
              <div className="flex space-x-2">
                {child.recentGrades.length > 0 ? child.recentGrades.map((grade: number, index: number) => (
                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {Math.round(grade)}%
                  </span>
                )) : (
                  <span className="text-xs text-gray-500">No recent scores</span>
                )}
              </div>
            </div>

            {/* Teacher Contact Section */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                    {child.teacherAvailable ? child.teacher.split(' ').map((n: string) => n[0]).join('').slice(0, 2) : '?'}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {child.teacherAvailable ? child.teacher : 'Teacher Not Available'}
                    </p>
                    <p className="text-xs text-gray-500">Class Teacher</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleContactTeacher(child)}
                  disabled={!child.teacherAvailable}
                  className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                    child.teacherAvailable
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {child.teacherAvailable ? 'Contact' : 'N/A'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions for Parents */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            type="button"
            className="flex items-center space-x-3 p-4 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl hover:bg-white/70 transition-all duration-200 text-gray-700 hover:text-gray-900"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 9a2 2 0 002 2h8a2 2 0 002-2l-2-9m-6 0V7" />
            </svg>
            <div className="text-left">
              <p className="font-medium">View Detailed Reports</p>
              <p className="text-xs opacity-70">See academic progress</p>
            </div>
          </button>

          <button
            type="button"
            className="flex items-center space-x-3 p-4 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl hover:bg-white/70 transition-all duration-200 text-gray-700 hover:text-gray-900"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <div className="text-left">
              <p className="font-medium">Contact Teachers</p>
              <p className="text-xs opacity-70">Send messages to teachers</p>
            </div>
          </button>
        </div>
      </div>

      {/* Teacher Contact Modal */}
      {showContactModal && selectedTeacher && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Teacher Contact</h3>
              <button
                type="button"
                onClick={() => setShowContactModal(false)}
                className="text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-semibold">
                  {selectedTeacher.name.split(' ').map((n: string) => n[0]).join('').slice(0, 2)}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{selectedTeacher.name}</h4>
                  <p className="text-sm text-gray-600">Class Teacher - {selectedTeacher.class}</p>
                  <p className="text-xs text-gray-500">Teaching {selectedTeacher.childName}</p>
                </div>
              </div>

              <div className="space-y-3">
                {selectedTeacher.email && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <p className="text-sm text-gray-600">{selectedTeacher.email}</p>
                    </div>
                  </div>
                )}

                {selectedTeacher.phone && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phone</p>
                      <p className="text-sm text-gray-600">{selectedTeacher.phone}</p>
                    </div>
                  </div>
                )}

                {!selectedTeacher.email && !selectedTeacher.phone && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No contact information available</p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3 pt-4">
                {selectedTeacher.email && (
                  <button
                    type="button"
                    onClick={() => window.location.href = `mailto:${selectedTeacher.email}`}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Send Email
                  </button>
                )}
                {selectedTeacher.phone && (
                  <button
                    type="button"
                    onClick={() => window.location.href = `tel:${selectedTeacher.phone}`}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Call
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
