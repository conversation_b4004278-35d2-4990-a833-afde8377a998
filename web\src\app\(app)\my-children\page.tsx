
"use client";

import ParentDashboard from "@/components/dashboard/parent-dashboard";
import { useAuth } from "@/hooks/use-auth";

export default function MyChildrenPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center">
            <div className="w-8 h-8 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Loading...</h3>
            <p className="text-sm text-muted-foreground">
              Please wait while we load your information.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            My Children
          </h1>
          <p className="text-muted-foreground mt-1">
            View and track your children's academic progress.
          </p>
        </div>
      </div>
      <ParentDashboard user={user} />
    </div>
  );
}
