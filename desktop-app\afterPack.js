const fs = require('fs');
const path = require('path');

exports.default = async function(context) {
  // Additional post-build configurations
  const { appOutDir, packager } = context;
  
  try {
    // Log build information
    console.log('✅ Build completed successfully');
    console.log('📦 Output Directory:', appOutDir);
    console.log('🔧 Platform:', packager.platform.name);
    
    // Optional: Add custom build metadata
    const buildMetadata = {
      buildTime: new Date().toISOString(),
      platform: packager.platform.name,
      arch: packager.arch
    };
    
    // Write build metadata
    fs.writeFileSync(
      path.join(appOutDir, 'build-metadata.json'), 
      JSON.stringify(buildMetadata, null, 2)
    );
    
    return true;
  } catch (error) {
    console.error('❌ Post-build script failed:', error);
    return false;
  }
}; 