import { useState, useEffect } from 'react'

export function useSetup() {
  const [isFirstRun, setIsFirstRun] = useState(false)
  const [isSetupComplete, setIsSetupComplete] = useState(false)

  useEffect(() => {
    // Check if this is the first run
    const setupCompleted = localStorage.getItem('maggie-prep-setup-completed')
    
    if (!setupCompleted) {
      setIsFirstRun(true)
    } else {
      setIsSetupComplete(true)
    }
  }, [])

  const completeSetup = () => {
    localStorage.setItem('maggie-prep-setup-completed', 'true')
    localStorage.setItem('maggie-prep-setup-date', new Date().toISOString())
    setIsFirstRun(false)
    setIsSetupComplete(true)
  }

  const resetSetup = () => {
    localStorage.removeItem('maggie-prep-setup-completed')
    localStorage.removeItem('maggie-prep-setup-date')
    setIsFirstRun(true)
    setIsSetupComplete(false)
  }

  return {
    isFirstRun,
    isSetupComplete,
    completeSetup,
    resetSetup
  }
}
