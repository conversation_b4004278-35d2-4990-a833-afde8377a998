import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import { useNotifications } from '../components/providers/notification-provider'
import { Modal } from '../components/ui/modal'
import {
  Subject,
  StudentResult,
  ClassLevel,
  Term,
  GradeScale,
  DEFAULT_GRADE_SCALES
} from '../lib/results-types'
import {
  calculateGrade,
  calculatePercentage,
  isPassed,
  getClassCategory,
  getSubjectsForClass,
  canUploadForClass,
  validateMarks,
  getGradeColor,
  generateRemarks
} from '../lib/results-utils'
import { getCurrentAcademicYear } from '../lib/business-logic'
import { Student } from '../lib/types'
import {
  collection,
  addDoc,
  onSnapshot,
  query,
  orderBy,
  getDocs
} from 'firebase/firestore'
import { db } from '../lib/firebase'

export default function Results() {
  const { user } = useAuth()
  const { notify } = useNotifications()
  const [students, setStudents] = useState<Student[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [results, setResults] = useState<StudentResult[]>([])
  const [loading, setLoading] = useState(true)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterClass, setFilterClass] = useState<ClassLevel | 'all'>('all')
  const [filterTerm, setFilterTerm] = useState<Term | 'all'>('all')
  const [uploadData, setUploadData] = useState({
    subjectId: '',
    term: 'First' as Term,
    academicYear: getCurrentAcademicYear(),
    marks: '',
    totalMarks: '100',
    remarks: '',
    teacherComment: ''
  })

  // Load data from Firestore
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load students
        const studentsQuery = query(collection(db, 'students'), orderBy('name'))
        const studentsSnapshot = await getDocs(studentsQuery)
        const studentsData = studentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Student[]
        setStudents(studentsData)

        // Load subjects
        const subjectsQuery = query(collection(db, 'subjects'), orderBy('name'))
        const subjectsSnapshot = await getDocs(subjectsQuery)
        const subjectsData = subjectsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Subject[]
        setSubjects(subjectsData)

        // Load results
        const resultsQuery = query(collection(db, 'results'), orderBy('createdAt', 'desc'))
        const unsubscribeResults = onSnapshot(resultsQuery, (snapshot) => {
          const resultsData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as StudentResult[]
          setResults(resultsData)
        })

        setLoading(false)
        return () => unsubscribeResults()
      } catch (error) {
        console.error('Error loading data:', error)
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleUploadResult = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !selectedStudent) return

    try {
      const marks = parseInt(uploadData.marks)
      const totalMarks = parseInt(uploadData.totalMarks)

      // Validate marks
      const markErrors = validateMarks(marks, totalMarks)
      if (markErrors.length > 0) {
        notify.error('Invalid Marks', markErrors.join(', '))
        return
      }

      // Get subject details
      const subject = subjects.find(s => s.id === uploadData.subjectId)
      if (!subject) {
        notify.error('Error', 'Subject not found.')
        return
      }

      // Get or create grade scale for this class
      const classCategory = getClassCategory(selectedStudent.class as ClassLevel)
      const gradeRanges = DEFAULT_GRADE_SCALES[classCategory]
      
      const mockGradeScale: GradeScale = {
        id: 'mock',
        classLevel: selectedStudent.class as ClassLevel,
        gradeRanges,
        totalMarks,
        passMarkPercentage: 40,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: user.id
      }

      // Calculate grade and other metrics
      const grade = calculateGrade(marks, mockGradeScale)
      const percentage = calculatePercentage(marks, totalMarks)
      const passed = isPassed(marks, mockGradeScale)

      const resultData: Omit<StudentResult, 'id'> = {
        studentId: selectedStudent.id,
        studentName: selectedStudent.name,
        classLevel: selectedStudent.class as ClassLevel,
        subjectId: subject.id,
        subjectName: subject.name,
        subjectCode: subject.code,
        term: uploadData.term,
        academicYear: uploadData.academicYear,
        marks,
        totalMarks,
        grade,
        percentage,
        isPassed: passed,
        remarks: uploadData.remarks || generateRemarks(grade),
        teacherComment: uploadData.teacherComment,
        uploadedBy: user.id,
        uploadedByName: user.name,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      await addDoc(collection(db, 'results'), resultData)

      notify.success(
        'Result Uploaded',
        `Result for ${selectedStudent.name} in ${subject.name} has been uploaded successfully.`
      )

      // Reset form
      setUploadData({
        subjectId: '',
        term: 'First',
        academicYear: getCurrentAcademicYear(),
        marks: '',
        totalMarks: '100',
        remarks: '',
        teacherComment: ''
      })
      setSelectedStudent(null)
      setShowUploadModal(false)

    } catch (error) {
      console.error('Error uploading result:', error)
      notify.error('Error', 'Failed to upload result. Please try again.')
    }
  }

  const handleSelectStudent = (student: Student) => {
    setSelectedStudent(student)
    setShowUploadModal(true)
  }

  const resetUploadForm = () => {
    setUploadData({
      subjectId: '',
      term: 'First',
      academicYear: getCurrentAcademicYear(),
      marks: '',
      totalMarks: '100',
      remarks: '',
      teacherComment: ''
    })
    setSelectedStudent(null)
    setShowUploadModal(false)
  }

  const getAvailableSubjects = () => {
    if (!selectedStudent) return []
    return getSubjectsForClass(selectedStudent.class as ClassLevel, subjects)
  }

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesClass = filterClass === 'all' || student.class === filterClass
    return matchesSearch && matchesClass
  })

  const filteredResults = results.filter(result => {
    const matchesTerm = filterTerm === 'all' || result.term === filterTerm
    return matchesTerm
  })

  const allClassLevels: ClassLevel[] = [
    'Nursery 1', 'Nursery 2', 'KG 1', 'KG 2',
    'Basic 1', 'Basic 2', 'Basic 3', 'Basic 4', 'Basic 5', 'Basic 6',
    'Basic 7', 'Basic 8', 'Basic 9'
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Calculate stats
  const totalStudents = students.length
  const totalResults = results.length
  const currentYearResults = results.filter(r => r.academicYear === getCurrentAcademicYear())
  const passedResults = currentYearResults.filter(r => r.isPassed)
  const passRate = currentYearResults.length > 0 ? Math.round((passedResults.length / currentYearResults.length) * 100) : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Results Management</h1>
          <p className="text-gray-600 mt-2">Upload and manage student results</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Students</p>
              <p className="text-2xl font-bold text-blue-600">{totalStudents}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Results</p>
              <p className="text-2xl font-bold text-green-600">{totalResults}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Year</p>
              <p className="text-2xl font-bold text-purple-600">{currentYearResults.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pass Rate</p>
              <p className="text-2xl font-bold text-orange-600">{passRate}%</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
        <div className="flex flex-wrap gap-4">
          <div className="relative flex-1 min-w-64">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search students by name or class..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          <select
            value={filterClass}
            onChange={(e) => setFilterClass(e.target.value as ClassLevel | 'all')}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            title="Filter by class"
          >
            <option value="all">All Classes</option>
            {allClassLevels.map(classLevel => (
              <option key={classLevel} value={classLevel}>
                {classLevel}
              </option>
            ))}
          </select>
          <select
            value={filterTerm}
            onChange={(e) => setFilterTerm(e.target.value as Term | 'all')}
            className="px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            title="Filter by term"
          >
            <option value="all">All Terms</option>
            <option value="First">First Term</option>
            <option value="Second">Second Term</option>
            <option value="Third">Third Term</option>
          </select>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            Students ({filteredStudents.length})
          </h2>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {filteredStudents.map((student, index) => {
            const studentResults = results.filter(r => r.studentId === student.id)
            const latestResults = studentResults.filter(r => r.academicYear === getCurrentAcademicYear())
            const canUpload = canUploadForClass(user?.role || '', user?.assignedClasses || [], student.class as ClassLevel)

            return (
              <div
                key={student.id}
                className={`flex items-center justify-between p-4 hover:bg-white/50 transition-all duration-200 ${
                  index !== filteredStudents.length - 1 ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-center gap-4 flex-1">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                    {student.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-1">
                      <h3 className="font-semibold text-gray-900 truncate">{student.name}</h3>
                      <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full flex-shrink-0">
                        {student.class}
                      </span>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <svg className="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {latestResults.length} results
                      </span>

                      {latestResults.length > 0 && (
                        <div className="flex gap-1">
                          {latestResults.slice(0, 3).map(result => {
                            const gradeColor = getGradeColor(result.grade)
                            return (
                              <span
                                key={result.id}
                                className="px-2 py-1 text-xs font-semibold rounded text-white"
                                style={{ backgroundColor: gradeColor }}
                                title={`${result.subjectName}: ${result.grade}`}
                              >
                                {result.subjectCode}
                              </span>
                            )
                          })}
                          {latestResults.length > 3 && (
                            <span className="px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded font-medium">
                              +{latestResults.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <button
                  type="button"
                  onClick={() => handleSelectStudent(student)}
                  disabled={!canUpload}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 flex-shrink-0 ml-4 ${
                    canUpload
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <span className="text-sm font-medium">Upload</span>
                </button>
              </div>
            )
          })}
        </div>
      </div>

      {filteredStudents.length === 0 && (
        <div className="text-center py-16">
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-12 border border-white/20 shadow-xl max-w-md mx-auto">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">No students found</h3>
            <p className="text-gray-600">
              {searchTerm || filterClass !== 'all'
                ? 'No students match your search criteria. Try adjusting your filters.'
                : 'No students available for result upload.'}
            </p>
          </div>
        </div>
      )}

      {/* Recent Results */}
      {filteredResults.length > 0 && (
        <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
          <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            Recent Results
          </h2>
          <div className="space-y-3">
            {filteredResults.slice(0, 10).map(result => {
              const gradeColor = getGradeColor(result.grade)
              return (
                <div key={result.id} className="flex justify-between items-center p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/70 transition-all duration-200">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-sm">
                      {result.studentName.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{result.studentName}</p>
                      <p className="text-sm text-gray-600 flex items-center gap-2">
                        <span>{result.subjectName}</span>
                        <span>•</span>
                        <span>{result.classLevel}</span>
                        <span>•</span>
                        <span>{result.term} Term</span>
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span
                      className="px-3 py-1 text-sm font-semibold rounded-lg text-white"
                      style={{ backgroundColor: gradeColor }}
                    >
                      {result.grade}
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      {result.marks}/{result.totalMarks} ({result.percentage}%)
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Upload Result Modal */}
      <Modal
        isOpen={showUploadModal}
        onClose={resetUploadForm}
        title={`Upload Result for ${selectedStudent?.name || ''}`}
      >
        {selectedStudent && (
          <div className="max-h-[70vh] overflow-y-auto">
            <form onSubmit={handleUploadResult} className="space-y-4">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-200">
                <p className="text-sm font-semibold text-gray-700">Student: {selectedStudent.name}</p>
                <p className="text-xs text-gray-600">Class: {selectedStudent.class}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                  <select
                    value={uploadData.subjectId}
                    onChange={(e) => setUploadData(prev => ({ ...prev, subjectId: e.target.value }))}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    title="Select subject"
                    required
                  >
                    <option value="">Select subject</option>
                    {getAvailableSubjects().map(subject => (
                      <option key={subject.id} value={subject.id}>
                        {subject.name} ({subject.code})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Term</label>
                  <select
                    value={uploadData.term}
                    onChange={(e) => setUploadData(prev => ({ ...prev, term: e.target.value as Term }))}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    title="Select term"
                  >
                    <option value="First">First Term</option>
                    <option value="Second">Second Term</option>
                    <option value="Third">Third Term</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                  <input
                    type="text"
                    value={uploadData.academicYear}
                    onChange={(e) => setUploadData(prev => ({ ...prev, academicYear: e.target.value }))}
                    placeholder="2024-2025"
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Marks Obtained</label>
                  <input
                    type="number"
                    min="0"
                    value={uploadData.marks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, marks: e.target.value }))}
                    placeholder="Enter marks"
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Marks</label>
                  <input
                    type="number"
                    min="1"
                    value={uploadData.totalMarks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, totalMarks: e.target.value }))}
                    placeholder="100"
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Remarks (Optional)</label>
                  <textarea
                    value={uploadData.remarks}
                    onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}
                    placeholder="Additional remarks..."
                    rows={2}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Teacher Comment (Optional)</label>
                  <textarea
                    value={uploadData.teacherComment}
                    onChange={(e) => setUploadData(prev => ({ ...prev, teacherComment: e.target.value }))}
                    placeholder="Teacher's comment..."
                    rows={2}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-3 border-t border-gray-200">
                <button
                  type="button"
                  onClick={resetUploadForm}
                  className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg flex items-center justify-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <span>Upload Result</span>
                </button>
              </div>
            </form>
          </div>
        )}
      </Modal>
    </div>
  )
}
