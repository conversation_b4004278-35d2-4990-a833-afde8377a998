import { 
  FeeStructure, 
  PaymentRecord, 
  PaymentStatus, 
  StudentPayment,
  PaymentSummary,
  ClassLevel 
} from './academic-fees-types';

// Calculate total fees from fee structure
export function calculateTotalFees(feeStructure: FeeStructure): number {
  const fees = feeStructure.fees;
  return (
    fees.tuition +
    fees.registration +
    fees.books +
    fees.uniform +
    fees.feeding +
    (fees.transport || 0) +
    fees.examination +
    fees.development +
    fees.pta +
    fees.sports +
    fees.library +
    (fees.laboratory || 0) +
    (fees.other || 0)
  );
}

// Calculate total amount paid from payment records
export function calculateAmountPaid(payments: PaymentRecord[]): number {
  return payments.reduce((total, payment) => total + payment.amount, 0);
}

// Calculate remaining amount
export function calculateAmountRemaining(totalDue: number, totalPaid: number): number {
  return Math.max(0, totalDue - totalPaid);
}

// Determine payment status based on amounts and due date
export function determinePaymentStatus(
  totalDue: number, 
  totalPaid: number, 
  dueDate?: string
): PaymentStatus {
  const remaining = calculateAmountRemaining(totalDue, totalPaid);
  
  if (remaining === 0) {
    return 'completed';
  }
  
  if (totalPaid === 0) {
    // Check if overdue
    if (dueDate && new Date(dueDate) < new Date()) {
      return 'overdue';
    }
    return 'pending';
  }
  
  // Partial payment
  if (dueDate && new Date(dueDate) < new Date()) {
    return 'overdue';
  }
  
  return 'partial';
}

// Generate unique receipt number
export function generateReceiptNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `RCP-${timestamp.slice(-6)}-${random}`;
}

// Calculate collection rate percentage
export function calculateCollectionRate(expected: number, collected: number): number {
  if (expected === 0) return 0;
  return Math.round((collected / expected) * 100 * 100) / 100; // Round to 2 decimal places
}

// Format currency amount
export function formatCurrency(amount: number, currency: string = 'GHS'): string {
  return new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

// Format date for display
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-GH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Calculate days overdue
export function calculateDaysOverdue(dueDate: string): number {
  const due = new Date(dueDate);
  const today = new Date();
  const diffTime = today.getTime() - due.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
}

// Import academic year functions from business logic
export {
  getCurrentAcademicYear,
  getAvailableAcademicYears,
  getNextAcademicYear,
  getPreviousAcademicYear,
  isCurrentAcademicYear,
  parseAcademicYear,
  formatAcademicYear,
  getAcademicYearForDate
} from './business-logic';

// Validate fee structure
export function validateFeeStructure(fees: FeeStructure['fees']): string[] {
  const errors: string[] = [];
  
  // Check for negative values
  Object.entries(fees).forEach(([key, value]) => {
    if (typeof value === 'number' && value < 0) {
      errors.push(`${key} cannot be negative`);
    }
  });
  
  // Check required fields
  const requiredFields = ['tuition', 'registration', 'books', 'uniform', 'feeding', 'examination', 'development', 'pta', 'sports', 'library'];
  requiredFields.forEach(field => {
    if (!(field in fees) || fees[field as keyof typeof fees] === undefined) {
      errors.push(`${field} is required`);
    }
  });
  
  return errors;
}

// Calculate payment breakdown for a specific fee type
export function calculateFeeTypeTotal(
  studentPayments: StudentPayment[], 
  feeType: keyof FeeStructure['fees']
): { expected: number; collected: number } {
  let expected = 0;
  let collected = 0;
  
  studentPayments.forEach(studentPayment => {
    // Add to expected (this would need fee structure lookup in real implementation)
    // For now, we'll calculate from the total due
    expected += studentPayment.totalAmountDue;
    
    // Add to collected for this fee type
    studentPayment.payments.forEach(payment => {
      const feeAmount = payment.feeBreakdown[feeType];
      if (feeAmount) {
        collected += feeAmount;
      }
    });
  });
  
  return { expected, collected };
}

// Generate payment summary for analytics
export function generatePaymentSummary(
  studentPayments: StudentPayment[],
  academicYear: string,
  classLevel?: ClassLevel
): PaymentSummary {
  // Filter by class level if specified
  const filteredPayments = classLevel 
    ? studentPayments.filter(sp => sp.classLevel === classLevel)
    : studentPayments;
  
  const totalStudents = filteredPayments.length;
  const studentsWithPayments = filteredPayments.filter(sp => sp.payments.length > 0).length;
  const studentsFullyPaid = filteredPayments.filter(sp => sp.paymentStatus === 'completed').length;
  const studentsPartiallyPaid = filteredPayments.filter(sp => sp.paymentStatus === 'partial').length;
  const studentsNoPaid = filteredPayments.filter(sp => sp.payments.length === 0).length;
  const studentsOverdue = filteredPayments.filter(sp => sp.paymentStatus === 'overdue').length;
  
  const totalAmountExpected = filteredPayments.reduce((sum, sp) => sum + sp.totalAmountDue, 0);
  const totalAmountCollected = filteredPayments.reduce((sum, sp) => sum + sp.totalAmountPaid, 0);
  const totalAmountPending = totalAmountExpected - totalAmountCollected;
  const collectionRate = calculateCollectionRate(totalAmountExpected, totalAmountCollected);
  
  // Calculate monthly collections
  const monthlyCollections: PaymentSummary['monthlyCollections'] = [];
  const monthlyData: { [key: string]: { amount: number; count: number } } = {};
  
  filteredPayments.forEach(studentPayment => {
    studentPayment.payments.forEach(payment => {
      const month = payment.paymentDate.substring(0, 7); // YYYY-MM format
      if (!monthlyData[month]) {
        monthlyData[month] = { amount: 0, count: 0 };
      }
      monthlyData[month].amount += payment.amount;
      monthlyData[month].count += 1;
    });
  });
  
  Object.entries(monthlyData).forEach(([month, data]) => {
    monthlyCollections.push({
      month,
      amount: data.amount,
      paymentsCount: data.count
    });
  });
  
  // Sort monthly collections by month
  monthlyCollections.sort((a, b) => a.month.localeCompare(b.month));
  
  // Calculate fee type breakdown
  const feeTypes: (keyof FeeStructure['fees'])[] = [
    'tuition', 'registration', 'books', 'uniform', 'feeding', 'transport',
    'examination', 'development', 'pta', 'sports', 'library', 'laboratory', 'other'
  ];
  
  const feeTypeBreakdown: PaymentSummary['feeTypeBreakdown'] = {} as any;
  
  feeTypes.forEach(feeType => {
    const { expected, collected } = calculateFeeTypeTotal(filteredPayments, feeType);
    feeTypeBreakdown[feeType] = { expected, collected };
  });
  
  return {
    academicYear,
    classLevel,
    totalStudents,
    studentsWithPayments,
    studentsFullyPaid,
    studentsPartiallyPaid,
    studentsNoPaid,
    studentsOverdue,
    totalAmountExpected,
    totalAmountCollected,
    totalAmountPending,
    collectionRate,
    monthlyCollections,
    feeTypeBreakdown,
    lastUpdated: new Date().toISOString()
  };
}

// Search and filter utilities
export function filterStudentPayments(
  studentPayments: StudentPayment[],
  filters: {
    classLevel?: ClassLevel;
    paymentStatus?: PaymentStatus;
    searchTerm?: string;
    academicYear?: string;
  }
): StudentPayment[] {
  return studentPayments.filter(payment => {
    // Filter by class level
    if (filters.classLevel && payment.classLevel !== filters.classLevel) {
      return false;
    }
    
    // Filter by payment status
    if (filters.paymentStatus && payment.paymentStatus !== filters.paymentStatus) {
      return false;
    }
    
    // Filter by academic year
    if (filters.academicYear && payment.academicYear !== filters.academicYear) {
      return false;
    }
    
    // Filter by search term (student name or parent name)
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesStudent = payment.studentName.toLowerCase().includes(searchLower);
      const matchesParent = payment.parentName.toLowerCase().includes(searchLower);
      if (!matchesStudent && !matchesParent) {
        return false;
      }
    }
    
    return true;
  });
}

// Sort student payments
export function sortStudentPayments(
  studentPayments: StudentPayment[],
  sortBy: 'name' | 'class' | 'amount_due' | 'amount_paid' | 'status' | 'last_payment',
  sortOrder: 'asc' | 'desc' = 'asc'
): StudentPayment[] {
  const sorted = [...studentPayments].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.studentName.localeCompare(b.studentName);
        break;
      case 'class':
        comparison = a.classLevel.localeCompare(b.classLevel);
        break;
      case 'amount_due':
        comparison = a.totalAmountDue - b.totalAmountDue;
        break;
      case 'amount_paid':
        comparison = a.totalAmountPaid - b.totalAmountPaid;
        break;
      case 'status':
        comparison = a.paymentStatus.localeCompare(b.paymentStatus);
        break;
      case 'last_payment':
        const aDate = a.lastPaymentDate ? new Date(a.lastPaymentDate).getTime() : 0;
        const bDate = b.lastPaymentDate ? new Date(b.lastPaymentDate).getTime() : 0;
        comparison = aDate - bDate;
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
  
  return sorted;
}
