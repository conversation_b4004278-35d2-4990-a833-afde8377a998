
"use client";

import * as React from "react";
import {
  ChevronsUpDown,
  ChevronDown,
  MoreHorizontal,
  PlusCircle,
  Trash2,
  Edit,
  FileUp,
  Copy,
  Info,
} from "lucide-react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Student, Result, StudentFormData } from "@/lib/types";
import { useAuth } from "@/hooks/use-auth";
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
  } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
  } from "@/components/ui/alert-dialog";
import { useFloatingToast } from "@/hooks/use-floating-toast";
import { UploadResultDialog } from "./upload-result-dialog";
import { collection, addDoc, doc, deleteDoc, updateDoc, arrayUnion, setDoc, getDoc, arrayRemove } from "firebase/firestore";
import { db, storage } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { EditStudentDialog } from "./edit-student-dialog";
import { StudentDetailsDialog } from "./student-details-dialog";
import { cleanupStudentRecords } from "@/utils/student-cleanup";


export default function StudentListClient({ students: initialStudents }: { students: Student[] }) {
  const { user } = useAuth();
  const { toast } = useFloatingToast();
  const [students, setStudents] = React.useState(initialStudents);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({
    dob: false,
    parentId: false,
    results: false,
    // Hide more columns on mobile by default
    class: window.innerWidth < 768 ? false : true,
    dateAdded: window.innerWidth < 1024 ? false : true,
  });
  const [rowSelection, setRowSelection] = React.useState({});

  React.useEffect(() => {
    setStudents(initialStudents);
  }, [initialStudents]);

  const uploadPhoto = async (photo: File, studentId: string): Promise<string> => {
    const storageRef = ref(storage, `avatars/${studentId}/${photo.name}`);
    await uploadBytes(storageRef, photo);
    const downloadUrl = await getDownloadURL(storageRef);
    return downloadUrl;
  };

  const addStudent = async (studentData: StudentFormData) => {
    const { photo, ...newStudentData } = studentData;
    let avatarUrl = `https://placehold.co/100x100.png`;

    try {
        const studentRef = doc(collection(db, "students")); // Create a ref with a new ID first
        
        if (photo && photo.length > 0) {
            avatarUrl = await uploadPhoto(photo[0], studentRef.id);
        }

        const studentWithDefaults = {
            ...newStudentData,
            avatarUrl,
            results: [],
            dateAdded: new Date().toISOString().split('T')[0],
        };
        
        await setDoc(studentRef, studentWithDefaults);

        const newStudent = { id: studentRef.id, ...studentWithDefaults };
        
        const parentRef = doc(db, "users", newStudent.parentId);
        await updateDoc(parentRef, {
            childrenIds: arrayUnion(studentRef.id)
        });

        setStudents(prev => [newStudent, ...prev]);
        
        toast({
            title: "Student Added Successfully!",
            description: `${newStudent.name} has been added.`,
            variant: "success"
        });

    } catch (error) {
        console.error("Error adding student: ", error);
        toast({
            title: "Error",
            description: "Failed to add student. Please try again.",
            variant: "error"
        });
    }
  };

  const updateStudent = async (studentId: string, studentData: StudentFormData) => {
    const { photo, ...updatedData } = studentData;
    
    try {
        const studentRef = doc(db, "students", studentId);
        const originalStudent = students.find(s => s.id === studentId);

        if (!originalStudent) throw new Error("Student not found");
        
        let finalUpdatedData: any = { ...updatedData };
        if (photo && photo.length > 0) {
            const newAvatarUrl = await uploadPhoto(photo[0], studentId);
            finalUpdatedData.avatarUrl = newAvatarUrl;
        }

        await updateDoc(studentRef, finalUpdatedData);

        if (originalStudent.parentId !== updatedData.parentId) {
            const oldParentRef = doc(db, "users", originalStudent.parentId);
            await updateDoc(oldParentRef, {
                childrenIds: arrayRemove(studentId)
            });
            const newParentRef = doc(db, "users", updatedData.parentId);
            await updateDoc(newParentRef, {
                childrenIds: arrayUnion(studentId)
            });
        }

        setStudents(prev => prev.map(s => s.id === studentId ? { ...s, ...finalUpdatedData } : s));

        toast({
            title: "Student Updated",
            description: `${updatedData.name}'s record has been updated.`,
            variant: "success"
        });

    } catch (error) {
        console.error("Error updating student: ", error);
        toast({
            title: "Error",
            description: "Failed to update student. Please try again.",
            variant: "error"
        });
    }
  };


  const addResult = async (studentId: string, newResultData: Omit<Result, 'id' | 'studentId' | 'fileUrl' | 'date'>) => {
    const resultWithDefaults = {
        ...newResultData,
        fileUrl: '#', 
        date: new Date().toISOString().split('T')[0]
    };
    try {
        const resultRef = await addDoc(collection(db, `students/${studentId}/results`), resultWithDefaults);
        
        setStudents(prev => prev.map(s => {
            if (s.id === studentId) {
                const newResults = [...(s.results || []), { id: resultRef.id, ...resultWithDefaults }];
                return { ...s, results: newResults };
            }
            return s;
        }));

        toast({
            title: "Result Uploaded",
            description: `A new result for ${newResultData.subject} has been added.`,
            variant: "success"
        });
    } catch (error) {
        console.error("Error adding result: ", error);
        toast({
            title: "Error",
            description: "Failed to upload result. Please try again.",
            variant: "error"
        });
    }
  };

  const updateResult = async (studentId: string, resultId: string, updatedResultData: Omit<Result, 'id' | 'studentId' | 'fileUrl' | 'date'>) => {
    try {
        const resultRef = doc(db, `students/${studentId}/results`, resultId);
        await updateDoc(resultRef, updatedResultData);

        setStudents(prev => prev.map(s => {
            if (s.id === studentId) {
                const updatedResults = s.results.map(r => 
                    r.id === resultId ? { ...r, ...updatedResultData } : r
                );
                return { ...s, results: updatedResults };
            }
            return s;
        }));

        toast({
            title: "Result Updated",
            description: `The result for ${updatedResultData.subject} has been updated.`,
            variant: "success"
        });

    } catch (error) {
        console.error("Error updating result: ", error);
        toast({
            title: "Error",
            description: "Failed to update result. Please try again.",
            variant: "error"
        });
    }
  };


  const handleDeleteStudent = async (studentId: string) => {
    try {
        const student = students.find(s => s.id === studentId);

        // Use the comprehensive cleanup function
        const result = await cleanupStudentRecords(studentId, student?.name);

        setStudents(prev => prev.filter(s => s.id !== studentId));
        toast({
            title: "Student Deleted Successfully",
            description: `Student and all related records removed. Payment records: ${result.deletedStudentPayments}, Transactions: ${result.deletedPaymentTransactions}`,
            variant: 'error'
        });
    } catch (error) {
        console.error("Error deleting student: ", error);
        toast({
            title: "Error",
            description: "Failed to delete student. Please try again.",
            variant: "error"
        });
    }
  };

  const columns: ColumnDef<Student>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          disabled={user?.role !== 'admin'}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          disabled={user?.role !== 'admin'}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
          const student = row.original;
          const initials = student.name.split(' ').map(n => n[0]).join('');
          return (
              <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8 border" data-ai-hint="student portrait">
                      <AvatarImage src={student.avatarUrl} alt={student.name} />
                      <AvatarFallback>{initials}</AvatarFallback>
                  </Avatar>
                  <div className="capitalize font-medium">{student.name}</div>
              </div>
          )
      },
    },
    {
      accessorKey: "class",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Class
            <ChevronsUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="lowercase">{row.getValue("class")}</div>,
    },
    {
      accessorKey: "dob",
      header: "Date of Birth",
      cell: ({ row }) => <div>{row.getValue("dob")}</div>,
    },
    {
      accessorKey: "results",
      header: "Results",
      cell: ({ row }) => {
        const results = row.getValue("results") as Student["results"];
        if (!results || results.length === 0) {
          return <Badge variant="secondary">0 terms</Badge>;
        }
        
        // Count unique term combinations (term + year)
        const uniqueTerms = new Set();
        results.forEach((result: any) => {
          uniqueTerms.add(`${result.term}-${result.year}`);
        });
        
        return <Badge variant="secondary">{uniqueTerms.size} terms</Badge>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const student = row.original;
        
        return (
            <AlertDialog>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <StudentDetailsDialog student={student}>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <Info className="mr-2 h-4 w-4" />View details
                        </DropdownMenuItem>
                    </StudentDetailsDialog>
                    <DropdownMenuItem
                        onClick={() => {
                            navigator.clipboard.writeText(student.id)
                            toast({ title: "Copied!", description: "Student ID copied to clipboard."})
                        }}
                        className="mobile-text-sm"
                    >
                        <Copy className="mr-2 h-4 w-4 shrink-0" />
                        Copy student ID
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <UploadResultDialog student={student} onUpload={addResult} onUpdate={updateResult}>
                        <DropdownMenuItem 
                            onSelect={(e) => e.preventDefault()}
                            className="mobile-text-sm"
                        >
                            <FileUp className="mr-2 h-4 w-4 shrink-0" />View/Manage results
                        </DropdownMenuItem>
                    </UploadResultDialog>
                    {user?.role === 'admin' && (
                        <>
                            <DropdownMenuSeparator />
                            <EditStudentDialog student={student} onUpdateStudent={updateStudent}>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="mobile-text-sm">
                                    <Edit className="mr-2 h-4 w-4 shrink-0" />Edit student
                                </DropdownMenuItem>
                            </EditStudentDialog>
                            <AlertDialogTrigger asChild>
                                <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10 mobile-text-sm">
                                    <Trash2 className="mr-2 h-4 w-4 shrink-0" />
                                    Delete student
                                </DropdownMenuItem>
                            </AlertDialogTrigger>
                        </>
                    )}
                    </DropdownMenuContent>
                </DropdownMenu>
                <AlertDialogContent className="mobile-dialog">
                    <AlertDialogHeader>
                    <AlertDialogTitle className="mobile-text-lg lg:text-xl">Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription className="mobile-text-sm">
                        This action cannot be undone. This will permanently delete the student's account
                        and remove their data from our servers.
                    </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="mobile-button-container">
                    <AlertDialogCancel className="mobile-button">Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleDeleteStudent(student.id)} className="mobile-button">
                        Continue
                    </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        );
      },
    },
  ];

  const table = useReactTable({
    data: students,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <Card className="mobile-card-container">
      <CardHeader className="mobile-padding-sm lg:p-6">
        <CardTitle className="mobile-text-lg lg:text-xl">All Students</CardTitle>
        <CardDescription className="mobile-text-sm">Browse and manage student records.</CardDescription>
      </CardHeader>
      <CardContent className="mobile-padding-sm lg:p-6">
      <div className="w-full max-w-full overflow-hidden">
      <div className="flex items-center py-4 flex-col sm:flex-row mobile-gap-base justify-between">
        <Input
          placeholder="Filter students by name..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="w-full sm:max-w-xs mobile-input"
        />
        <div className="flex items-center mobile-gap-sm w-full sm:w-auto justify-between sm:justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="mobile-text-sm mobile-button">
                Columns <ChevronDown className="ml-2 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="mobile-dropdown">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize mobile-text-sm"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border overflow-hidden">
        <div className="mobile-table-container">
          <Table className="mobile-table">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className="mobile-text-sm mobile-table th">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="mobile-text-sm mobile-table td">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center mobile-text-sm"
                  >
                    No students found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4 flex-col sm:flex-row mobile-gap-base">
        <div className="flex-1 text-sm text-muted-foreground text-center sm:text-left mobile-text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="mobile-button-container">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="mobile-button"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="mobile-button"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
      </CardContent>
    </Card>

  );
}
