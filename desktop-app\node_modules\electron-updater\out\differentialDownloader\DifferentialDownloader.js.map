{"version": 3, "file": "DifferentialDownloader.js", "sourceRoot": "", "sources": ["../../src/differentialDownloader/DifferentialDownloader.ts"], "names": [], "mappings": ";;;AAAA,+DAAuJ;AAEvJ,uCAAsC;AACtC,2BAAsC;AAItC,iDAAyC;AACzC,6BAAyB;AACzB,+DAAmF;AACnF,uEAA0G;AAC1G,mHAAiJ;AAgBjJ,MAAsB,sBAAsB;IAK1C,oEAAoE;IACpE,YACqB,kBAAsC,EAChD,YAA+B,EAC/B,OAAsC;QAF5B,uBAAkB,GAAlB,kBAAkB,CAAoB;QAChD,iBAAY,GAAZ,YAAY,CAAmB;QAC/B,YAAO,GAAP,OAAO,CAA+B;QARjD,uBAAkB,GAAkB,IAAI,CAAA;QAUtC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;IAC9B,CAAC;IAED,oBAAoB;QAClB,MAAM,MAAM,GAAG;YACb,OAAO,EAAE;gBACP,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC9B,MAAM,EAAE,KAAK;aACd;SACF,CAAA;QACD,IAAA,0CAAmB,EAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAChD,qDAAqD;QACrD,IAAA,8CAAuB,EAAC,MAAM,CAAC,CAAA;QAC/B,OAAO,MAAM,CAAA;IACf,CAAC;IAES,UAAU,CAAC,WAAqB,EAAE,WAAqB;QAC/D,yIAAyI;QACzI,IAAI,WAAW,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,yBAAyB,WAAW,CAAC,OAAO,MAAM,WAAW,CAAC,OAAO,8BAA8B,CAAC,CAAA;QACtH,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,UAAU,GAAG,IAAA,uCAAiB,EAAC,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,CAAA;QACtE,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,CAAA;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CAAA;YAC9C,IAAI,SAAS,CAAC,IAAI,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;gBAC9C,YAAY,IAAI,MAAM,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,QAAQ,IAAI,MAAM,CAAA;YACpB,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAA;QAC5C,IAAI,YAAY,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE,CAAC;YACjH,MAAM,IAAI,KAAK,CAAC,gDAAgD,YAAY,eAAe,QAAQ,cAAc,OAAO,EAAE,CAAC,CAAA;QAC7H,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,SAAS,WAAW,CAAC,OAAO,CAAC,kBAAkB,WAAW,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;QAExI,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAEO,YAAY,CAAC,KAAuB;QAC1C,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,GAAyB,EAAE;YAC5C,OAAO,OAAO,CAAC,GAAG,CAChB,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBACtB,OAAO,IAAA,gBAAK,EAAC,UAAU,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;oBACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAA;gBACnE,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CACH,CAAA;QACH,CAAC,CAAA;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC;aACtC,IAAI,CAAC,UAAU,CAAC;aAChB,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YAChB,iEAAiE;YACjE,OAAO,UAAU,EAAE;iBAChB,KAAK,CAAC,eAAe,CAAC,EAAE;gBACvB,oDAAoD;gBACpD,IAAI,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,eAAe,EAAE,CAAC,CAAA;gBAC7D,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC;wBACH,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;oBAC3B,CAAC;oBAAC,OAAO,QAAQ,EAAE,CAAC;wBAClB,+BAA+B;oBACjC,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,CAAA;YACT,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACT,MAAM,CAAC,CAAA;YACT,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAuB,EAAE,MAAyB;QAC7E,MAAM,SAAS,GAAG,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QACvD,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;QAClE,MAAM,SAAS,GAAG,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QACvD,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;QAClE,MAAM,OAAO,GAAG,IAAA,sBAAiB,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;QAC1E,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpC,MAAM,OAAO,GAAe,EAAE,CAAA;YAE9B,sDAAsD;YACtD,IAAI,qBAAqB,GAA8D,SAAS,CAAA;YAChG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,yBAAyB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvE,yEAAyE;gBACzE,MAAM,kBAAkB,GAAkB,EAAE,CAAA;gBAC5C,IAAI,eAAe,GAAG,CAAC,CAAA;gBAEvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,IAAI,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;wBACzC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;wBAC9C,eAAe,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;oBAC1C,CAAC;gBACH,CAAC;gBAED,MAAM,gCAAgC,GAAqC;oBACzE,kBAAkB,EAAE,kBAAkB;oBACtC,UAAU,EAAE,eAAe;iBAC5B,CAAA;gBAED,qBAAqB,GAAG,IAAI,6FAA6C,CAAC,gCAAgC,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBACpK,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;YACrC,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,sCAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;YAC3E,0EAA0E;YAC1E,eAAe,CAAC,eAAe,GAAG,KAAK,CAAA;YACvC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YAE7B,yDAAyD;YACzD,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACxB,CAAC;gBAAC,OAAO,CAAC,KAAa,CAAC,GAAG,EAAE;oBAC3B,kDAAkD;oBAClD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;oBACnB,IAAI,CAAC;wBACH,eAAe,CAAC,QAAQ,EAAE,CAAA;oBAC5B,CAAC;oBAAC,OAAO,CAAM,EAAE,CAAC;wBAChB,MAAM,CAAC,CAAC,CAAC,CAAA;wBACT,OAAM;oBACR,CAAC;oBAED,OAAO,CAAC,SAAS,CAAC,CAAA;gBACpB,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAErB,IAAI,UAAU,GAAG,IAAI,CAAA;YACrB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBAC1B,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;oBACvB,UAAU,GAAG,MAAM,CAAA;gBACrB,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACtC,CAAC;YACH,CAAC;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YAE9B,IAAI,CAAM,CAAA;YACV,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;gBAC3C,CAAC,GAAG,IAAA,gEAAsC,EAAC,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;gBACvF,CAAC,CAAC,CAAC,CAAC,CAAA;gBACJ,OAAM;YACR,CAAC;YAED,IAAI,sBAAsB,GAAG,CAAC,CAAA;YAC9B,IAAI,SAAS,GAAkB,IAAI,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;YAEjE,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CACjD;YAAC,cAAsB,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAE5C,CAAC,GAAG,CAAC,KAAa,EAAQ,EAAE;;gBAC1B,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC1B,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC;wBACpC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;oBAC5C,CAAC;oBACD,WAAW,CAAC,GAAG,EAAE,CAAA;oBACjB,OAAM;gBACR,CAAC;gBAED,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;gBAChC,IAAI,SAAS,CAAC,IAAI,KAAK,mCAAa,CAAC,IAAI,EAAE,CAAC;oBAC1C,0DAA0D;oBAC1D,IAAI,qBAAqB,EAAE,CAAC;wBAC1B,qBAAqB,CAAC,aAAa,EAAE,CAAA;oBACvC,CAAC;oBAED,IAAA,uBAAQ,EAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;oBACnE,OAAM;gBACR,CAAC;gBAED,MAAM,KAAK,GAAG,SAAS,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,CAAA;gBAC7D,cAAc,CAAC,OAAQ,CAAC,KAAK,GAAG,KAAK,CAAA;gBAErC,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,mDAAG,mBAAmB,KAAK,EAAE,CAAC,CAAA;gBAEhD,8BAA8B;gBAC9B,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,qBAAqB,CAAC,kBAAkB,EAAE,CAAA;gBAC5C,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE;oBACzE,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBAC5B,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;wBAC1B,MAAM,CAAC,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC,CAAA;oBAC9D,CAAC,CAAC,CAAA;oBACF,6HAA6H;oBAC7H,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;wBAC/B,MAAM,CAAC,IAAA,sCAAe,EAAC,QAAQ,CAAC,CAAC,CAAA;oBACnC,CAAC;oBAED,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;wBACzB,GAAG,EAAE,KAAK;qBACX,CAAC,CAAA;oBACF,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;wBACxB,4CAA4C;wBAC5C,IAAI,qBAAqB,EAAE,CAAC;4BAC1B,qBAAqB,CAAC,gBAAgB,EAAE,CAAA;wBAC1C,CAAC;wBAED,IAAI,EAAE,sBAAsB,KAAK,GAAG,EAAE,CAAC;4BACrC,sBAAsB,GAAG,CAAC,CAAA;4BAC1B,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAA;wBAClC,CAAC;6BAAM,CAAC;4BACN,CAAC,CAAC,KAAK,CAAC,CAAA;wBACV,CAAC;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;gBACF,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,UAAkB,EAAE,MAAc,EAAE,WAAmB,EAAE,EAAE;oBACjF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;oBAC3D,SAAS,GAAG,WAAW,CAAA;oBACvB,IAAA,0CAAmB,EAAC,IAAI,SAAG,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC,CAAA;oBACvD,OAAO,CAAC,cAAc,EAAE,CAAA;gBAC1B,CAAC,CAAC,CAAA;gBACF,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBAC7D,OAAO,CAAC,GAAG,EAAE,CAAA;YACf,CAAC,CAAA;YAED,CAAC,CAAC,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACJ,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,YAAoB;QACjE,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClD,cAAc,CAAC,OAAQ,CAAC,KAAK,GAAG,SAAS,KAAK,IAAI,YAAY,EAAE,CAAA;QAChE,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE;YACzC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;YAC5B,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEF,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,6BAA6B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAA;QAC/F,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,OAAO,CAAC,cAA8B,EAAE,WAAoC;QAClF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE;gBACzE,IAAI,CAAC,IAAA,gDAAsB,EAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;oBAC9C,OAAM;gBACR,CAAC;gBAED,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBAC5B,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;oBAC1B,MAAM,CAAC,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC,CAAA;gBAC9D,CAAC,CAAC,CAAA;gBAEF,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;gBAChC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;YACrC,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAC7D,OAAO,CAAC,GAAG,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AA1RD,wDA0RC;AAED,SAAS,WAAW,CAAC,KAAa,EAAE,MAAM,GAAG,KAAK;IAChD,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAQ,CAAC,GAAG,MAAM,CAAA;AACtF,CAAC;AAED,SAAS;AACT,SAAS,WAAW,CAAC,GAAW;IAC9B,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAC9B,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AAClD,CAAC", "sourcesContent": ["import { BlockMapDataHolder, createHttpError, DigestTransform, HttpExecutor, configureRequestUrl, configureRequestOptions } from \"builder-util-runtime\"\nimport { BlockMap } from \"builder-util-runtime/out/blockMapApi\"\nimport { close, open } from \"fs-extra\"\nimport { createWriteStream } from \"fs\"\nimport { OutgoingHttpHeaders, RequestOptions } from \"http\"\nimport { ProgressInfo, CancellationToken } from \"builder-util-runtime\"\nimport { Logger } from \"../types\"\nimport { copyData } from \"./DataSplitter\"\nimport { URL } from \"url\"\nimport { computeOperations, Operation, OperationKind } from \"./downloadPlanBuilder\"\nimport { checkIsRangesSupported, executeTasksUsingMultipleRangeRequests } from \"./multipleRangeDownloader\"\nimport { ProgressDifferentialDownloadCallbackTransform, ProgressDifferentialDownloadInfo } from \"./ProgressDifferentialDownloadCallbackTransform\"\n\nexport interface DifferentialDownloaderOptions {\n  readonly oldFile: string\n  readonly newUrl: URL\n  readonly logger: Logger\n  readonly newFile: string\n\n  readonly requestHeaders: OutgoingHttpHeaders | null\n\n  readonly isUseMultipleRangeRequest?: boolean\n\n  readonly cancellationToken: CancellationToken\n  onProgress?: (progress: ProgressInfo) => void\n}\n\nexport abstract class DifferentialDownloader {\n  fileMetadataBuffer: Buffer | null = null\n\n  private readonly logger: Logger\n\n  // noinspection TypeScriptAbstractClassConstructorCanBeMadeProtected\n  constructor(\n    protected readonly blockAwareFileInfo: BlockMapDataHolder,\n    readonly httpExecutor: HttpExecutor<any>,\n    readonly options: DifferentialDownloaderOptions\n  ) {\n    this.logger = options.logger\n  }\n\n  createRequestOptions(): RequestOptions {\n    const result = {\n      headers: {\n        ...this.options.requestHeaders,\n        accept: \"*/*\",\n      },\n    }\n    configureRequestUrl(this.options.newUrl, result)\n    // user-agent, cache-control and other common options\n    configureRequestOptions(result)\n    return result\n  }\n\n  protected doDownload(oldBlockMap: BlockMap, newBlockMap: BlockMap): Promise<any> {\n    // we don't check other metadata like compressionMethod - generic check that it is make sense to differentially update is suitable for it\n    if (oldBlockMap.version !== newBlockMap.version) {\n      throw new Error(`version is different (${oldBlockMap.version} - ${newBlockMap.version}), full download is required`)\n    }\n\n    const logger = this.logger\n    const operations = computeOperations(oldBlockMap, newBlockMap, logger)\n    if (logger.debug != null) {\n      logger.debug(JSON.stringify(operations, null, 2))\n    }\n\n    let downloadSize = 0\n    let copySize = 0\n    for (const operation of operations) {\n      const length = operation.end - operation.start\n      if (operation.kind === OperationKind.DOWNLOAD) {\n        downloadSize += length\n      } else {\n        copySize += length\n      }\n    }\n\n    const newSize = this.blockAwareFileInfo.size\n    if (downloadSize + copySize + (this.fileMetadataBuffer == null ? 0 : this.fileMetadataBuffer.length) !== newSize) {\n      throw new Error(`Internal error, size mismatch: downloadSize: ${downloadSize}, copySize: ${copySize}, newSize: ${newSize}`)\n    }\n\n    logger.info(`Full: ${formatBytes(newSize)}, To download: ${formatBytes(downloadSize)} (${Math.round(downloadSize / (newSize / 100))}%)`)\n\n    return this.downloadFile(operations)\n  }\n\n  private downloadFile(tasks: Array<Operation>): Promise<any> {\n    const fdList: Array<OpenedFile> = []\n    const closeFiles = (): Promise<Array<void>> => {\n      return Promise.all(\n        fdList.map(openedFile => {\n          return close(openedFile.descriptor).catch((e: any) => {\n            this.logger.error(`cannot close file \"${openedFile.path}\": ${e}`)\n          })\n        })\n      )\n    }\n    return this.doDownloadFile(tasks, fdList)\n      .then(closeFiles)\n      .catch((e: any) => {\n        // then must be after catch here (since then always throws error)\n        return closeFiles()\n          .catch(closeFilesError => {\n            // closeFiles never throw error, but just to be sure\n            try {\n              this.logger.error(`cannot close files: ${closeFilesError}`)\n            } catch (errorOnLog) {\n              try {\n                console.error(errorOnLog)\n              } catch (_ignored) {\n                // ok, give up and ignore error\n              }\n            }\n            throw e\n          })\n          .then(() => {\n            throw e\n          })\n      })\n  }\n\n  private async doDownloadFile(tasks: Array<Operation>, fdList: Array<OpenedFile>): Promise<any> {\n    const oldFileFd = await open(this.options.oldFile, \"r\")\n    fdList.push({ descriptor: oldFileFd, path: this.options.oldFile })\n    const newFileFd = await open(this.options.newFile, \"w\")\n    fdList.push({ descriptor: newFileFd, path: this.options.newFile })\n    const fileOut = createWriteStream(this.options.newFile, { fd: newFileFd })\n    await new Promise((resolve, reject) => {\n      const streams: Array<any> = []\n\n      // Create our download info transformer if we have one\n      let downloadInfoTransform: ProgressDifferentialDownloadCallbackTransform | undefined = undefined\n      if (!this.options.isUseMultipleRangeRequest && this.options.onProgress) {\n        // TODO: Does not support multiple ranges (someone feel free to PR this!)\n        const expectedByteCounts: Array<number> = []\n        let grandTotalBytes = 0\n\n        for (const task of tasks) {\n          if (task.kind === OperationKind.DOWNLOAD) {\n            expectedByteCounts.push(task.end - task.start)\n            grandTotalBytes += task.end - task.start\n          }\n        }\n\n        const progressDifferentialDownloadInfo: ProgressDifferentialDownloadInfo = {\n          expectedByteCounts: expectedByteCounts,\n          grandTotal: grandTotalBytes,\n        }\n\n        downloadInfoTransform = new ProgressDifferentialDownloadCallbackTransform(progressDifferentialDownloadInfo, this.options.cancellationToken, this.options.onProgress)\n        streams.push(downloadInfoTransform)\n      }\n\n      const digestTransform = new DigestTransform(this.blockAwareFileInfo.sha512)\n      // to simply debug, do manual validation to allow file to be fully written\n      digestTransform.isValidateOnEnd = false\n      streams.push(digestTransform)\n\n      // noinspection JSArrowFunctionCanBeReplacedWithShorthand\n      fileOut.on(\"finish\", () => {\n        ;(fileOut.close as any)(() => {\n          // remove from fd list because closed successfully\n          fdList.splice(1, 1)\n          try {\n            digestTransform.validate()\n          } catch (e: any) {\n            reject(e)\n            return\n          }\n\n          resolve(undefined)\n        })\n      })\n\n      streams.push(fileOut)\n\n      let lastStream = null\n      for (const stream of streams) {\n        stream.on(\"error\", reject)\n        if (lastStream == null) {\n          lastStream = stream\n        } else {\n          lastStream = lastStream.pipe(stream)\n        }\n      }\n\n      const firstStream = streams[0]\n\n      let w: any\n      if (this.options.isUseMultipleRangeRequest) {\n        w = executeTasksUsingMultipleRangeRequests(this, tasks, firstStream, oldFileFd, reject)\n        w(0)\n        return\n      }\n\n      let downloadOperationCount = 0\n      let actualUrl: string | null = null\n      this.logger.info(`Differential download: ${this.options.newUrl}`)\n\n      const requestOptions = this.createRequestOptions()\n      ;(requestOptions as any).redirect = \"manual\"\n\n      w = (index: number): void => {\n        if (index >= tasks.length) {\n          if (this.fileMetadataBuffer != null) {\n            firstStream.write(this.fileMetadataBuffer)\n          }\n          firstStream.end()\n          return\n        }\n\n        const operation = tasks[index++]\n        if (operation.kind === OperationKind.COPY) {\n          // We are copying, let's not send status updates to the UI\n          if (downloadInfoTransform) {\n            downloadInfoTransform.beginFileCopy()\n          }\n\n          copyData(operation, firstStream, oldFileFd, reject, () => w(index))\n          return\n        }\n\n        const range = `bytes=${operation.start}-${operation.end - 1}`\n        requestOptions.headers!.range = range\n\n        this.logger?.debug?.(`download range: ${range}`)\n\n        // We are starting to download\n        if (downloadInfoTransform) {\n          downloadInfoTransform.beginRangeDownload()\n        }\n\n        const request = this.httpExecutor.createRequest(requestOptions, response => {\n          response.on(\"error\", reject)\n          response.on(\"aborted\", () => {\n            reject(new Error(\"response has been aborted by the server\"))\n          })\n          // Electron net handles redirects automatically, our NodeJS test server doesn't use redirects - so, we don't check 3xx codes.\n          if (response.statusCode >= 400) {\n            reject(createHttpError(response))\n          }\n\n          response.pipe(firstStream, {\n            end: false,\n          })\n          response.once(\"end\", () => {\n            // Pass on that we are downloading a segment\n            if (downloadInfoTransform) {\n              downloadInfoTransform.endRangeDownload()\n            }\n\n            if (++downloadOperationCount === 100) {\n              downloadOperationCount = 0\n              setTimeout(() => w(index), 1000)\n            } else {\n              w(index)\n            }\n          })\n        })\n        request.on(\"redirect\", (statusCode: number, method: string, redirectUrl: string) => {\n          this.logger.info(`Redirect to ${removeQuery(redirectUrl)}`)\n          actualUrl = redirectUrl\n          configureRequestUrl(new URL(actualUrl), requestOptions)\n          request.followRedirect()\n        })\n        this.httpExecutor.addErrorAndTimeoutHandlers(request, reject)\n        request.end()\n      }\n\n      w(0)\n    })\n  }\n\n  protected async readRemoteBytes(start: number, endInclusive: number): Promise<Buffer> {\n    const buffer = Buffer.allocUnsafe(endInclusive + 1 - start)\n    const requestOptions = this.createRequestOptions()\n    requestOptions.headers!.range = `bytes=${start}-${endInclusive}`\n    let position = 0\n    await this.request(requestOptions, chunk => {\n      chunk.copy(buffer, position)\n      position += chunk.length\n    })\n\n    if (position !== buffer.length) {\n      throw new Error(`Received data length ${position} is not equal to expected ${buffer.length}`)\n    }\n    return buffer\n  }\n\n  private request(requestOptions: RequestOptions, dataHandler: (chunk: Buffer) => void): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const request = this.httpExecutor.createRequest(requestOptions, response => {\n        if (!checkIsRangesSupported(response, reject)) {\n          return\n        }\n\n        response.on(\"error\", reject)\n        response.on(\"aborted\", () => {\n          reject(new Error(\"response has been aborted by the server\"))\n        })\n\n        response.on(\"data\", dataHandler)\n        response.on(\"end\", () => resolve())\n      })\n      this.httpExecutor.addErrorAndTimeoutHandlers(request, reject)\n      request.end()\n    })\n  }\n}\n\nfunction formatBytes(value: number, symbol = \" KB\"): string {\n  return new Intl.NumberFormat(\"en\").format((value / 1024).toFixed(2) as any) + symbol\n}\n\n// safety\nfunction removeQuery(url: string): string {\n  const index = url.indexOf(\"?\")\n  return index < 0 ? url : url.substring(0, index)\n}\n\ninterface OpenedFile {\n  readonly descriptor: number\n  readonly path: string\n}\n"]}