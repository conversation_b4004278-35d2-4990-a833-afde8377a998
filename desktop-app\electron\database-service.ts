import { ipcMain } from 'electron';

// Try to import MongoDB, handle gracefully if not available
let MongoClient: any = null;
let ObjectId: any = null;
let mongoAvailable = false;

// Initialize MongoDB asynchronously
async function initializeMongoDB() {
  try {
    const mongodb = await import('mongodb');
    MongoClient = mongodb.MongoClient;
    ObjectId = mongodb.ObjectId;
    mongoAvailable = true;
    console.log('✅ MongoDB driver loaded successfully');
  } catch (error) {
    console.warn('⚠️ MongoDB not installed, using fallback mode');
    mongoAvailable = false;
  }
}

// Types
export interface DatabaseConfig {
  mongoUrl: string;
  mongoDatabase: string;
  syncEnabled: boolean;
}

export interface SyncMetadata {
  lastModified: Date;
  source: 'firebase' | 'mongodb';
  syncStatus: 'pending' | 'synced' | 'conflict';
  firebaseId?: string;
  mongoId?: string;
}

export interface DatabaseDocument {
  _id?: any | string; // ObjectId or string
  id?: string;
  syncMeta?: SyncMetadata;
  [key: string]: any;
}

export type ConnectionStatus = 'online' | 'offline' | 'syncing' | 'error';

// Main Process Database Service
export class MainProcessDatabaseService {
  private mongoClient: any = null; // MongoClient or null
  private mongoDb: any = null; // Db or null
  private isConnected: boolean = false;
  private connectionStatus: ConnectionStatus = 'offline';
  private config: DatabaseConfig;
  private syncQueue: Array<{ collection: string; operation: string; data: any; timestamp: Date }> = [];

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.setupIpcHandlers();
    // Initialize MongoDB when the service is created
    initializeMongoDB();
  }

  // Setup IPC handlers for renderer communication
  private setupIpcHandlers(): void {
    // Connection management
    ipcMain.handle('db:connect', async () => {
      return await this.connect();
    });

    ipcMain.handle('db:disconnect', async () => {
      return await this.disconnect();
    });

    ipcMain.handle('db:getStatus', () => {
      return this.getConnectionStatus();
    });

    // CRUD operations
    ipcMain.handle('db:create', async (event, collection: string, data: any) => {
      return await this.create(collection, data);
    });

    ipcMain.handle('db:read', async (event, collection: string, query: any = {}) => {
      return await this.read(collection, query);
    });

    ipcMain.handle('db:update', async (event, collection: string, id: string, data: any) => {
      return await this.update(collection, id, data);
    });

    ipcMain.handle('db:delete', async (event, collection: string, id: string) => {
      return await this.delete(collection, id);
    });

    // Sync operations
    ipcMain.handle('db:getSyncQueue', () => {
      return this.getSyncQueue();
    });

    ipcMain.handle('db:clearSyncQueue', () => {
      return this.clearSyncQueue();
    });

    console.log('✅ Database IPC handlers registered');
  }

  // Connection Management
  async connect(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!mongoAvailable || !MongoClient) {
        console.log('⚠️ MongoDB not available, running in fallback mode');
        this.connectionStatus = 'offline';
        return {
          success: true, // Return success so app doesn't crash
          error: 'MongoDB not installed - using fallback mode'
        };
      }

      console.log('🔄 Connecting to MongoDB...');

      // Connect to MongoDB
      this.mongoClient = new MongoClient(this.config.mongoUrl, {
        serverSelectionTimeoutMS: 5000,
        connectTimeoutMS: 10000,
        maxPoolSize: 10,
      });

      await this.mongoClient.connect();
      this.mongoDb = this.mongoClient.db(this.config.mongoDatabase);

      // Test connection
      await this.mongoDb.admin().ping();

      this.isConnected = true;
      this.connectionStatus = 'offline'; // Start offline, will be updated by network monitor

      console.log('✅ Connected to MongoDB:', this.config.mongoDatabase);

      // Initialize collections if they don't exist
      await this.initializeCollections();

      return { success: true };
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      this.connectionStatus = 'error';
      return {
        success: false,
        error: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  async disconnect(): Promise<{ success: boolean }> {
    if (this.mongoClient) {
      await this.mongoClient.close();
      this.mongoClient = null;
      this.mongoDb = null;
      this.isConnected = false;
      this.connectionStatus = 'offline';
      
      console.log('🔌 Disconnected from MongoDB');
    }
    return { success: true };
  }

  private async initializeCollections(): Promise<void> {
    if (!this.mongoDb) return;

    const collections = ['users', 'students', 'results', 'feeStructures', 'studentPayments', 'subjects'];
    
    for (const collectionName of collections) {
      try {
        // Create collection if it doesn't exist
        const existingCollections = await this.mongoDb.listCollections({ name: collectionName }).toArray();
        
        if (existingCollections.length === 0) {
          await this.mongoDb.createCollection(collectionName);
          console.log(`📁 Created collection: ${collectionName}`);
        }
        
        // Create indexes for better performance
        const collection = this.mongoDb.collection(collectionName);
        await collection.createIndex({ 'syncMeta.lastModified': 1 });
        await collection.createIndex({ 'syncMeta.firebaseId': 1 });
        
      } catch (error) {
        console.warn(`⚠️ Failed to initialize collection ${collectionName}:`, error);
      }
    }
  }

  // Connection Status
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
  }

  isOnline(): boolean {
    return this.connectionStatus === 'online' || this.connectionStatus === 'syncing';
  }

  // CRUD Operations
  async create(collectionName: string, data: any): Promise<{ success: boolean; id?: string; error?: string }> {
    if (!mongoAvailable || !this.mongoDb) {
      return { success: false, error: 'MongoDB not available - please install MongoDB dependencies' };
    }

    try {
      // Add sync metadata
      const document = {
        ...data,
        syncMeta: {
          lastModified: new Date(),
          source: 'mongodb',
          syncStatus: this.isOnline() ? 'pending' : 'pending',
          mongoId: undefined // Will be set after insert
        }
      };

      // Remove any existing _id to let MongoDB generate it
      delete document._id;

      // Insert into MongoDB
      const collection = this.mongoDb.collection(collectionName);
      const result = await collection.insertOne(document);
      
      // Update sync metadata with MongoDB ID
      await collection.updateOne(
        { _id: result.insertedId },
        { $set: { 'syncMeta.mongoId': result.insertedId.toString() } }
      );

      const documentId = result.insertedId.toString();

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'create', { ...document, _id: result.insertedId });
      }

      console.log(`✅ Created document in ${collectionName}:`, documentId);
      return { success: true, id: documentId };

    } catch (error) {
      console.error(`❌ Failed to create document in ${collectionName}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async read(collectionName: string, query: any = {}): Promise<{ success: boolean; data?: any[]; error?: string }> {
    if (!mongoAvailable || !this.mongoDb) {
      return { success: false, error: 'MongoDB not available - please install MongoDB dependencies' };
    }

    try {
      const collection = this.mongoDb.collection(collectionName);
      const documents = await collection.find(query).toArray();
      
      // Convert MongoDB documents to app format
      const data = documents.map(doc => ({
        id: doc._id.toString(),
        ...doc,
        _id: undefined // Remove MongoDB _id from app data
      }));

      return { success: true, data };

    } catch (error) {
      console.error(`❌ Failed to read from ${collectionName}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async update(collectionName: string, id: string, data: any): Promise<{ success: boolean; error?: string }> {
    if (!this.mongoDb) {
      return { success: false, error: 'Database not connected' };
    }

    try {
      const collection = this.mongoDb.collection(collectionName);
      
      // Update sync metadata
      const updateData = {
        ...data,
        'syncMeta.lastModified': new Date(),
        'syncMeta.source': 'mongodb',
        'syncMeta.syncStatus': this.isOnline() ? 'pending' : 'pending'
      };

      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return { success: false, error: `Document with id ${id} not found` };
      }

      // Add to sync queue if online
      if (this.isOnline()) {
        this.addToSyncQueue(collectionName, 'update', { id, ...updateData });
      }

      console.log(`✅ Updated document in ${collectionName}:`, id);
      return { success: true };

    } catch (error) {
      console.error(`❌ Failed to update document in ${collectionName}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async delete(collectionName: string, id: string): Promise<{ success: boolean; error?: string }> {
    if (!this.mongoDb) {
      return { success: false, error: 'Database not connected' };
    }

    try {
      const collection = this.mongoDb.collection(collectionName);
      
      // Get document before deletion for sync queue
      const document = await collection.findOne({ _id: new ObjectId(id) });
      
      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      if (result.deletedCount === 0) {
        return { success: false, error: `Document with id ${id} not found` };
      }

      // Add to sync queue if online
      if (this.isOnline() && document) {
        this.addToSyncQueue(collectionName, 'delete', { id, firebaseId: document.syncMeta?.firebaseId });
      }

      console.log(`✅ Deleted document from ${collectionName}:`, id);
      return { success: true };

    } catch (error) {
      console.error(`❌ Failed to delete document from ${collectionName}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Sync Queue Management
  private addToSyncQueue(collection: string, operation: string, data: any): void {
    this.syncQueue.push({
      collection,
      operation,
      data,
      timestamp: new Date()
    });
  }

  getSyncQueue(): Array<{ collection: string; operation: string; data: any; timestamp: Date }> {
    return [...this.syncQueue];
  }

  clearSyncQueue(): void {
    this.syncQueue = [];
  }
}

// Default configuration
export const defaultDatabaseConfig: DatabaseConfig = {
  mongoUrl: 'mongodb://localhost:27017',
  mongoDatabase: 'maggie_school_offline',
  syncEnabled: true
};

// Global instance
let databaseServiceInstance: MainProcessDatabaseService | null = null;

export function initializeDatabaseService(config?: DatabaseConfig): MainProcessDatabaseService {
  if (!databaseServiceInstance) {
    databaseServiceInstance = new MainProcessDatabaseService(config || defaultDatabaseConfig);
  }
  return databaseServiceInstance;
}

export function getDatabaseService(): MainProcessDatabaseService | null {
  return databaseServiceInstance;
}
